#!/bin/bash

# Check if MongoDB is running
mongo_running=$(pgrep mongod | wc -l)

if [ $mongo_running -eq 0 ]; then
  echo "MongoDB is not running. Starting MongoDB..."
  
  # Create data directory if it doesn't exist
  mkdir -p ./data/db
  
  # Start MongoDB in background
  mongod --dbpath=./data/db &
  
  # Wait for MongoDB to start
  echo "Waiting for MongoDB to start..."
  sleep 5
  
  # Check if MongoDB started successfully
  mongo_running=$(pgrep mongod | wc -l)
  if [ $mongo_running -eq 0 ]; then
    echo "Failed to start MongoDB. Please install MongoDB or start it manually."
    exit 1
  else
    echo "MongoDB started successfully."
  fi
else
  echo "MongoDB is already running."
fi

exit 0 