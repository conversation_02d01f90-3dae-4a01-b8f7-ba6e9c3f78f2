const { execSync } = require('child_process');
const { spawn } = require('child_process');
const path = require('path');

(async function() {
  console.log('Checking if MongoDB is running...');

  try {
    // Try a simple MongoDB connection using mongo CLI
    execSync('mongosh --eval "db.runCommand({ping:1})"', { stdio: 'ignore' });
    console.log('MongoDB is already running');
  } catch (error) {
    console.log('MongoDB is not running. Starting MongoDB with Docker...');
    
    try {
      execSync('docker ps -q -f name=event-planner-mongo', { encoding: 'utf8' });
      console.log('MongoDB container exists, starting it...');
      execSync('docker start event-planner-mongo');
    } catch (error) {
      console.log('Creating new MongoDB container...');
      execSync('docker run --name event-planner-mongo -p 27017:27017 -d mongo:5.0');
    }
    
    console.log('Waiting for MongoDB to initialize...');
    // Wait for MongoDB to be ready
    try {
      // Wait for up to 30 seconds for MongoDB to start
      console.log('Waiting for MongoDB to become available...');
      let attempts = 0;
      const maxAttempts = 10;
      
      const waitForMongo = () => {
        return new Promise((resolve, reject) => {
          const checkMongo = () => {
            attempts++;
            try {
              execSync('docker exec event-planner-mongo mongosh --eval "db.runCommand({ping:1})"', { stdio: 'ignore' });
              console.log('✅ MongoDB is ready!');
              resolve();
            } catch (error) {
              if (attempts >= maxAttempts) {
                console.error('❌ MongoDB failed to start after multiple attempts');
                console.log('Starting application anyway - some features may not work...');
                resolve();
              } else {
                console.log(`Waiting for MongoDB to start (attempt ${attempts}/${maxAttempts})...`);
                setTimeout(checkMongo, 3000);
              }
            }
          };
          
          checkMongo();
        });
      };
      
      await waitForMongo();
    } catch (error) {
      console.error('Error waiting for MongoDB:', error);
      console.log('Starting application anyway...');
    }
  }

  console.log('Starting application...');
  const serverProcess = spawn('npm', ['run', 'dev'], { 
    stdio: 'inherit',
    shell: true
  });

  serverProcess.on('close', (code) => {
    console.log(`Server process exited with code ${code}`);
  });
})(); 