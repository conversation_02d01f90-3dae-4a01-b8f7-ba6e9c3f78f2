/**
 * <PERSON><PERSON>t to repair server files and resolve common issues
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting server repair process...');

// Ensure server/routes directory exists
const routesDir = path.join(__dirname, '../server/routes');
if (!fs.existsSync(routesDir)) {
  console.log('Creating routes directory...');
  fs.mkdirSync(routesDir, { recursive: true });
}

// Fix health routes
console.log('Fixing health routes...');
const healthRoutesContent = `// Simple health check routes
const express = require('express');
const router = express.Router();

// Basic health check
router.get('/', (req, res) => {
  res.json({ status: 'ok', message: 'API is running', time: new Date().toISOString() });
});

// Database health check
router.get('/db', (req, res) => {
  const mongoose = require('mongoose');
  const status = mongoose.connection.readyState;
  
  const stateMap = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  
  res.json({
    status: status === 1 ? 'ok' : 'error',
    dbState: stateMap[status] || 'unknown',
    dbReadyState: status
  });
});

module.exports = router;`;

fs.writeFileSync(path.join(routesDir, 'healthRoutes.js'), healthRoutesContent);
console.log('Health routes fixed!');

// Add a new script to package.json
console.log('Adding repair script to package.json...');
try {
  const packageJsonPath = path.join(__dirname, '../package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  packageJson.scripts = packageJson.scripts || {};
  packageJson.scripts['repair'] = 'node scripts/repair-server.js';
  packageJson.scripts['dev:repair'] = 'npm run repair && npm run dev';
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('Scripts added to package.json');
} catch (error) {
  console.error('Error updating package.json:', error.message);
}

console.log('\nServer repair completed! Run "npm run dev:repair" to start the server with fixed files.'); 