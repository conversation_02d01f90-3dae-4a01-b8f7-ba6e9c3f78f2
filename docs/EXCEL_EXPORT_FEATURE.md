# Excel Export Feature for Daily View

## Overview

The Daily View now supports exporting the current day's tasks to an Excel file. This feature allows users to easily share, analyze, or backup their daily task schedules with rich formatting and multi-line cells.

## How to Use

1. Navigate to the Daily View in the calendar section
2. Select the date you want to export
3. Click the "Export Excel" button in the header toolbar (next to the Groups button)
4. The Excel file will be automatically downloaded with the filename format: `daily-view-timeline-YYYY-MM-DD.xlsx`

## Excel File Structure

The exported Excel file uses a **timeline grid format** that mirrors the Daily View layout:

- **Rows**: Time slots (24 hours from 12:00 AM to 11:00 PM)
- **Columns**: Assignees (including "Unassigned" and group names)
- **Cells**: Task information for what each assignee is doing at that time

### Example Structure:
```
Time    | <PERSON>                           | <PERSON>                    | Unassigned
--------|------------------------------------|------------------------------ |------------------
12:00 AM|                                    |                               | Setup venue (11:00 PM - 1:00 AM)
        |                                    |                               | Type: Setup | Status: In Progress
        |                                    |                               | Location: Main Hall
--------|------------------------------------|------------------------------ |------------------
9:00 AM | Meeting prep (9:00 AM - 11:00 AM) | Review budget (9:00 AM - 10:00 AM) |
        | Type: Planning                     | Type: Finance                 |
--------|------------------------------------|------------------------------ |------------------
10:00 AM| Meeting prep (9:00 AM - 11:00 AM) | Send invites (10:00 AM - 11:00 AM) | Catering setup (10:30 AM - 12:00 PM)
        | Type: Planning                     | Type: Communication          | Type: Catering
        |                                    |                               | Location: Kitchen
```

### Cell Content Format:
Each cell contains task information with **multiple lines**:
- **Line 1**: `Task Name (Start Time - End Time)`
- **Line 2+**: Additional details like `Type: [Type] | Status: [Status]` and `Location: [Location]`
- Multiple tasks in the same time slot are separated by **double line breaks**
- Tasks that span multiple time slots appear in all relevant rows with full timing information

## Features

- **Timeline Grid Layout**: Matches the visual structure of the Daily View with time slots as rows and assignees as columns

- **Multi-line Cells**: Each task is displayed with detailed information across multiple lines within a single cell

- **Rich Formatting**:
  - Header row with bold text and blue background
  - Alternating row colors for better readability
  - Proper cell borders and alignment
  - Auto-sized columns and rows

- **Detailed Task Information**: Each task shows:
  - Task name with start and end times
  - Type, status, and location on separate lines
  - Clear visual separation between multiple tasks

- **Automatic Time Calculation**: Task overlaps with time slots are calculated based on:
  1. Start time + duration (if duration is specified)
  2. Soft deadline (if no duration)
  3. Hard deadline (if no duration or soft deadline)
  4. Default 1-hour duration (if none of the above)

- **Multi-task Handling**: Multiple tasks in the same time slot are separated by double line breaks for clear distinction

- **Group Support**: Assignee groups are properly labeled and exported as separate columns

- **Auto-sizing**: Columns and rows automatically adjust to content size

- **Internationalization**: Button text and tooltips support both English and Chinese translations

- **Date-specific Export**: Only exports tasks for the currently selected date

- **Hourly Granularity**: 24-hour timeline with 1-hour time slots (12:00 AM to 11:00 PM)

## Technical Implementation

The Excel export functionality is implemented directly in the DailyView component with:

- Client-side Excel generation using the `xlsx` library (no server round-trip required)
- Rich formatting with styles, colors, and borders
- Automatic file download using browser APIs
- Responsive UI integration with existing toolbar
- Multi-line cell support with proper text wrapping

## Dependencies

- **xlsx**: JavaScript library for reading and writing Excel files
- Installed via: `npm install xlsx`

## Translation Keys

The feature uses the following translation keys:

- `calendar.dailyView.exportExcel`: Button text
- `calendar.dailyView.exportExcelTooltip`: Tooltip text

## Browser Compatibility

The feature works in all modern browsers that support:
- Blob API
- URL.createObjectURL()
- HTML5 download attribute
- File download functionality

This includes Chrome, Firefox, Safari, and Edge (modern versions).

## File Format

- **Output**: `.xlsx` (Excel 2007+ format)
- **Compatibility**: Opens in Microsoft Excel, Google Sheets, LibreOffice Calc, and other spreadsheet applications
- **Features**: Preserves formatting, multi-line cells, and styling across different applications
