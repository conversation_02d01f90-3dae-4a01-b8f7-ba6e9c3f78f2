# Internationalization (i18n) Guide

This document explains how internationalization is implemented in the Event Planner application.

## Overview

The application supports multiple languages, currently:
- English (en)
- Traditional Chinese (zh-TW)

## Implementation

The application uses a split approach for internationalization:

1. **Client-side**: Uses `react-i18next` with translations stored in JSON files
2. **Server-side**: Uses `i18n` package with translations stored in JSON files

## File Structure

### Client-side Translations

Client-side translations are stored in:
- `client/src/translations/en.json` - English translations
- `client/src/translations/zh-TW.json` - Traditional Chinese translations

These files are imported directly in the `client/src/i18n.js` file and loaded into the i18next instance.

### Server-side Translations

Server-side translations are stored in:
- `server/locales/en.json` - English translations
- `server/locales/zh-TW.json` - Traditional Chinese translations

These files are used by the server to translate error messages, success messages, and other server-generated content.

## Adding New Translations

### Client-side

1. Add the new translation key and value to both language files in `client/src/translations/`
2. Use the key in your React components with the `useTranslation` hook:

```jsx
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();
  
  return <div>{t('myNewKey')}</div>;
};
```

### Server-side

1. Add the new translation key and value to both language files in `server/locales/`
2. Use the key in your server code:

```javascript
// In a route handler
res.json({
  message: req.__('success.myNewKey')
});
```

## Language Detection and Switching

The application detects the user's language preference in the following order:

1. User's preference from database (if authenticated)
2. Query parameter 'lang'
3. Cookie 'lang'
4. Browser's Accept-Language header
5. Default to 'en'

Users can change their language preference using the language selector in the application header.

## Adding a New Language

To add a new language:

1. Create a new translation file in `client/src/translations/` (e.g., `fr.json` for French)
2. Create a new translation file in `server/locales/` (e.g., `fr.json` for French)
3. Update the language detection middleware in `server/middleware/languageMiddleware.js` to include the new language
4. Update the language selector component in `client/src/components/LanguageSelector.js` to include the new language
