# Setting Up Google OAuth for Event Planner

This guide will help you set up Google OAuth for the Event Planner application.

## 1. Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"

## 2. Configure OAuth Consent Screen

1. In the Google Cloud Console, go to "APIs & Services" > "OAuth consent screen"
2. Select the appropriate user type (External or Internal)
3. Fill in the required information:
   - App name: "Event Planner"
   - User support email: Your email address
   - Developer contact information: Your email address
4. Add the following scopes:
   - `./auth/userinfo.email`
   - `./auth/userinfo.profile`
5. Save and continue

## 3. Create OAuth Client ID

1. In the Google Cloud Console, go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Select "Web application" as the application type
4. Name: "Event Planner Web Client"
5. Add authorized JavaScript origins:
   - `http://localhost:3000` (for development)
   - Your production URL if applicable
6. Add authorized redirect URIs:
   - `http://localhost:5001/api/users/auth/google/callback` (for development)
   - Your production callback URL if applicable
7. Click "Create"

## 4. Configure Environment Variables

### Server-side (.env file in server directory)

```
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_CALLBACK_URL=http://localhost:5001/api/users/auth/google/callback
CLIENT_URL=http://localhost:3000
```

### Client-side (.env file in client directory)

```
REACT_APP_GOOGLE_CLIENT_ID=your-client-id
```

## 5. Restart Your Application

After setting up the environment variables, restart both your client and server applications to apply the changes.

## Troubleshooting

### "The OAuth client was not found" Error

This error occurs when:
1. The Google OAuth client ID is incorrect or doesn't exist
2. You haven't set up the REACT_APP_GOOGLE_CLIENT_ID environment variable
3. The OAuth consent screen hasn't been properly configured

Solution:
- Double-check your client ID in the Google Cloud Console
- Make sure you've set the correct environment variables
- Verify that your OAuth consent screen is properly configured

### "Error: redirect_uri_mismatch" Error

This error occurs when the redirect URI used doesn't match any of the authorized redirect URIs configured in the Google Cloud Console.

Solution:
- Add the correct redirect URI to the authorized redirect URIs list in the Google Cloud Console
- Make sure the GOOGLE_CALLBACK_URL environment variable matches one of the authorized redirect URIs
