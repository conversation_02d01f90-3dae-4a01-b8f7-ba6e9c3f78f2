#!/bin/bash

# Update AuthContext imports from ../App
find client/src -type f -name "*.js" -exec sed -i 's/import { AuthContext } from '\''..\/App'\''/import { AuthContext } from '\''..\/contexts\/AuthContext'\''/g' {} \;

# Update EventContext imports from ../App
find client/src -type f -name "*.js" -exec sed -i 's/import { EventContext } from '\''..\/App'\''/import { EventContext } from '\''..\/contexts\/EventContext'\''/g' {} \;

# Update AuthContext imports from ../../App
find client/src -type f -name "*.js" -exec sed -i 's/import { AuthContext } from '\''..\/..\/App'\''/import { AuthContext } from '\''..\/..\/contexts\/AuthContext'\''/g' {} \;

# Update EventContext imports from ../../App
find client/src -type f -name "*.js" -exec sed -i 's/import { EventContext } from '\''..\/..\/App'\''/import { EventContext } from '\''..\/..\/contexts\/EventContext'\''/g' {} \;

# Update combined imports from ../App
find client/src -type f -name "*.js" -exec sed -i 's/import { AuthContext, EventContext } from '\''..\/App'\''/import { AuthContext } from '\''..\/contexts\/AuthContext'\''\nimport { EventContext } from '\''..\/contexts\/EventContext'\''/g' {} \;
find client/src -type f -name "*.js" -exec sed -i 's/import { EventContext, AuthContext } from '\''..\/App'\''/import { AuthContext } from '\''..\/contexts\/AuthContext'\''\nimport { EventContext } from '\''..\/contexts\/EventContext'\''/g' {} \;

# Update combined imports from ../../App
find client/src -type f -name "*.js" -exec sed -i 's/import { AuthContext, EventContext } from '\''..\/..\/App'\''/import { AuthContext } from '\''..\/..\/contexts\/AuthContext'\''\nimport { EventContext } from '\''..\/..\/contexts\/EventContext'\''/g' {} \;
find client/src -type f -name "*.js" -exec sed -i 's/import { EventContext, AuthContext } from '\''..\/..\/App'\''/import { AuthContext } from '\''..\/..\/contexts\/AuthContext'\''\nimport { EventContext } from '\''..\/..\/contexts\/EventContext'\''/g' {} \;
