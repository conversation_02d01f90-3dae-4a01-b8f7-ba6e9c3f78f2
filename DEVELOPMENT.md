# Event Planner - Development Guide

This guide provides instructions for setting up and developing the Event Planner application. For the quickest setup, follow the [Quick Setup](#quick-setup-recommended) section which outlines the 3 simple steps to get your development environment running.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Getting Started](#getting-started)
  - [Clone the Repository](#clone-the-repository)
  - [Environment Setup](#environment-setup)
  - [Installation](#installation)
- [Development Workflow](#development-workflow)
  - [Quick Setup (Recommended)](#quick-setup-recommended)
  - [Alternative Setup Options](#alternative-setup-options)
  - [Database Management](#database-management)
  - [Testing](#testing)
  - [Stopping the Development Environment](#stopping-the-development-environment)
- [Project Structure](#project-structure)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18.20 or later)
- **npm** (v10.7 or later)
- **MongoDB** (v5.0 or later) or **Docker** for containerized MongoDB
- **Git**

## Getting Started

### Clone the Repository

```bash
git clone <repository-url>
cd event-planner
```

### Environment Setup

1. Create environment files for both server and client:

```bash
# Create server environment file
cp server/.env.example server/.env

# Create client environment file
cp client/.env.example client/.env
```

2. Configure the environment variables in both `.env` files:

**Server (.env)**:
```
NODE_ENV=development
PORT=5001
MONGO_URI=mongodb://localhost:27017/event-planner
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRE=30d
```

**Client (.env)**:
```
REACT_APP_API_URL=http://localhost:5001/api
```

### Installation

Install all dependencies for both server and client:

```bash
# Install all dependencies (server and client)
npm run install-all
```

This command will:
- Install server dependencies
- Install client dependencies

## Development Workflow

### Quick Setup (Recommended)

**Follow these 3 simple steps to get started quickly:**

1. **Start MongoDB using Docker:**
   ```bash
   npm run docker-mongo
   ```

2. **Load seed data (this will clear the database):**
   ```bash
   npm run seed
   ```

3. **Start both client and server:**
   ```bash
   npm run dev
   ```

That's it! Your development environment is now running with sample data.

### Alternative Setup Options

#### Option 1: Run with Local MongoDB

If you have MongoDB installed locally:

```bash
# Start the application (server + client)
npm run dev
```

#### Option 2: Run with Docker MongoDB

If you prefer using Docker for MongoDB:

```bash
# Start MongoDB in Docker and run the application
npm run dev:with-db
```

#### Option 3: Run with Database Seeding

To start with fresh sample data in one command:

```bash
# Start the application with database seeding
npm run dev:seed
```

### Database Management

#### Database Initialization

To initialize the database with both schema and seed data:

```bash
cd server
npm run db:init
```

This will:
1. Run Liquibase to set up the database schema
2. Seed the database with initial data

To reset the database (clear and reinitialize):

```bash
cd server
npm run db:reset
```

#### Seeding the Database

To only seed the database with sample data (without schema changes):

```bash
npm run seed
```

#### Database Migrations with Liquibase

The project uses Liquibase for MongoDB to manage database schema changes. This ensures consistent database structure across all environments and provides version control for database changes.

##### Checking Migration Status

To check the status of database migrations:

```bash
cd server
npm run liquibase:status
```

##### Applying Migrations

To apply pending migrations:

```bash
cd server
npm run liquibase:update
```

##### Creating New Migrations

To generate a new migration file:

```bash
cd server
npm run liquibase:generate my_change_description
```

This will create a new XML file in `server/db/changelog/changes/` with a timestamp and your description. Edit this file to define your database changes.

##### Rolling Back Migrations

To roll back the most recent migration:

```bash
cd server
npm run liquibase:rollback
```

To roll back multiple migrations, specify the count:

```bash
cd server
npm run liquibase:rollback 3  # Rolls back 3 migrations
```

##### Configuration

Liquibase behavior can be controlled through environment variables in your `.env` file:

- `AUTO_UPDATE_LIQUIBASE=true` - Automatically apply migrations on server start
- `REQUIRE_MIGRATIONS=true` - Require migrations to be up-to-date before starting
- `SKIP_LIQUIBASE=true` - Skip Liquibase migration checks entirely

#### Docker MongoDB Commands

```bash
# Start MongoDB Docker container
npm run docker-mongo

# Stop MongoDB Docker container
npm run docker-mongo-stop
```

#### Docker Compose Setup

For a more comprehensive Docker setup, you can use Docker Compose:

1. Create a `docker-compose.yml` file in the project root:

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    container_name: event-planner-mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=event-planner
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

volumes:
  mongodb_data:
    name: event-planner-mongodb-data
```

2. Start the MongoDB container using Docker Compose:

```bash
docker-compose up -d
```

3. Stop the MongoDB container:

```bash
docker-compose down
```

### Testing

To test your application, you can use Jest for both frontend and backend testing:

1. For backend testing:

```bash
# Run backend tests
cd server
npm test
```

2. For frontend testing:

```bash
# Run frontend tests
cd client
npm test
```

### Stopping the Development Environment

To stop all running development processes:

```bash
npm run stop-dev
```

## Project Structure

The project follows a client-server architecture:

```
event-planner/
├── client/                 # React frontend
│   ├── public/             # Static files
│   ├── src/                # React source code
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── context/        # React context providers
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API service functions
│   │   ├── utils/          # Utility functions
│   │   ├── App.js          # Main App component
│   │   └── index.js        # Entry point
│   └── package.json        # Client dependencies
│
├── server/                 # Express backend
│   ├── config/             # Configuration files
│   ├── controllers/        # Route controllers
│   ├── data/               # Seed data
│   ├── middleware/         # Express middleware
│   ├── models/             # Mongoose models
│   ├── routes/             # API routes
│   ├── scripts/            # Utility scripts
│   ├── server.js           # Server entry point
│   └── package.json        # Server dependencies
│
├── scripts/                # Project utility scripts
├── package.json            # Project dependencies and scripts
└── README.md               # Project documentation
```

## Troubleshooting

### Common Issues

#### MongoDB Connection Issues

If you encounter MongoDB connection issues:

1. Check if MongoDB is running:
   ```bash
   # For local MongoDB
   mongosh --eval "db.runCommand({ping:1})"

   # For Docker MongoDB
   docker ps | grep event-planner-mongo
   ```

2. Repair server configuration:
   ```bash
   npm run repair
   ```

#### Port Conflicts

If you encounter port conflicts:

1. Change the port in `server/.env`:
   ```
   PORT=5002
   ```

2. Update the client proxy in `client/package.json`:
   ```json
   "proxy": "http://localhost:5002"
   ```

3. Update `REACT_APP_API_URL` in `client/.env`:
   ```
   REACT_APP_API_URL=http://localhost:5002/api
   ```

### Server Repair

If you encounter server issues, run the repair script:

```bash
npm run repair
```

## Additional Resources

- [Main README](./README.md) - Project overview and features
- [API Documentation](./server/README.md) - API endpoints and usage
