#!/bin/bash

# Check if MongoDB is running
echo "Checking MongoDB status..."
if pgrep mongod > /dev/null
then
  echo "MongoDB is already running"
else
  echo "MongoDB is not running. Attempting to start..."
  
  # Try to start MongoDB
  if command -v mongod &> /dev/null
  then
    # MongoDB is installed, start it
    echo "Starting MongoDB..."
    mongod --dbpath=./data/db --fork --logpath=./data/db/mongod.log
    
    if [ $? -eq 0 ]
    then
      echo "MongoDB started successfully."
    else
      echo "Failed to start MongoDB. Please make sure MongoDB is installed correctly."
      
      # Create directory for MongoDB data if it doesn't exist
      mkdir -p ./data/db
      
      echo "Trying again with newly created data directory..."
      mongod --dbpath=./data/db --fork --logpath=./data/db/mongod.log
      
      if [ $? -eq 0 ]
      then
        echo "MongoDB started successfully on second attempt."
      else
        echo "Failed to start MongoDB. Please install MongoDB manually."
      fi
    fi
  else
    echo "MongoDB is not installed. Please install MongoDB first."
    echo "Visit https://www.mongodb.com/try/download/community for instructions."
  fi
fi

# Start the application
echo "Starting the application..."
npm run dev 