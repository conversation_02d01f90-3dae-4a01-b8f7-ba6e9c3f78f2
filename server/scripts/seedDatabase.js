/**
 * <PERSON><PERSON>t to seed the database with initial data
 *
 * This script can be run in two modes:
 * 1. Standalone mode: Directly executed to seed the database
 * 2. Liquibase mode: Called by Liquibase as part of a changeset
 *
 * Usage:
 *   - Direct execution: node seedDatabase.js
 *   - With Liquibase flag: node seedDatabase.js --liquibase
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Event = require('../models/Event');
const Task = require('../models/Task');
const Template = require('../models/Template');
const config = require('../config/config');
const seedData = require('../data/seedData');

// Parse command line arguments
const isLiquibaseMode = process.argv.includes('--liquibase');

// Connect to MongoDB
mongoose.connect(config.mongoURI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// Function to clear the database
const clearDatabase = async () => {
  console.log('Clearing database...');
  await User.deleteMany({});
  await Event.deleteMany({});
  await Task.deleteMany({});
  await Template.deleteMany({});
  console.log('Database cleared.');
};

// Function to seed the database
const seedDatabase = async () => {
  console.log('Seeding database...');

  // Create users
  const users = [];
  for (const userData of seedData.users) {
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(userData.password, salt);

    const user = new User({
      name: userData.name,
      email: userData.email,
      password: hashedPassword,
      role: userData.role
    });

    const savedUser = await user.save();
    users.push(savedUser);
  }
  console.log(`${users.length} users created.`);

  // Create events and link to the first user (admin)
  const events = [];
  for (const eventData of seedData.events) {
    const event = new Event({
      title: eventData.title,
      eventType: eventData.eventType,
      date: eventData.date,
      description: eventData.description,
      venue: eventData.venue,
      budget: eventData.budget,
      owner: users[0]._id
    });

    const savedEvent = await event.save();
    events.push(savedEvent);
  }
  console.log(`${events.length} events created.`);

  // Create tasks and link to events and users
  const eventIds = events.map(event => event._id);
  const userIds = users.map(user => user._id);
  const tasksData = seedData.generateTasks(eventIds, userIds);

  for (const taskData of tasksData) {
    const task = new Task({
      name: taskData.name,
      description: taskData.description,
      taskType: taskData.taskType,
      event: taskData.event,
      startTime: taskData.startTime,
      softDeadline: taskData.softDeadline,
      hardDeadline: taskData.hardDeadline,
      assignees: taskData.assignees,
      status: taskData.status,
      priority: taskData.priority,
      dependencies: taskData.dependencies
    });

    await task.save();
  }
  console.log(`${tasksData.length} tasks created.`);

  // Create templates
  for (const templateData of seedData.templates) {
    const template = new Template({
      title: templateData.title,
      eventType: templateData.eventType,
      description: templateData.description,
      tasks: templateData.tasks,
      creator: users[0]._id
    });

    await template.save();
  }
  console.log(`${seedData.templates.length} templates created.`);

  console.log('Database seeding completed!');
};

// Main function to run seeding
const runSeed = async () => {
  try {
    // In Liquibase mode, we assume the connection is already established
    // and we don't want to disconnect at the end
    if (!isLiquibaseMode) {
      // Connect to MongoDB
      console.log('Connecting to MongoDB for database reset...');
      await mongoose.connect(config.mongoURI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        serverSelectionTimeoutMS: 60000, // 60 second timeout
      });
      console.log('MongoDB connected successfully');
    }

    // Skip clearing the DATABASECHANGELOG and DATABASECHANGELOGLOCK collections in Liquibase mode
    // These collections are managed by Liquibase and should not be cleared
    console.log('Clearing existing data...');
    try {
      const collections = mongoose.connection.collections;

      for (const key in collections) {
        // Skip Liquibase tracking collections when in Liquibase mode
        if (isLiquibaseMode &&
            (key === 'DATABASECHANGELOG' || key === 'DATABASECHANGELOGLOCK')) {
          console.log(`Skipping Liquibase collection: ${key}`);
          continue;
        }

        const collection = collections[key];
        await collection.deleteMany({});
      }
      console.log('Collections cleared successfully');
    } catch (error) {
      console.error('Failed to clear database:', error.message);
      throw error; // Rethrow to exit
    }

    // Seed database with fresh data
    console.log('Seeding database with fresh sample data...');
    await seedDatabase();
    console.log('Database seed process completed successfully!');

    // Only disconnect and exit if not in Liquibase mode
    if (!isLiquibaseMode) {
      await mongoose.disconnect();
      console.log('Disconnected from MongoDB');
      process.exit(0);
    } else {
      console.log('Seed completed in Liquibase mode - keeping connection open');
      // In Liquibase mode, we return instead of exiting
      return;
    }
  } catch (error) {
    console.error('Fatal error during database seed process:', error);

    // Only disconnect if not in Liquibase mode
    if (!isLiquibaseMode) {
      try {
        await mongoose.disconnect();
      } catch (e) {}
      process.exit(1);
    } else {
      // In Liquibase mode, we throw the error instead of exiting
      throw error;
    }
  }
};

// If this script is run directly, execute the seeding
if (require.main === module) {
  runSeed();
} else {
  // If imported as a module, export the functions
  module.exports = {
    clearDatabase,
    seedDatabase
  };
}