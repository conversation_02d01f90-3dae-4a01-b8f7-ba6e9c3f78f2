const http = require('http');
const fs = require('fs');

// Configuration
const API_URL = 'http://localhost:5000/api/debug/db';
const OUTPUT_FILE = 'db-data.json';

// Function to pretty print JSON
function prettyPrintJson(json) {
  return JSON.stringify(json, null, 2);
}

// Function to fetch data from API
function fetchData(url) {
  return new Promise((resolve, reject) => {
    http.get(url, (res) => {
      let data = '';
      
      // A chunk of data has been received
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      // The whole response has been received
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Main function
async function main() {
  try {
    // Get collection name from command line arguments
    const collection = process.argv[2];
    let url = API_URL;
    
    if (collection) {
      url = `${API_URL}/${collection}`;
      console.log(`Fetching ${collection} collection...`);
    } else {
      console.log('Fetching all collections...');
    }
    
    // Fetch data
    const data = await fetchData(url);
    
    // Print to console
    console.log('\n===== DATABASE DATA =====');
    console.log(prettyPrintJson(data));
    console.log('========================');
    
    // Save to file
    fs.writeFileSync(OUTPUT_FILE, prettyPrintJson(data));
    console.log(`\nData saved to ${OUTPUT_FILE}`);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run the main function
main();
