/**
 * Liquibase script for MongoDB database migrations
 * 
 * This script provides a Node.js interface to run Liquibase commands for MongoDB.
 * It supports various operations like update, rollback, status, and validation.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Configuration
const LIQUIBASE_PROPERTIES = path.join(__dirname, '../db/liquibase.properties');
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/event-planner';

// Parse command line arguments
const command = process.argv[2] || 'status';
const additionalArgs = process.argv.slice(3);

// Ensure the liquibase.properties file has the correct MongoDB URI
function updatePropertiesFile() {
  try {
    let content = fs.readFileSync(LIQUIBASE_PROPERTIES, 'utf8');
    const urlRegex = /url=.*/;
    content = content.replace(urlRegex, `url=${MONGODB_URI}`);
    fs.writeFileSync(LIQUIBASE_PROPERTIES, content);
    console.log(`Updated Liquibase properties with MongoDB URI: ${MONGODB_URI}`);
  } catch (error) {
    console.error('Error updating Liquibase properties file:', error.message);
    process.exit(1);
  }
}

// Run Liquibase command
function runLiquibase(args) {
  console.log(`Running Liquibase command: ${args.join(' ')}`);
  
  // Use npx to run liquibase from node_modules
  const liquibase = spawn('npx', ['liquibase', ...args], {
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit'
  });

  return new Promise((resolve, reject) => {
    liquibase.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Liquibase exited with code ${code}`));
      }
    });

    liquibase.on('error', (err) => {
      reject(new Error(`Failed to start Liquibase: ${err.message}`));
    });
  });
}

// Generate a new changeset file
function generateChangeset(name) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '').replace('T', '_').slice(0, 17);
  const filename = `${timestamp}_${name || 'db_change'}.xml`;
  const filePath = path.join(__dirname, '../db/changelog/changes', filename);
  
  const template = `<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:pro="http://www.liquibase.org/xml/ns/pro"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.4.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
        http://www.liquibase.org/xml/ns/pro
        http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.4.xsd">

    <changeSet id="${timestamp}" author="liquibase">
        <!-- Add your changes here -->
        <!-- Examples:
        <ext:createCollection collectionName="newCollection"/>
        
        <ext:createIndex collectionName="collection">
            <ext:keys>{ "field": 1 }</ext:keys>
            <ext:options>{ "unique": true }</ext:options>
        </ext:createIndex>
        
        <ext:insertMany collectionName="collection">
            <ext:documents>
                [
                    { "field": "value1" },
                    { "field": "value2" }
                ]
            </ext:documents>
        </ext:insertMany>
        -->
    </changeSet>

</databaseChangeLog>`;

  fs.writeFileSync(filePath, template);
  
  // Update master changelog to include the new file
  const masterChangelogPath = path.join(__dirname, '../db/changelog/master.xml');
  let masterContent = fs.readFileSync(masterChangelogPath, 'utf8');
  
  // Find the last include tag and add our new one after it
  const lastIncludeIndex = masterContent.lastIndexOf('<include file=');
  if (lastIncludeIndex !== -1) {
    const endOfLineIndex = masterContent.indexOf('/>', lastIncludeIndex) + 2;
    const beforeInclude = masterContent.substring(0, endOfLineIndex);
    const afterInclude = masterContent.substring(endOfLineIndex);
    
    masterContent = beforeInclude + 
      `\n    <include file="db/changelog/changes/${filename}"/>` + 
      afterInclude;
    
    fs.writeFileSync(masterChangelogPath, masterContent);
  }
  
  console.log(`Generated new changeset file: ${filePath}`);
  console.log(`Updated master changelog to include the new file`);
  
  return filePath;
}

// Main function
async function main() {
  try {
    // Update the properties file with the current MongoDB URI
    updatePropertiesFile();
    
    // Handle different commands
    switch (command) {
      case 'update':
        await runLiquibase(['--defaultsFile=db/liquibase.properties', 'update']);
        console.log('Database schema updated successfully');
        break;
        
      case 'rollback':
        const count = additionalArgs[0] || 1;
        await runLiquibase(['--defaultsFile=db/liquibase.properties', 'rollbackCount', count]);
        console.log(`Rolled back ${count} change(s) successfully`);
        break;
        
      case 'status':
        await runLiquibase(['--defaultsFile=db/liquibase.properties', 'status']);
        break;
        
      case 'validate':
        await runLiquibase(['--defaultsFile=db/liquibase.properties', 'validate']);
        console.log('Liquibase changelog is valid');
        break;
        
      case 'generate':
        const name = additionalArgs[0] || '';
        generateChangeset(name);
        break;
        
      default:
        console.error(`Unknown command: ${command}`);
        console.log('Available commands: update, rollback, status, validate, generate');
        process.exit(1);
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Run the main function
main();
