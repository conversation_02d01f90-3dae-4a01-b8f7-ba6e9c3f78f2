/**
 * <PERSON><PERSON>t to view MongoDB data
 */

const mongoose = require('mongoose');
const fs = require('fs');
const config = require('../config/config');

// Import models
const User = require('../models/User');
const Event = require('../models/Event');
const Task = require('../models/Task');
const Template = require('../models/Template');

// Function to pretty print JSON
function prettyPrintJson(json) {
  return JSON.stringify(json, null, 2);
}

// Main function
async function main() {
  try {
    // Get collection name from command line arguments
    const collection = process.argv[2];
    
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(config.mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB connected successfully');
    
    let data = {};
    
    // Fetch data based on collection name
    if (collection) {
      console.log(`Fetching ${collection} collection...`);
      
      switch(collection.toLowerCase()) {
        case 'users':
          // Explicitly include password field which is excluded by default
          data = await User.find().select('+password');
          console.log('\nUser passwords (for verification purposes only):\n');
          data.forEach(user => {
            console.log(`User: ${user.name} (${user.email})\nHashed Password: ${user.password}\n`);
          });
          break;
        case 'events':
          data = await Event.find().populate('owner', 'name email');
          break;
        case 'tasks':
          data = await Task.find().populate('event', 'title').populate('assignee', 'name email');
          break;
        case 'templates':
          data = await Template.find().populate('creator', 'name email');
          break;
        default:
          console.error(`Unknown collection: ${collection}`);
          process.exit(1);
      }
    } else {
      // Fetch all collections
      console.log('Fetching all collections...');
      
      const users = await User.find()//.select('-password');
      const events = await Event.find();
      const tasks = await Task.find();
      const templates = await Template.find();
      
      data = {
        users,
        events,
        tasks,
        templates
      };
    }
    
    // Print to console
    console.log('\n===== DATABASE DATA =====');
    console.log(prettyPrintJson(data));
    console.log('========================');
    
    // Save to file
    const outputFile = 'db-data.json';
    fs.writeFileSync(outputFile, prettyPrintJson(data));
    console.log(`\nData saved to ${outputFile}`);
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Run the main function
main();
