/**
 * <PERSON><PERSON><PERSON> to fix user passwords in the database
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const config = require('../config/config');
const User = require('../models/User');

// Main function to fix passwords
async function fixPasswords() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(config.mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB connected successfully');

    // Find all users
    const users = await User.find();
    console.log(`Found ${users.length} users in the database`);

    // Update passwords
    for (const user of users) {
      // Generate salt
      const salt = await bcrypt.genSalt(10);
      
      // Hash password
      const hashedPassword = await bcrypt.hash('password123', salt);
      
      // Update user password
      user.password = hashedPassword;
      await user.save();
      
      console.log(`Updated password for user: ${user.email}`);
    }

    console.log('All passwords have been updated successfully!');

    // Verify passwords
    for (const user of users) {
      const updatedUser = await User.findById(user._id).select('+password');
      const isMatch = await bcrypt.compare('password123', updatedUser.password);
      console.log(`Verification for ${updatedUser.email}: ${isMatch ? 'SUCCESS' : 'FAILED'}`);
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');

  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Run the main function
fixPasswords();
