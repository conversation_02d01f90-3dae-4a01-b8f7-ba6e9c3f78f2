/**
 * Database Initialization Script
 * 
 * This script initializes the database with both schema and data:
 * 1. Runs Liquibase to set up the schema structure
 * 2. Seeds the database with initial data
 * 
 * Usage:
 *   node initDatabase.js [--force]
 * 
 * Options:
 *   --force: Force reinitialization even if the database already exists
 */

const { spawn } = require('child_process');
const path = require('path');
const mongoose = require('mongoose');
const config = require('../config/config');
const { clearDatabase, seedDatabase } = require('./seedDatabase');

// Parse command line arguments
const forceInit = process.argv.includes('--force');

// Function to run Liquibase update
const runLiquibaseUpdate = () => {
  return new Promise((resolve, reject) => {
    console.log('Running Liquibase update to set up database schema...');
    
    const liquibase = spawn('node', [path.join(__dirname, 'liquibase.js'), 'update'], {
      stdio: 'inherit'
    });
    
    liquibase.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Liquibase update failed with code ${code}`));
      } else {
        console.log('Liquibase update completed successfully');
        resolve();
      }
    });
    
    liquibase.on('error', (err) => {
      reject(new Error(`Failed to start Liquibase: ${err.message}`));
    });
  });
};

// Function to check if database is already initialized
const isDatabaseInitialized = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000
    });
    
    // Check if DATABASECHANGELOG collection exists and has records
    const collections = await mongoose.connection.db.listCollections().toArray();
    const hasChangelogCollection = collections.some(col => col.name === 'DATABASECHANGELOG');
    
    if (hasChangelogCollection) {
      const changelogCount = await mongoose.connection.db.collection('DATABASECHANGELOG').countDocuments();
      return changelogCount > 0;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking database initialization:', error.message);
    return false;
  } finally {
    // Disconnect from MongoDB
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
    }
  }
};

// Main function to initialize the database
const initDatabase = async () => {
  try {
    // Check if database is already initialized
    const isInitialized = await isDatabaseInitialized();
    
    if (isInitialized && !forceInit) {
      console.log('Database is already initialized. Use --force to reinitialize.');
      return;
    }
    
    // Run Liquibase update to set up schema
    await runLiquibaseUpdate();
    
    // Connect to MongoDB for seeding
    console.log('Connecting to MongoDB for data seeding...');
    await mongoose.connect(config.mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    // Clear and seed the database
    if (forceInit) {
      await clearDatabase();
    }
    
    await seedDatabase();
    console.log('Database initialization completed successfully!');
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    
  } catch (error) {
    console.error('Database initialization failed:', error.message);
    
    // Disconnect from MongoDB if connected
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
    }
    
    process.exit(1);
  }
};

// Run the initialization
initDatabase();
