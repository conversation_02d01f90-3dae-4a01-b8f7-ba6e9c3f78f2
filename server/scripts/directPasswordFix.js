/**
 * <PERSON><PERSON><PERSON> to directly fix user passwords in the database
 * This bypasses the User model's pre-save hook
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const config = require('../config/config');

// Main function to fix passwords
async function fixPasswordsDirectly() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(config.mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB connected successfully');

    // Get the User collection directly
    const UserCollection = mongoose.connection.collection('users');
    
    // Generate a consistent password hash
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('password123', salt);
    console.log(`Generated hashed password: ${hashedPassword}`);
    
    // Update all users with the new password
    const result = await UserCollection.updateMany(
      {}, // Match all documents
      { $set: { password: hashedPassword } } // Set the new password
    );
    
    console.log(`Updated ${result.modifiedCount} users with the new password`);
    
    // Verify the update
    const users = await UserCollection.find({}).toArray();
    console.log('\nUsers in database after update:');
    for (const user of users) {
      console.log(`${user.email} - Password: ${user.password}`);
      // Test password verification
      const isMatch = await bcrypt.compare('password123', user.password);
      console.log(`Password verification for ${user.email}: ${isMatch ? 'SUCCESS' : 'FAILED'}`);
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the main function
fixPasswordsDirectly();
