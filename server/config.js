const config = {
  nodeEnv: process.env.NODE_ENV || 'development',
  jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key-dev',
  jwtExpiresIn: process.env.JWT_EXPIRE || '30d',
  port: process.env.PORT || 5001,
  mongoUri: process.env.MONGO_URI || 'mongodb://localhost:27017/event-planner',
  google: {
    clientID: process.env.GOOGLE_CLIENT_ID || 'your-google-client-id',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || 'your-google-client-secret',
    callbackURL: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:5001/api/users/auth/google/callback',
  },
  clientURL: process.env.CLIENT_URL || 'http://localhost:3000',
};

module.exports = config;
