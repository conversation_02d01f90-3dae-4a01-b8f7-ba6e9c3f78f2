/**
 * Seed data for the application
 */

// Sample users
const users = [
  {
    name: "Admin User",
    email: "<EMAIL>",
    password: "password123",
    role: "admin"
  },
  {
    name: "Regular User",
    email: "<EMAIL>",
    password: "password123",
    role: "user"
  },
  {
    name: "Project Manager",
    email: "<EMAIL>",
    password: "password123",
    role: "user"
  },
  {
    name: "Event Coordinator",
    email: "<EMAIL>",
    password: "password123",
    role: "user"
  },
  {
    name: "Marketing Specialist",
    email: "<EMAIL>",
    password: "password123",
    role: "user"
  },
  {
    name: "Technical Support",
    email: "<EMAIL>",
    password: "password123",
    role: "user"
  },
  {
    name: "Finance Manager",
    email: "<EMAIL>",
    password: "password123",
    role: "user"
  },
  {
    name: "Guest Speaker",
    email: "<EMAIL>",
    password: "password123",
    role: "user"
  }
];

// Sample events
const events = [
  {
    title: "Wedding Celebration",
    eventType: "Wedding",
    date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days in future
    description: "A beautiful wedding celebration at Sunset Beach",
    venue: {
      name: "Sunset Beach Resort",
      address: "123 Coastal Highway, Beach City"
    },
    budget: {
      total: 15000,
      currency: "USD"
    }
  },
  {
    title: "Corporate Annual Meeting",
    eventType: "Corporate",
    date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days in future
    description: "Annual company meeting with presentations and team building",
    venue: {
      name: "Grand Conference Center",
      address: "456 Business Ave, Downtown"
    },
    budget: {
      total: 5000,
      currency: "USD"
    }
  },
  {
    title: "Birthday Party",
    eventType: "Birthday",
    date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days in future
    description: "Surprise birthday party with friends and family",
    venue: {
      name: "Home",
      address: "789 Residential St, Hometown"
    },
    budget: {
      total: 500,
      currency: "USD"
    }
  }
];

// Sample templates
const templates = [
  {
    title: "Wedding Planning Template",
    eventType: "Wedding",
    description: "Standard template for wedding planning",
    tasks: [
      {
        name: "Book Venue",
        taskType: "Booking",
        details: "Find and book the perfect venue",
        duration: "02:30:00", // 2.5 hours
        relativeDeadline: -90 // 90 days before event
      },
      {
        name: "Send Invitations",
        taskType: "Communication",
        details: "Design and send invitations to guests",
        duration: "03:45:00", // 3.75 hours
        relativeDeadline: -60 // 60 days before event
      },
      {
        name: "Book Catering",
        taskType: "Booking",
        details: "Arrange food and drinks for the event",
        duration: "01:15:30", // 1 hour, 15 minutes, 30 seconds
        relativeDeadline: -45 // 45 days before event
      },
      {
        name: "Final Guest Count",
        taskType: "Planning",
        details: "Confirm final number of attendees",
        duration: "00:45:00", // 45 minutes
        relativeDeadline: -14 // 14 days before event
      }
    ]
  },
  {
    title: "Birthday Party Template",
    eventType: "Birthday",
    description: "Template for planning birthday parties",
    tasks: [
      {
        name: "Send Invitations",
        taskType: "Communication",
        details: "Invite friends and family",
        duration: "01:00:00", // 1 hour
        relativeDeadline: -21 // 21 days before event
      },
      {
        name: "Order Cake",
        taskType: "Booking",
        details: "Order birthday cake from bakery",
        duration: "00:30:00", // 30 minutes
        relativeDeadline: -7 // 7 days before event
      },
      {
        name: "Buy Decorations",
        taskType: "Shopping",
        details: "Purchase party decorations and supplies",
        duration: "01:30:00", // 1.5 hours
        relativeDeadline: -5 // 5 days before event
      }
    ]
  }
];

// Function to generate sample tasks
const generateTasks = (eventIds, userIds) => {
  const tasks = [];
  const statuses = ["Not Started", "In Progress", "Completed"];
  const priorities = ["Low", "Medium", "High"];

  eventIds.forEach((eventId, eventIndex) => {
    // Generate 3-5 tasks per event
    const numTasks = Math.floor(Math.random() * 3) + 3;

    for (let i = 0; i < numTasks; i++) {
      const eventDate = new Date(events[eventIndex].date);
      const softDeadline = new Date(eventDate);
      softDeadline.setDate(softDeadline.getDate() - (i * 5 + 5)); // Staggered deadlines

      const hardDeadline = new Date(softDeadline);
      hardDeadline.setDate(hardDeadline.getDate() + 2);

      const taskTypes = ['Planning', 'Booking', 'Shopping', 'Communication', 'Setup', 'Cleanup'];
      // Generate a random duration in the format hh:mm:ss
      const hours = Math.floor(Math.random() * 5); // 0-4 hours
      const minutes = Math.floor(Math.random() * 60); // 0-59 minutes
      const seconds = Math.floor(Math.random() * 60); // 0-59 seconds
      const duration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      // Randomly assign 1-3 assignees to each task
      const numAssignees = Math.floor(Math.random() * 3) + 1;
      const taskAssignees = [];

      // Select unique assignees
      for (let j = 0; j < numAssignees; j++) {
        const randomUserId = userIds[Math.floor(Math.random() * userIds.length)];
        if (!taskAssignees.includes(randomUserId)) {
          taskAssignees.push(randomUserId);
        }
      }

      tasks.push({
        name: `Task ${i + 1} for ${events[eventIndex].title}`,
        description: `This is a sample task for the ${events[eventIndex].title} event`,
        taskType: taskTypes[Math.floor(Math.random() * taskTypes.length)],
        event: eventId,
        startTime: new Date(),
        duration: duration, // Add duration in hh:mm:ss format
        softDeadline,
        hardDeadline,
        assignees: taskAssignees,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        dependencies: []
      });
    }
  });

  return tasks;
};

module.exports = {
  users,
  events,
  templates,
  generateTasks
};
