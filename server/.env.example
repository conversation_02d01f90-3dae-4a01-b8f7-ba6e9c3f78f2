# Server Environment Variables

# Node Environment
NODE_ENV=development

# Server Port
PORT=5001

# MongoDB Connection
MONGO_URI=mongodb://localhost:27017/event-planner

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRE=30d

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:5001/api/users/auth/google/callback

# Client URL for redirects
CLIENT_URL=http://localhost:3000

# Liquibase Configuration
# Set to true to automatically apply pending migrations on server start
AUTO_UPDATE_LIQUIBASE=true
# Set to true to require migrations to be up-to-date before starting the server
REQUIRE_MIGRATIONS=false
# Set to true to skip Liquibase migration checks
SKIP_LIQUIBASE=false
