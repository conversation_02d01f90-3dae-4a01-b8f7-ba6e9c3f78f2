# Liquibase for MongoDB - Database Migration System

This directory contains the Liquibase configuration for managing database migrations in the Event Planner application.

## Overview

Liquibase is a database-independent library for tracking, managing, and applying database schema changes. This project uses Liquibase with the MongoDB extension to manage MongoDB schema changes in a controlled, versioned manner.

## Directory Structure

```
db/
├── changelog/                  # Contains all changelog files
│   ├── changes/                # Individual changelog files
│   │   └── initial-schema.xml  # Initial database schema
│   └── master.xml              # Master changelog that includes all other changelogs
├── liquibase.properties        # Liquibase configuration
└── README.md                   # This file
```

## How It Works

1. **Master Changelog**: The `master.xml` file is the entry point for Liquibase. It includes all other changelog files.

2. **Individual Changelogs**: Each change to the database schema is defined in a separate XML file in the `changes/` directory.

3. **Tracking**: Liquibase tracks which changes have been applied in a special collection called `DATABASECHANGELOG` in your MongoDB database.

## Creating New Migrations

To create a new migration:

1. Run the generate command:
   ```bash
   npm run liquibase:generate my_change_description
   ```

2. This creates a new XML file in the `changelog/changes/` directory with a timestamp and your description.

3. Edit the file to define your database changes. For example:

   ```xml
   <changeSet id="20230501123456" author="developer">
       <ext:createCollection collectionName="newCollection">
           <ext:options>
               {
                   "validator": {
                       "$jsonSchema": {
                           "bsonType": "object",
                           "required": ["name"],
                           "properties": {
                               "name": {
                                   "bsonType": "string",
                                   "description": "Name field"
                               }
                           }
                       }
                   }
               }
           </ext:options>
       </ext:createCollection>
   </changeSet>
   ```

## Common MongoDB Operations

### Create a Collection

```xml
<ext:createCollection collectionName="myCollection">
    <ext:options>
        {
            "validator": {
                "$jsonSchema": {
                    "bsonType": "object",
                    "required": ["name"],
                    "properties": {
                        "name": {
                            "bsonType": "string"
                        }
                    }
                }
            }
        }
    </ext:options>
</ext:createCollection>
```

### Create an Index

```xml
<ext:createIndex collectionName="myCollection">
    <ext:keys>
        { "name": 1 }
    </ext:keys>
    <ext:options>
        { "unique": true }
    </ext:options>
</ext:createIndex>
```

### Insert Documents

```xml
<ext:insertMany collectionName="myCollection">
    <ext:documents>
        [
            { "name": "Document 1" },
            { "name": "Document 2" }
        ]
    </ext:documents>
</ext:insertMany>
```

### Update Documents

```xml
<ext:updateMany collectionName="myCollection">
    <ext:filter>
        { "name": "Document 1" }
    </ext:filter>
    <ext:update>
        { "$set": { "updated": true } }
    </ext:update>
</ext:updateMany>
```

### Drop a Collection

```xml
<ext:dropCollection collectionName="myCollection"/>
```

## Available Commands

- **Update Database**: Apply all pending migrations
  ```bash
  npm run liquibase:update
  ```

- **Check Status**: Show which migrations are pending
  ```bash
  npm run liquibase:status
  ```

- **Rollback**: Revert the most recent migration
  ```bash
  npm run liquibase:rollback
  ```

- **Validate**: Verify that the changelog is valid
  ```bash
  npm run liquibase:validate
  ```

## Best Practices

1. **One Change Per File**: Each changelog file should contain a single logical change.

2. **Descriptive IDs**: Use descriptive IDs for your changesets to make it clear what each one does.

3. **Test Migrations**: Always test migrations in a development environment before applying them to production.

4. **Avoid Modifying Applied Changesets**: Once a changeset has been applied to any environment, don't modify it. Create a new changeset instead.

5. **Include Rollback Instructions**: When possible, include rollback instructions in your changesets.

## Resources

- [Liquibase Documentation](https://docs.liquibase.com/)
- [Liquibase MongoDB Extension](https://github.com/liquibase/liquibase-mongodb)
