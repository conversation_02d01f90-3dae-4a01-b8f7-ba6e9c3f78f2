<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:pro="http://www.liquibase.org/xml/ns/pro"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.4.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
        http://www.liquibase.org/xml/ns/pro
        http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.4.xsd">

    <changeSet id="1" author="liquibase">
        <ext:createCollection collectionName="users">
            <ext:options>
                {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["name", "email"],
                            "properties": {
                                "name": {
                                    "bsonType": "string",
                                    "description": "User's name"
                                },
                                "email": {
                                    "bsonType": "string",
                                    "description": "User's email address"
                                },
                                "password": {
                                    "bsonType": "string",
                                    "description": "User's password"
                                },
                                "googleId": {
                                    "bsonType": "string",
                                    "description": "Google ID for OAuth"
                                },
                                "picture": {
                                    "bsonType": "string",
                                    "description": "User's profile picture URL"
                                },
                                "role": {
                                    "bsonType": "string",
                                    "enum": ["user", "admin"],
                                    "description": "User's role"
                                },
                                "language": {
                                    "bsonType": "string",
                                    "enum": ["en", "zh-TW"],
                                    "description": "User's preferred language"
                                }
                            }
                        }
                    }
                }
            </ext:options>
        </ext:createCollection>
    </changeSet>

    <changeSet id="2" author="liquibase">
        <ext:createCollection collectionName="events">
            <ext:options>
                {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["title", "eventType", "date", "owner"],
                            "properties": {
                                "title": {
                                    "bsonType": "string",
                                    "description": "Event title"
                                },
                                "eventType": {
                                    "bsonType": "string",
                                    "enum": ["Wedding", "Birthday", "Corporate", "Anniversary", "Other"],
                                    "description": "Type of event"
                                },
                                "date": {
                                    "bsonType": "date",
                                    "description": "Event date"
                                },
                                "description": {
                                    "bsonType": "string",
                                    "description": "Event description"
                                },
                                "venue": {
                                    "bsonType": "object",
                                    "properties": {
                                        "name": {
                                            "bsonType": "string",
                                            "description": "Venue name"
                                        },
                                        "address": {
                                            "bsonType": "string",
                                            "description": "Venue address"
                                        }
                                    }
                                },
                                "budget": {
                                    "bsonType": "object",
                                    "properties": {
                                        "total": {
                                            "bsonType": "number",
                                            "description": "Total budget amount"
                                        },
                                        "currency": {
                                            "bsonType": "string",
                                            "description": "Budget currency"
                                        }
                                    }
                                },
                                "owner": {
                                    "bsonType": "objectId",
                                    "description": "Event owner reference"
                                },
                                "collaborators": {
                                    "bsonType": "array",
                                    "items": {
                                        "bsonType": "object",
                                        "properties": {
                                            "user": {
                                                "bsonType": "objectId",
                                                "description": "Collaborator user reference"
                                            },
                                            "role": {
                                                "bsonType": "string",
                                                "enum": ["Admin", "Editor", "Viewer"],
                                                "description": "Collaborator role"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            </ext:options>
        </ext:createCollection>
    </changeSet>

    <changeSet id="3" author="liquibase">
        <ext:createCollection collectionName="tasks">
            <ext:options>
                {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["name", "taskType", "event"],
                            "properties": {
                                "name": {
                                    "bsonType": "string",
                                    "description": "Task name"
                                },
                                "taskType": {
                                    "bsonType": "string",
                                    "description": "Type of task"
                                },
                                "startTime": {
                                    "bsonType": "date",
                                    "description": "Task start time"
                                },
                                "duration": {
                                    "bsonType": "string",
                                    "description": "Task duration"
                                },
                                "location": {
                                    "bsonType": "string",
                                    "description": "Task location"
                                },
                                "details": {
                                    "bsonType": "string",
                                    "description": "Task details"
                                },
                                "cost": {
                                    "bsonType": "object",
                                    "properties": {
                                        "amount": {
                                            "bsonType": "number",
                                            "description": "Cost amount"
                                        },
                                        "currency": {
                                            "bsonType": "string",
                                            "description": "Cost currency"
                                        },
                                        "isPaid": {
                                            "bsonType": "bool",
                                            "description": "Payment status"
                                        }
                                    }
                                },
                                "budgetItems": {
                                    "bsonType": "array",
                                    "items": {
                                        "bsonType": "object",
                                        "properties": {
                                            "description": {
                                                "bsonType": "string",
                                                "description": "Budget item description"
                                            },
                                            "estimatedAmount": {
                                                "bsonType": "number",
                                                "description": "Estimated amount"
                                            },
                                            "actualAmount": {
                                                "bsonType": "number",
                                                "description": "Actual amount"
                                            },
                                            "currency": {
                                                "bsonType": "string",
                                                "description": "Currency"
                                            },
                                            "isPaid": {
                                                "bsonType": "bool",
                                                "description": "Payment status"
                                            }
                                        }
                                    }
                                },
                                "assignees": {
                                    "bsonType": "array",
                                    "items": {
                                        "bsonType": "objectId",
                                        "description": "Assignee user reference"
                                    }
                                },
                                "softDeadline": {
                                    "bsonType": "date",
                                    "description": "Soft deadline"
                                },
                                "hardDeadline": {
                                    "bsonType": "date",
                                    "description": "Hard deadline"
                                },
                                "dependencies": {
                                    "bsonType": "array",
                                    "items": {
                                        "bsonType": "string",
                                        "description": "Dependent task ID"
                                    }
                                },
                                "subtasks": {
                                    "bsonType": "array",
                                    "items": {
                                        "bsonType": "objectId",
                                        "description": "Subtask reference"
                                    }
                                },
                                "parentTask": {
                                    "bsonType": "objectId",
                                    "description": "Parent task reference"
                                },
                                "status": {
                                    "bsonType": "string",
                                    "enum": ["Not Started", "In Progress", "Completed", "Delayed", "Cancelled"],
                                    "description": "Task status"
                                },
                                "event": {
                                    "bsonType": "objectId",
                                    "description": "Event reference"
                                }
                            }
                        }
                    }
                }
            </ext:options>
        </ext:createCollection>
    </changeSet>

    <changeSet id="4" author="liquibase">
        <ext:createCollection collectionName="templates">
            <ext:options>
                {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["title", "eventType"],
                            "properties": {
                                "title": {
                                    "bsonType": "string",
                                    "description": "Template title"
                                },
                                "eventType": {
                                    "bsonType": "string",
                                    "enum": ["Wedding", "Birthday", "Corporate", "Anniversary", "Other"],
                                    "description": "Type of event"
                                },
                                "description": {
                                    "bsonType": "string",
                                    "description": "Template description"
                                },
                                "tasks": {
                                    "bsonType": "array",
                                    "items": {
                                        "bsonType": "object",
                                        "properties": {
                                            "name": {
                                                "bsonType": "string",
                                                "description": "Task name"
                                            },
                                            "taskType": {
                                                "bsonType": "string",
                                                "description": "Type of task"
                                            },
                                            "details": {
                                                "bsonType": "string",
                                                "description": "Task details"
                                            },
                                            "relativeDeadline": {
                                                "bsonType": "int",
                                                "description": "Relative deadline in days"
                                            }
                                        }
                                    }
                                },
                                "creator": {
                                    "bsonType": "objectId",
                                    "description": "Creator user reference"
                                },
                                "isDefault": {
                                    "bsonType": "bool",
                                    "description": "Whether this is a default template"
                                }
                            }
                        }
                    }
                }
            </ext:options>
        </ext:createCollection>
    </changeSet>

    <changeSet id="5" author="liquibase">
        <ext:createIndex collectionName="users">
            <ext:keys>
                { "email": 1 }
            </ext:keys>
            <ext:options>
                { "unique": true }
            </ext:options>
        </ext:createIndex>
    </changeSet>

</databaseChangeLog>
