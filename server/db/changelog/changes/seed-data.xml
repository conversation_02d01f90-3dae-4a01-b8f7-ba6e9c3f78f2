<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:pro="http://www.liquibase.org/xml/ns/pro"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.4.xsd
        http://www.liquibase.org/xml/ns/dbchangelog-ext
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
        http://www.liquibase.org/xml/ns/pro
        http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.4.xsd">

    <!-- This changeset is for importing seed data -->
    <changeSet id="seed-data-1" author="liquibase" runOnChange="true" runAlways="false">
        <!-- This changeset will only run once unless the content changes -->
        <comment>Import initial seed data for the application</comment>
        
        <!-- This custom tag will trigger our JavaScript-based seed data import -->
        <ext:runCommand>
            <ext:command>node ${project.basedir}/scripts/seedDatabase.js --liquibase</ext:command>
        </ext:runCommand>
        
        <!-- Add a tag to mark this point in the changelog -->
        <tagDatabase tag="seed-data-imported"/>
    </changeSet>

</databaseChangeLog>
