const i18n = require('i18n');
const path = require('path');

// Configure i18n
i18n.configure({
  // Path to locales
  locales: ['en', 'zh-TW'],
  // Default locale
  defaultLocale: 'en',
  // Directory where locales are stored
  directory: path.join(__dirname, '../locales'),
  // Auto reload locales
  autoReload: true,
  // Update files when new keys are detected
  updateFiles: false,
  // Sync locales
  syncFiles: false,
  // Cookie name for storing locale
  cookie: 'lang',
  // Query parameter to switch locale
  queryParameter: 'lang',
  // Object notation for accessing nested properties
  objectNotation: true,
  // API mode
  api: {
    // Set to true to send language as a response header
    __: true,
    __n: true,
    __l: true,
    __h: true,
    __mf: true
  }
});

module.exports = i18n;
