/**
 * Server configuration settings
 */
require('dotenv').config();

const config = {
  // Database configuration
  mongoURI: process.env.MONGODB_URI || 'mongodb://localhost:27017/event-planner',
  
  // JWT configuration
  jwtSecret: process.env.JWT_SECRET || 'your_development_jwt_secret',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1d',
  
  // Server configuration
  port: process.env.PORT || 5000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // Cors configuration
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  
  // File upload configuration
  uploadPath: process.env.UPLOAD_PATH || 'uploads/',
  maxFileSize: process.env.MAX_FILE_SIZE || 5 * 1024 * 1024, // 5MB
  
  // Logging
  loggingFormat: process.env.NODE_ENV === 'production' ? 'combined' : 'dev',
  
  // Debug mode
  debug: process.env.DEBUG === 'true',

  stripe: {
    secretKey: process.env.STRIPE_SECRETE_KEY || 'TEST_KEY'
  }
};

// Validate critical config values
if (!config.mongoURI) {
  console.error('MongoDB URI is not defined! Check your environment variables.');
  process.exit(1);
}

if (!config.jwtSecret) {
  console.warn('WARNING: JWT secret is not defined! Using default development secret.');
}

console.log(`Configuration loaded for environment: ${config.nodeEnv}`);

module.exports = config; 