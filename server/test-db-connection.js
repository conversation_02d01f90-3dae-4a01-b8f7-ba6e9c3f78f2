const mongoose = require('mongoose');
const config = require('./config/config');

console.log('Attempting to connect to MongoDB at:', config.mongoURI);

mongoose.connect(config.mongoURI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  console.log('✓ MongoDB Connected Successfully');
  process.exit(0);
})
.catch(err => {
  console.error('✗ MongoDB Connection Error:', err);
  process.exit(1);
}); 