const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const eventController = require('../controllers/eventController');

// @route   GET api/events
// @desc    Get all events
// @access  Private
router.get('/', auth, eventController.getEvents);

// @route   GET api/events/:id
// @desc    Get event by ID
// @access  Private
router.get('/:id', auth, eventController.getEventById);

// @route   POST api/events
// @desc    Create a new event
// @access  Private
router.post('/', auth, eventController.createEvent);

// @route   PUT api/events/:id
// @desc    Update an event
// @access  Private
router.put('/:id', auth, eventController.updateEvent);

// @route   DELETE api/events/:id
// @desc    Delete an event
// @access  Private
router.delete('/:id', auth, eventController.deleteEvent);

module.exports = router; 