const express = require('express');
const router = express.Router();

// @desc    Get all data from in-memory database
// @route   GET /api/debug/db
// @access  Public (only in development mode)
router.get('/db', (req, res) => {
  // Only allow in development mode
  if (process.env.NODE_ENV !== 'development') {
    return res.status(403).json({ message: 'Debug routes only available in development mode' });
  }

  // Check if in-memory database exists
  if (!req.app.locals.db) {
    return res.status(404).json({ message: 'In-memory database not found' });
  }

  // Return the entire in-memory database
  res.json(req.app.locals.db);
});

// @desc    Get specific collection from in-memory database
// @route   GET /api/debug/db/:collection
// @access  Public (only in development mode)
router.get('/db/:collection', (req, res) => {
  // Only allow in development mode
  if (process.env.NODE_ENV !== 'development') {
    return res.status(403).json({ message: 'Debug routes only available in development mode' });
  }

  const { collection } = req.params;

  // Check if in-memory database exists
  if (!req.app.locals.db) {
    return res.status(404).json({ message: 'In-memory database not found' });
  }

  // Check if collection exists
  if (!req.app.locals.db[collection]) {
    return res.status(404).json({ message: `Collection '${collection}' not found` });
  }

  // Return the requested collection
  res.json(req.app.locals.db[collection]);
});

module.exports = router;
