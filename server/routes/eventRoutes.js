const express = require('express');
const router = express.Router();
const { 
  createEvent, 
  getEvents, 
  getEventById, 
  updateEvent, 
  deleteEvent, 
  addCollaborator, 
  removeCollaborator, 
  getEventTasks,
  inviteUser,
  respondToInvitation,
  getEventInvitations,
  getUserPendingInvitations,
  cancelInvitation
} = require('../controllers/eventController');
const { protect } = require('../middleware/authMiddleware');

// Event routes
router.route('/')
  .post(protect, createEvent)
  .get(protect, getEvents);

router.route('/:id')
  .get(protect, getEventById)
  .put(protect, updateEvent)
  .delete(protect, deleteEvent);

// Collaborator routes
router.route('/:id/collaborators')
  .post(protect, addCollaborator);

router.route('/:id/collaborators/:userId')
  .delete(protect, removeCollaborator);

// Invitation routes
router.route('/:id/invitations')
  .post(protect, inviteUser)
  .get(protect, getEventInvitations);

router.route('/:id/invitations/:invitationId')
  .put(protect, respondToInvitation)
  .delete(protect, cancelInvitation);

// Get pending invitations for current user
router.route('/invitations/pending')
  .get(protect, getUserPendingInvitations);

// Task routes
router.route('/:id/tasks')
  .get(protect, getEventTasks);

module.exports = router; 