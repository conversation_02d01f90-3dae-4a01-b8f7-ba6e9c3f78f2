const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { protect } = require('../middleware/authMiddleware');

// @desc    Get current language
// @route   GET /api/language
// @access  Public
router.get('/', (req, res) => {
  res.json({
    language: req.getLocale(),
    availableLanguages: req.getLocales()
  });
});

// @desc    Set language for guest (cookie-based)
// @route   POST /api/language
// @access  Public
router.post('/', (req, res) => {
  const { language } = req.body;

  if (!language || !['en', 'zh-TW'].includes(language)) {
    return res.status(400).json({
      message: 'Language is required and must be one of: en, zh-TW'
    });
  }

  try {
    // Set language for this request
    req.setLocale(language);

    // Set cookie for future requests
    const expirationDate = new Date();
    expirationDate.setFullYear(expirationDate.getFullYear() + 1); // 1 year from now
    res.cookie('lang', language, {
      expires: expirationDate,
      httpOnly: true
    });

    // Try to use i18n translation if available
    let message = 'Language updated successfully';
    try {
      if (typeof req.__ === 'function') {
        message = req.__('success.language.updated');
      }
    } catch (translationError) {
      console.error('Translation error:', translationError);
      // Use default message if translation fails
    }

    res.json({
      message,
      language: req.getLocale()
    });
  } catch (error) {
    console.error('Error setting language:', error);
    res.status(500).json({
      message: 'Failed to update language',
      error: error.message
    });
  }
});

// @desc    Set language for authenticated user (persisted in database)
// @route   POST /api/language/user
// @access  Private
router.post('/user', protect, async (req, res) => {
  try {
    const { language } = req.body;

    if (!language || !['en', 'zh-TW'].includes(language)) {
      return res.status(400).json({
        message: 'Language is required and must be one of: en, zh-TW'
      });
    }

    // Update user's language preference in the database
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.language = language;
    await user.save();

    // Set language for this request
    req.setLocale(language);

    // Set cookie for future requests
    const expirationDate = new Date();
    expirationDate.setFullYear(expirationDate.getFullYear() + 1); // 1 year from now
    res.cookie('lang', language, {
      expires: expirationDate,
      httpOnly: true
    });

    // Try to use i18n translation if available
    let message = 'Language updated successfully';
    try {
      if (typeof req.__ === 'function') {
        message = req.__('success.language.updated');
      }
    } catch (translationError) {
      console.error('Translation error:', translationError);
      // Use default message if translation fails
    }

    res.json({
      message,
      language: req.getLocale()
    });
  } catch (error) {
    console.error('Error updating user language:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error.message
    });
  }
});

module.exports = router;
