const express = require('express');
const guestRoutes = require('./resources/guestRoutes');
const venueRoutes = require('./resources/venueRoutes');
const seatRoutes = require('./resources/seatRoutes');
const stakeholderRoutes = require('./resources/stakeholderRoutes');
const supplyRoutes = require('./resources/supplyRoutes');

const router = express.Router();

console.log('Setting up resource routes...');

// Mount resource-specific routes
router.use('/guests', guestRoutes);
console.log('Guest routes mounted at /api/resources/guests');

router.use('/venues', venueRoutes);
console.log('Venue routes mounted at /api/resources/venues');

router.use('/seats', seatRoutes);
console.log('Seat routes mounted at /api/resources/seats');

router.use('/stakeholders', stakeholderRoutes);
console.log('Stakeholder routes mounted at /api/resources/stakeholders');

router.use('/supplies', supplyRoutes);
console.log('Supply routes mounted at /api/resources/supplies');

// Add a test route to verify the router is working
router.get('/test', (req, res) => {
  console.log('Resource test route accessed');
  res.json({ message: 'Resource routes are working' });
});

module.exports = router;
