const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');

// Basic health check
router.get('/', (req, res) => {
  res.json({ status: 'ok', message: 'API is running', time: new Date().toISOString() });
});

// Database health check
router.get('/db', (req, res) => {
  const status = mongoose.connection.readyState;
  const stateMap = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  res.json({
    status: status === 1 ? 'ok' : 'error',
    dbState: stateMap[status] || 'unknown',
    dbReadyState: status
  });
});

module.exports = router;