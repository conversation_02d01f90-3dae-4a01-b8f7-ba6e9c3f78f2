const express = require('express');
const router = express.Router();
const {
  getTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate
} = require('../controllers/templateController');
const { protect, admin } = require('../middleware/authMiddleware');

router.route('/')
  .get(protect, getTemplates)
  .post(protect, createTemplate);

router.route('/:id')
  .get(protect, getTemplateById)
  .put(protect, updateTemplate)
  .delete(protect, deleteTemplate);

module.exports = router; 