const express = require('express');
const router = express.Router();
const { 
  getSubscriptionPlans,
  getCurrentSubscription,
  createCheckoutSession,
  handleStripeWebhook,
  cancelSubscription
} = require('../controllers/paymentController');
const { protect } = require('../middleware/authMiddleware');

// Public routes
router.get('/plans', getSubscriptionPlans);

// Special route for Stripe webhooks - no auth, needs raw body
router.post('/webhook', express.raw({ type: 'application/json' }), handleStripeWebhook);

// Protected routes - require user authentication
router.get('/subscription', protect, getCurrentSubscription);
router.post('/create-checkout-session', protect, createCheckoutSession);
router.post('/cancel-subscription', protect, cancelSubscription);

module.exports = router; 