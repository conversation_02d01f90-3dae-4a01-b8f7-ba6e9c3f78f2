const express = require('express');
const { guestController } = require('../../controllers/resources');
const { protect } = require('../../middleware/authMiddleware');

const router = express.Router();

console.log('Setting up guest routes...');

// Add a test route to verify the router is working without auth
router.get('/test', (req, res) => {
  console.log('Guest test route accessed');
  res.json({ message: 'Guest routes are working' });
});

// Add a test POST route to verify the router is working without auth
router.post('/test-create', (req, res) => {
  console.log('Guest test create route accessed with body:', req.body);
  res.status(201).json({
    message: 'Guest test create route is working',
    receivedData: req.body
  });
});

// Add a temporary route for creating guests without authentication (for development only)
router.post('/dev-create', async (req, res) => {
  console.log('Dev create guest route accessed with body:', req.body);
  try {
    // Set a mock user for development
    req.user = {
      _id: '6123456789abcdef01234567',
      name: 'Dev User',
      email: '<EMAIL>'
    };

    // Call the controller function directly
    await guestController.createGuest(req, res);
  } catch (error) {
    console.error('Error in dev-create route:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Add a temporary route for updating guests without authentication (for development only)
router.put('/dev-update/:id', async (req, res) => {
  console.log('Dev update guest route accessed with body:', req.body);
  try {
    // Set a mock user for development
    req.user = {
      _id: '6123456789abcdef01234567',
      name: 'Dev User',
      email: '<EMAIL>'
    };

    // Call the controller function directly
    await guestController.updateGuest(req, res);
  } catch (error) {
    console.error('Error in dev-update route:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Add a temporary route for deleting guests without authentication (for development only)
router.delete('/dev-delete/:id', async (req, res) => {
  console.log('Dev delete guest route accessed for ID:', req.params.id);
  try {
    // Set a mock user for development
    req.user = {
      _id: '6123456789abcdef01234567',
      name: 'Dev User',
      email: '<EMAIL>'
    };

    // Call the controller function directly
    await guestController.deleteGuest(req, res);
  } catch (error) {
    console.error('Error in dev-delete route:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Apply authentication middleware to all other routes
router.use(protect);
console.log('Authentication middleware applied to guest routes (except test routes)');

router.route('/')
  .get(guestController.getGuests)
  .post(guestController.createGuest);
console.log('GET and POST routes set up at /api/resources/guests');

router.route('/import')
  .post(guestController.importGuests);
console.log('POST route set up at /api/resources/guests/import');

router.route('/:id')
  .get(guestController.getGuestById)
  .put(guestController.updateGuest)
  .delete(guestController.deleteGuest);
console.log('GET, PUT, DELETE routes set up at /api/resources/guests/:id');

// Log all routes in this router
console.log('Guest routes:');
router.stack.forEach(r => {
  if (r.route && r.route.path) {
    const methods = Object.keys(r.route.methods).map(m => m.toUpperCase()).join(', ');
    console.log(`${methods} /api/resources/guests${r.route.path}`);
  }
});

module.exports = router;
