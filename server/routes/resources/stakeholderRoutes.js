const express = require('express');
const { stakeholderController } = require('../../controllers/resources');
const { protect } = require('../../middleware/authMiddleware');

const router = express.Router();

console.log('Setting up stakeholder routes...');

// Add a test route to verify the router is working without auth
router.get('/test', (req, res) => {
  console.log('Stakeholder test route accessed');
  res.json({ message: 'Stakeholder routes are working' });
});

// Add a test POST route to verify the router is working without auth
router.post('/test-create', (req, res) => {
  console.log('Stakeholder test create route accessed with body:', req.body);
  res.status(201).json({
    message: 'Stakeholder test create route is working',
    receivedData: req.body
  });
});

// Add a temporary route for creating stakeholders without authentication (for development only)
router.post('/dev-create', async (req, res) => {
  console.log('Dev create stakeholder route accessed with body:', req.body);
  try {
    // Set a mock user for development
    req.user = {
      _id: '6123456789abcdef01234567',
      name: 'Dev User',
      email: '<EMAIL>'
    };

    // Call the controller function directly
    await stakeholderController.createStakeholder(req, res);
  } catch (error) {
    console.error('Error in dev-create route:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Add a temporary route for updating stakeholders without authentication (for development only)
router.put('/dev-update/:id', async (req, res) => {
  console.log('Dev update stakeholder route accessed with body:', req.body);
  try {
    // Set a mock user for development
    req.user = {
      _id: '6123456789abcdef01234567',
      name: 'Dev User',
      email: '<EMAIL>'
    };

    // Call the controller function directly
    await stakeholderController.updateStakeholder(req, res);
  } catch (error) {
    console.error('Error in dev-update route:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Add a temporary route for deleting stakeholders without authentication (for development only)
router.delete('/dev-delete/:id', async (req, res) => {
  console.log('Dev delete stakeholder route accessed for ID:', req.params.id);
  try {
    // Set a mock user for development
    req.user = {
      _id: '6123456789abcdef01234567',
      name: 'Dev User',
      email: '<EMAIL>'
    };

    // Call the controller function directly
    await stakeholderController.deleteStakeholder(req, res);
  } catch (error) {
    console.error('Error in dev-delete route:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Apply authentication middleware to all other routes
router.use(protect);
console.log('Authentication middleware applied to stakeholder routes (except test routes)');

router.route('/')
  .get(stakeholderController.getStakeholders)
  .post(stakeholderController.createStakeholder);
console.log('GET and POST routes set up at /api/resources/stakeholders');

router.route('/:id')
  .get(stakeholderController.getStakeholderById)
  .put(stakeholderController.updateStakeholder)
  .delete(stakeholderController.deleteStakeholder);
console.log('GET, PUT, DELETE routes set up at /api/resources/stakeholders/:id');

// Log all routes in this router
console.log('Stakeholder routes:');
router.stack.forEach(r => {
  if (r.route && r.route.path) {
    const methods = Object.keys(r.route.methods).map(m => m.toUpperCase()).join(', ');
    console.log(`${methods} /api/resources/stakeholders${r.route.path}`);
  }
});

module.exports = router;
