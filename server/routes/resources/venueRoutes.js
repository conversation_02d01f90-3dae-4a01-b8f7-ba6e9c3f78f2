const express = require('express');
const { venueController } = require('../../controllers/resources');
const { protect } = require('../../middleware/authMiddleware');
const upload = require('../../middleware/uploadMiddleware');

const router = express.Router();

// Development routes (no authentication required)
router.post('/dev-create', venueController.devCreateVenue);
router.put('/dev-update/:id', venueController.devUpdateVenue);
router.delete('/dev-delete/:id', venueController.devDeleteVenue);

// Apply authentication middleware to all other routes
router.use(protect);

router.route('/')
  .get(venueController.getVenues)
  .post(venueController.createVenue);

router.route('/:id')
  .get(venueController.getVenueById)
  .put(venueController.updateVenue)
  .delete(venueController.deleteVenue);

router.route('/:id/floorplan')
  .put(venueController.updateFloorPlan);

// Upload background image for floor plan
router.route('/:id/floorplan/background')
  .post(upload.single('image'), venueController.uploadFloorPlanBackground);

module.exports = router;
