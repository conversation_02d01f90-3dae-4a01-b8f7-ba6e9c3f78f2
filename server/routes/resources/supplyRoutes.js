const express = require('express');
const { supplyController } = require('../../controllers/resources');
const { protect } = require('../../middleware/authMiddleware');

const router = express.Router();

console.log('Setting up supply routes...');

// Add a test route to verify the router is working without auth
router.get('/test', (req, res) => {
  console.log('Supply test route accessed');
  res.json({ message: 'Supply routes are working' });
});

// Apply authentication middleware to all other routes
router.use(protect);
console.log('Authentication middleware applied to supply routes (except test routes)');

router.route('/')
  .get(supplyController.getSupplies)
  .post(supplyController.createSupply);
console.log('GET and POST routes set up at /api/resources/supplies');

router.route('/:id')
  .get(supplyController.getSupplyById)
  .put(supplyController.updateSupply)
  .delete(supplyController.deleteSupply);
console.log('GET, PUT, DELETE routes set up at /api/resources/supplies/:id');

router.route('/:id/tasks/:taskId')
  .post(supplyController.addSupplyToTask)
  .delete(supplyController.removeSupplyFromTask);
console.log('POST, DELETE routes set up at /api/resources/supplies/:id/tasks/:taskId');

// Log all routes in this router
console.log('Supply routes:');
router.stack.forEach(r => {
  if (r.route && r.route.path) {
    const methods = Object.keys(r.route.methods).map(m => m.toUpperCase()).join(', ');
    console.log(`${methods} /api/resources/supplies${r.route.path}`);
  }
});

module.exports = router;
