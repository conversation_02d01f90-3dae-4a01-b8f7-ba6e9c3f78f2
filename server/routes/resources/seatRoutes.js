const express = require('express');
const { seatController } = require('../../controllers/resources');
const { protect } = require('../../middleware/authMiddleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(protect);

router.route('/')
  .get(seatController.getSeats)
  .post(seatController.createSeat);

router.route('/:id')
  .get(seatController.getSeatById)
  .put(seatController.updateSeat)
  .delete(seatController.deleteSeat);

router.route('/:id/assign')
  .put(seatController.assignGuestToSeat);

module.exports = router;
