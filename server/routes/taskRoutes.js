const express = require('express');
const router = express.Router();
const {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  deleteTask,
  batchDeleteTasks,
  updateTaskStatus,
  getTaskAttachments,
  uploadTaskAttachment,
  deleteTaskAttachment,
  getTasksForCalendar,
  exportTasks,
  importTasks,
  getTaskSchema,
  // Budget-related controllers
  addBudgetItem,
  updateBudgetItem,
  deleteBudgetItem,
  getEventBudget
} = require('../controllers/taskController');
const { protect } = require('../middleware/authMiddleware');
const upload = require('../middleware/uploadMiddleware');

// Task schema endpoint
router.route('/schema')
  .get(protect, getTaskSchema);

// Calendar view tasks - this must come BEFORE the /:id routes to avoid conflicts
router.route('/calendar')
  .get(protect, getTasksForCalendar);

// Budget management for an event - must come BEFORE the /:id routes
router.route('/budget/:eventId')
  .get(protect, getEventBudget);

// Export and import endpoints
router.route('/export/:eventId')
  .get(protect, exportTasks);

router.route('/import/:eventId')
  .post(protect, importTasks);

// Task routes
router.route('/')
  .post(protect, createTask)
  .get(protect, getTasks);

// IMPORTANT: Define specific routes BEFORE the /:id route to avoid conflicts
// Batch delete route - must come before /:id route to avoid "batch-delete" being treated as an ID
router.route('/batch-delete')
  .post(protect, batchDeleteTasks);

// This route must come AFTER all specific routes to avoid conflicts
router.route('/:id')
  .get(protect, getTaskById)
  .put(protect, updateTask)
  .delete(protect, deleteTask);

router.route('/:id/status')
  .patch(protect, updateTaskStatus);

// Budget item routes
router.route('/:id/budget')
  .post(protect, addBudgetItem);

router.route('/:id/budget/:budgetItemId')
  .put(protect, updateBudgetItem)
  .delete(protect, deleteBudgetItem);

// Task attachments
router.route('/:id/attachments')
  .get(protect, getTaskAttachments)
  .post(protect, upload.single('file'), uploadTaskAttachment);

router.route('/:id/attachments/:attachmentId')
  .delete(protect, deleteTaskAttachment);

module.exports = router;