const jwt = require('jsonwebtoken');
const { StatusCodes } = require('http-status-codes');
const User = require('../models/User');
const config = require('../config');

// Protect routes - middleware to verify JWT token
const protect = async (req, res, next) => {
  // Check for token in Authorization header
  let token = null;
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
    console.log('Authorization header found, token present:', !!token);
  }

  // If using in-memory database, use simplified authentication
  if (req.app.locals.db) {
    console.log('Using in-memory database for authentication');

    // Use the admin user from in-memory database
    const adminUser = req.app.locals.db.users.find(user => user._id === 'admin-user-1');
    if (adminUser) {
      req.user = {
        id: adminUser._id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role || 'admin'
      };
      console.log('Using in-memory admin user:', req.user.name);
      return next();
    }

    // Fallback to default dev user if no admin found
    req.user = {
      id: 'dev-user-1',
      name: 'Developer User',
      email: '<EMAIL>',
      role: 'admin'
    };
    console.log('Using fallback dev user');
    return next();
  }

  // MongoDB authentication with JWT
  if (!token) {
    console.error('No token provided for protected route');
    // Check if i18n is available
    const message = typeof req.__ === 'function'
      ? req.__('errors.auth.unauthorized')
      : 'Unauthorized access';
    return res.status(StatusCodes.UNAUTHORIZED).json({ message });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, config.jwtSecret);
    console.log('Token verified successfully');

    // Get user from the database
    req.user = await User.findById(decoded.id).select('-password');

    if (!req.user) {
      console.error('User not found for token');
      // Check if i18n is available
      const message = typeof req.__ === 'function'
        ? req.__('errors.auth.userNotFound')
        : 'User not found';
      return res.status(StatusCodes.UNAUTHORIZED).json({ message });
    }

    // Set language based on user preference if available
    if (req.user.language) {
      req.setLocale(req.user.language);
    }

    console.log('User authenticated:', req.user.name || req.user.email);
    next();
  } catch (error) {
    console.error('Token verification failed:', error.message);
    // Check if i18n is available
    const message = typeof req.__ === 'function'
      ? req.__('errors.auth.invalidToken')
      : 'Invalid token';
    res.status(StatusCodes.UNAUTHORIZED).json({ message });
  }
};

// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user.role || !roles.includes(req.user.role)) {
      // Check if i18n is available
      const message = typeof req.__ === 'function'
        ? req.__('errors.auth.unauthorized')
        : 'Unauthorized access';
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ message });
    }
    next();
  };
};

module.exports = { protect, authorize };