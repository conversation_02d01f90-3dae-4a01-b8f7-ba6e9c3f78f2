const User = require('../models/User');

/**
 * Middleware to detect and set the language for the request
 * It checks for language in the following order:
 * 1. User's preference from database (if authenticated)
 * 2. Query parameter 'lang'
 * 3. <PERSON><PERSON> 'lang'
 * 4. Accept-Language header
 * 5. Default to 'en'
 */
const detectLanguage = async (req, res, next) => {
  try {
    let lang = null;

    // Check if user is authenticated and has a language preference
    if (req.user && req.user.id) {
      try {
        const user = await User.findById(req.user.id);
        if (user && user.language) {
          lang = user.language;
        }
      } catch (error) {
        console.error('Error fetching user language preference:', error);
        // Continue with other methods if user lookup fails
      }
    }

    // If no user preference, get language from query parameter
    if (!lang) {
      lang = req.query.lang;
    }

    // If not in query, check cookie
    if (!lang && req.cookies && req.cookies.lang) {
      lang = req.cookies.lang;
    }

    // If not in cookie, check Accept-Language header
    if (!lang && req.headers['accept-language']) {
      // Parse Accept-Language header
      const acceptLanguage = req.headers['accept-language'];
      const languages = acceptLanguage.split(',').map(lang => {
        const [code, q = 'q=1.0'] = lang.trim().split(';');
        const quality = parseFloat(q.split('=')[1]);
        return { code, quality };
      });

      // Sort by quality
      languages.sort((a, b) => b.quality - a.quality);

      // Check if we support any of the languages
      const supportedLanguages = ['en', 'zh-TW'];
      const matchedLang = languages.find(lang =>
        supportedLanguages.includes(lang.code) ||
        supportedLanguages.includes(lang.code.split('-')[0])
      );

      if (matchedLang) {
        // If we have zh-TW or zh-HK or zh-*, use zh-TW
        if (matchedLang.code.startsWith('zh')) {
          lang = 'zh-TW';
        } else {
          lang = matchedLang.code;
        }
      }
    }

    // Set language for this request
    if (lang && ['en', 'zh-TW'].includes(lang)) {
      req.setLocale(lang);

      // Set cookie for future requests (but don't overwrite if from user preference)
      if (!req.user || !req.user.id) {
        res.cookie('lang', lang, {
          maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
          httpOnly: true
        });
      }
    }

    next();
  } catch (error) {
    console.error('Language detection error:', error);
    // Don't fail the request if language detection fails
    next();
  }
};

module.exports = { detectLanguage };
