const { StatusCodes } = require('http-status-codes');

// Not found middleware
const notFound = (req, res, next) => {
  // Check if i18n is available
  const notFoundMessage = typeof req.__ === 'function'
    ? req.__('errors.server.notFound')
    : 'Resource not found';
  const error = new Error(notFoundMessage + ` - ${req.originalUrl}`);
  res.status(StatusCodes.NOT_FOUND);
  next(error);
};

// Error handler middleware
const errorHandler = (err, req, res, next) => {
  const statusCode = res.statusCode === 200 ? 500 : res.statusCode;

  // Special handling for database connection issues
  if (err.name === 'MongooseServerSelectionError') {
    // Check if i18n is available
    const dbErrorMessage = typeof req.__ === 'function'
      ? req.__('errors.server.databaseError')
      : 'Database error';
    return res.status(503).json({
      message: dbErrorMessage,
      isDatabaseError: true
    });
  }

  res.status(statusCode).json({
    message: err.message,
    stack: process.env.NODE_ENV === 'production' ? null : err.stack
  });
};

module.exports = { notFound, errorHandler };