const fs = require('fs');
const path = require('path');

const DB_FILE_PATH = path.join(__dirname, '..', 'data', 'in-memory-db.json');

/**
 * Sets up persistence for the in-memory database
 * @param {Object} app - Express app instance
 */
const setupInMemoryPersistence = (app) => {
  // Create data directory if it doesn't exist
  const dataDir = path.join(__dirname, '..', 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  // Try to load existing data from file
  try {
    if (fs.existsSync(DB_FILE_PATH)) {
      const data = fs.readFileSync(DB_FILE_PATH, 'utf8');
      const parsedData = JSON.parse(data);
      
      // Only use file data if it's not empty and has the expected structure
      if (parsedData && parsedData.users && parsedData.events) {
        console.log('Loading in-memory database from file');
        app.locals.db = parsedData;
        console.log(`Loaded ${app.locals.db.users.length} users, ${app.locals.db.events.length} events, and ${app.locals.db.tasks.length || 0} tasks from file`);
      }
    }
  } catch (error) {
    console.error('Error loading in-memory database from file:', error.message);
  }
  
  // Save in-memory database to file periodically
  setInterval(() => {
    if (app.locals.db) {
      try {
        fs.writeFileSync(DB_FILE_PATH, JSON.stringify(app.locals.db, null, 2));
        console.log('In-memory database saved to file');
      } catch (error) {
        console.error('Error saving in-memory database to file:', error.message);
      }
    }
  }, 30000); // Save every 30 seconds
  
  // Save on process exit
  process.on('SIGINT', () => {
    if (app.locals.db) {
      try {
        fs.writeFileSync(DB_FILE_PATH, JSON.stringify(app.locals.db, null, 2));
        console.log('In-memory database saved to file on exit');
      } catch (error) {
        console.error('Error saving in-memory database to file on exit:', error.message);
      }
    }
    process.exit(0);
  });
};

module.exports = { setupInMemoryPersistence };
