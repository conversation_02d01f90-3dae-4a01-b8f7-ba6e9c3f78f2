/**
 * Utility to check and verify Liquibase migrations
 */
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Checks if Liquibase migrations need to be run
 * @returns {Promise<boolean>} True if migrations are up-to-date, false otherwise
 */
const checkLiquibaseMigrations = () => {
  return new Promise((resolve, reject) => {
    console.log('Checking Liquibase migrations status...');
    
    // Skip if SKIP_LIQUIBASE environment variable is set
    if (process.env.SKIP_LIQUIBASE === 'true') {
      console.log('Skipping Liquibase migration check as requested by environment variable');
      return resolve(true);
    }
    
    // Use npx to run the liquibase script
    const liquibase = spawn('node', [path.join(__dirname, '../scripts/liquibase.js'), 'status'], {
      cwd: path.join(__dirname, '..'),
    });
    
    let output = '';
    
    liquibase.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    liquibase.stderr.on('data', (data) => {
      console.error(`Liquibase error: ${data}`);
    });
    
    liquibase.on('close', (code) => {
      if (code !== 0) {
        console.error('Liquibase status check failed');
        return reject(new Error('Liquibase status check failed'));
      }
      
      // Check if there are pending changesets
      if (output.includes('changesets have not been applied')) {
        console.warn('Database schema is not up-to-date. Pending migrations detected.');
        
        // If AUTO_UPDATE_LIQUIBASE is set, run the migrations automatically
        if (process.env.AUTO_UPDATE_LIQUIBASE === 'true') {
          console.log('Running pending migrations automatically...');
          runLiquibaseUpdate()
            .then(() => resolve(true))
            .catch(err => reject(err));
        } else {
          console.warn('Please run "npm run liquibase:update" to apply pending migrations');
          resolve(false);
        }
      } else {
        console.log('Database schema is up-to-date');
        resolve(true);
      }
    });
    
    liquibase.on('error', (err) => {
      console.error('Failed to start Liquibase:', err);
      reject(err);
    });
  });
};

/**
 * Runs Liquibase update to apply pending migrations
 * @returns {Promise<void>}
 */
const runLiquibaseUpdate = () => {
  return new Promise((resolve, reject) => {
    console.log('Running Liquibase update...');
    
    const liquibase = spawn('node', [path.join(__dirname, '../scripts/liquibase.js'), 'update'], {
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    });
    
    liquibase.on('close', (code) => {
      if (code !== 0) {
        console.error('Liquibase update failed');
        return reject(new Error('Liquibase update failed'));
      }
      
      console.log('Liquibase update completed successfully');
      resolve();
    });
    
    liquibase.on('error', (err) => {
      console.error('Failed to start Liquibase update:', err);
      reject(err);
    });
  });
};

module.exports = {
  checkLiquibaseMigrations,
  runLiquibaseUpdate
};
