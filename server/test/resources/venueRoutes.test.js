const request = require('supertest');
const mongoose = require('mongoose');
// Import the Express app without starting the server
const { app } = require('../../server'); // Import only the app, not the server
const { Venue } = require('../../models/resources');
const User = require('../../models/User');
const Event = require('../../models/Event');
const jwt = require('jsonwebtoken');
const config = require('../../config');

describe('Venue API Routes', () => {
  let token;
  let testUser;
  let testEvent;
  let testVenue;

  beforeAll(async () => {
    // Create test user
    testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });

    // Create test event with all required fields
    testEvent = await Event.create({
      title: 'Test Event',
      description: 'Test event description',
      eventType: 'Wedding',
      date: new Date(),
      owner: testUser._id,
      collaborators: [{ user: testUser._id, role: 'Admin' }]
    });

    // Create test venue with all required fields
    testVenue = await Venue.create({
      name: 'Test Venue',
      address: '123 Test St, Test City, TS 12345',
      capacity: 100,
      description: 'A test venue for events',
      amenities: ['WiFi', 'Parking', 'Catering'],
      event: testEvent._id,
      createdBy: testUser._id
    });

    // Generate JWT token for authentication
    token = jwt.sign({ id: testUser._id }, config.jwtSecret, { expiresIn: '1h' });
  });

  describe('POST /api/resources/venues', () => {
    it('should create a new venue', async () => {
      const venueData = {
        name: 'Test Venue',
        address: '123 Test St, Test City, TS 12345',
        capacity: 100,
        description: 'A test venue for events',
        amenities: ['WiFi', 'Parking', 'Catering'],
        event: testEvent._id,
        createdBy: testUser._id
      };

      const res = await request(app)
        .post('/api/resources/venues')
        .set('Authorization', `Bearer ${token}`)
        .send(venueData);

      expect(res.statusCode).toEqual(201);
      expect(res.body).toHaveProperty('_id');
      expect(res.body.name).toEqual(venueData.name);
      expect(res.body.address).toEqual(venueData.address);

      // Save for later tests
      testVenue = res.body;
    });

    it('should return 400 if name or eventId is missing', async () => {
      const res = await request(app)
        .post('/api/resources/venues')
        .set('Authorization', `Bearer ${token}`)
        .send({ address: '123 Invalid St' });

      expect(res.statusCode).toEqual(400);
    });
  });

  describe('GET /api/resources/venues', () => {
    it('should get all venues for an event', async () => {
      const res = await request(app)
        .get(`/api/resources/venues?eventId=${testEvent._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(Array.isArray(res.body)).toBeTruthy();
      expect(res.body.length).toBeGreaterThan(0);
    });

    it('should return 400 if eventId is missing', async () => {
      const res = await request(app)
        .get('/api/resources/venues')
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(400);
    });
  });

  describe('GET /api/resources/venues/:id', () => {
    it('should get a venue by ID', async () => {
      const res = await request(app)
        .get(`/api/resources/venues/${testVenue._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('_id');
      expect(res.body.name).toEqual(testVenue.name);
    });

    it('should return 404 if venue not found', async () => {
      const res = await request(app)
        .get(`/api/resources/venues/${mongoose.Types.ObjectId()}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(404);
    });
  });

  describe('PUT /api/resources/venues/:id', () => {
    it('should update a venue', async () => {
      const updateData = {
        name: 'Updated Test Venue',
        capacity: 150
      };

      const res = await request(app)
        .put(`/api/resources/venues/${testVenue._id}`)
        .set('Authorization', `Bearer ${token}`)
        .send(updateData);

      expect(res.statusCode).toEqual(200);
      expect(res.body.name).toEqual(updateData.name);
      expect(res.body.capacity).toEqual(updateData.capacity);
    });
  });

  describe('PUT /api/resources/venues/:id/floorplan', () => {
    it('should update a venue floor plan', async () => {
      const floorPlanData = {
        floorPlan: {
          width: 800,
          height: 600,
          elements: [
            {
              id: 1,
              type: 'table',
              x: 100,
              y: 100,
              width: 80,
              height: 80
            },
            {
              id: 2,
              type: 'seat',
              x: 150,
              y: 150,
              width: 30,
              height: 30
            }
          ],
          background: null
        }
      };

      const res = await request(app)
        .put(`/api/resources/venues/${testVenue._id}/floorplan`)
        .set('Authorization', `Bearer ${token}`)
        .send(floorPlanData);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('floorPlan');
      expect(res.body.floorPlan.elements.length).toEqual(2);
    });
  });

  describe('DELETE /api/resources/venues/:id', () => {
    it('should delete a venue', async () => {
      const res = await request(app)
        .delete(`/api/resources/venues/${testVenue._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('message');

      // Verify it's deleted
      const checkRes = await request(app)
        .get(`/api/resources/venues/${testVenue._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(checkRes.statusCode).toEqual(404);
    });
  });
});
