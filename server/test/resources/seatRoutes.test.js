const request = require('supertest');
const mongoose = require('mongoose');
// Import the Express app without starting the server
const { app } = require('../../server'); // Import only the app, not the server
const { Seat, Venue, Guest } = require('../../models/resources');
const User = require('../../models/User');
const Event = require('../../models/Event');
const jwt = require('jsonwebtoken');
const config = require('../../config');

describe('Seat API Routes', () => {
  let token;
  let testUser;
  let testEvent;
  let testVenue;
  let testGuest;
  let testSeat;

  beforeAll(async () => {
    // Create test user
    testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });

    // Create test event with all required fields
    testEvent = await Event.create({
      title: 'Test Event',
      description: 'Test event description',
      eventType: 'Wedding',
      date: new Date(),
      owner: testUser._id,
      collaborators: [{ user: testUser._id, role: 'Admin' }]
    });

    // Create test venue with all required fields
    testVenue = await Venue.create({
      name: 'Test Venue',
      address: '123 Test St, Test City, TS 12345',
      capacity: 100,
      description: 'A test venue for events',
      amenities: ['WiFi', 'Parking', 'Catering'],
      event: testEvent._id,
      createdBy: testUser._id
    });

    // Create test guest
    testGuest = await Guest.create({
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '************',
      rsvpStatus: 'Confirmed',
      attributes: [{ type: 'Dietary', value: 'Vegetarian' }],
      event: testEvent._id
    });

    // Generate JWT token for authentication
    token = jwt.sign({ id: testUser._id }, config.jwtSecret, { expiresIn: '1h' });
  });

  describe('POST /api/resources/seats', () => {
    it('should create a new seat', async () => {
      const seatData = {
        label: 'Table 1, Seat 1',
        table: 'Table 1',
        venueId: testVenue._id,
        eventId: testEvent._id
      };

      const res = await request(app)
        .post('/api/resources/seats')
        .set('Authorization', `Bearer ${token}`)
        .send(seatData);

      expect(res.statusCode).toEqual(201);
      expect(res.body).toHaveProperty('_id');
      expect(res.body.label).toEqual(seatData.label);

      // Save for later tests
      testSeat = res.body;
    });

    it('should return 400 if required fields are missing', async () => {
      const res = await request(app)
        .post('/api/resources/seats')
        .set('Authorization', `Bearer ${token}`)
        .send({ label: 'Invalid Seat' });

      expect(res.statusCode).toEqual(400);
    });
  });

  describe('GET /api/resources/seats', () => {
    it('should get all seats for a venue', async () => {
      const res = await request(app)
        .get(`/api/resources/seats?venueId=${testVenue._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(Array.isArray(res.body)).toBeTruthy();
      expect(res.body.length).toBeGreaterThan(0);
    });

    it('should return 400 if venueId is missing', async () => {
      const res = await request(app)
        .get('/api/resources/seats')
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(400);
    });
  });

  describe('GET /api/resources/seats/:id', () => {
    it('should get a seat by ID', async () => {
      const res = await request(app)
        .get(`/api/resources/seats/${testSeat._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('_id');
      expect(res.body.label).toEqual(testSeat.label);
    });

    it('should return 404 if seat not found', async () => {
      const res = await request(app)
        .get(`/api/resources/seats/${mongoose.Types.ObjectId()}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(404);
    });
  });

  describe('PUT /api/resources/seats/:id/assign', () => {
    it('should assign a guest to a seat', async () => {
      const assignData = {
        guestId: testGuest._id
      };

      const res = await request(app)
        .put(`/api/resources/seats/${testSeat._id}/assign`)
        .set('Authorization', `Bearer ${token}`)
        .send(assignData);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('assignedGuest');
      expect(res.body.assignedGuest.toString()).toEqual(testGuest._id.toString());
    });
  });

  describe('PUT /api/resources/seats/:id/unassign', () => {
    it('should unassign a guest from a seat', async () => {
      const res = await request(app)
        .put(`/api/resources/seats/${testSeat._id}/unassign`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body.assignedGuest).toBeNull();
    });
  });

  describe('DELETE /api/resources/seats/:id', () => {
    it('should delete a seat', async () => {
      const res = await request(app)
        .delete(`/api/resources/seats/${testSeat._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('message');

      // Verify it's deleted
      const checkRes = await request(app)
        .get(`/api/resources/seats/${testSeat._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(checkRes.statusCode).toEqual(404);
    });
  });
});
