const request = require('supertest');
const mongoose = require('mongoose');
// Import the Express app without starting the server
const { app } = require('../../server'); // Import only the app, not the server
const { Guest } = require('../../models/resources');
const User = require('../../models/User');
const Event = require('../../models/Event');
const jwt = require('jsonwebtoken');
const config = require('../../config');

describe('Guest API Routes', () => {
  let token;
  let testUser;
  let testEvent;
  let testGuest;

  beforeAll(async () => {
    // Create test user
    testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });

    // Create test event with all required fields
    testEvent = await Event.create({
      title: 'Test Event',
      description: 'Test event description',
      eventType: 'Wedding',
      date: new Date(),
      owner: testUser._id,
      collaborators: [{ user: testUser._id, role: 'Admin' }]
    });

    // Generate JWT token for authentication
    token = jwt.sign({ id: testUser._id }, config.jwtSecret, { expiresIn: '1h' });
  });

  describe('POST /api/resources/guests', () => {
    it('should create a new guest', async () => {
      const guestData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '************',
        rsvpStatus: 'Pending',
        attributes: [{ type: 'Dietary', value: 'Vegetarian' }],
        eventId: testEvent._id
      };

      const res = await request(app)
        .post('/api/resources/guests')
        .set('Authorization', `Bearer ${token}`)
        .send(guestData);

      expect(res.statusCode).toEqual(201);
      expect(res.body).toHaveProperty('_id');
      expect(res.body.name).toEqual(guestData.name);
      expect(res.body.email).toEqual(guestData.email);

      // Save for later tests
      testGuest = res.body;
    });

    it('should return 400 if name or eventId is missing', async () => {
      const res = await request(app)
        .post('/api/resources/guests')
        .set('Authorization', `Bearer ${token}`)
        .send({ email: '<EMAIL>' });

      expect(res.statusCode).toEqual(400);
    });
  });

  describe('GET /api/resources/guests', () => {
    it('should get all guests for an event', async () => {
      const res = await request(app)
        .get(`/api/resources/guests?eventId=${testEvent._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(Array.isArray(res.body)).toBeTruthy();
      expect(res.body.length).toBeGreaterThan(0);
    });

    it('should return 400 if eventId is missing', async () => {
      const res = await request(app)
        .get('/api/resources/guests')
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(400);
    });
  });

  describe('GET /api/resources/guests/:id', () => {
    it('should get a guest by ID', async () => {
      const res = await request(app)
        .get(`/api/resources/guests/${testGuest._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('_id');
      expect(res.body.name).toEqual(testGuest.name);
    });

    it('should return 404 if guest not found', async () => {
      const res = await request(app)
        .get(`/api/resources/guests/${mongoose.Types.ObjectId()}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(404);
    });
  });

  describe('PUT /api/resources/guests/:id', () => {
    it('should update a guest', async () => {
      const updateData = {
        name: 'John Doe Updated',
        rsvpStatus: 'Confirmed'
      };

      const res = await request(app)
        .put(`/api/resources/guests/${testGuest._id}`)
        .set('Authorization', `Bearer ${token}`)
        .send(updateData);

      expect(res.statusCode).toEqual(200);
      expect(res.body.name).toEqual(updateData.name);
      expect(res.body.rsvpStatus).toEqual(updateData.rsvpStatus);
    });
  });

  describe('DELETE /api/resources/guests/:id', () => {
    it('should delete a guest', async () => {
      const res = await request(app)
        .delete(`/api/resources/guests/${testGuest._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('message');

      // Verify it's deleted
      const checkRes = await request(app)
        .get(`/api/resources/guests/${testGuest._id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(checkRes.statusCode).toEqual(404);
    });
  });
});
