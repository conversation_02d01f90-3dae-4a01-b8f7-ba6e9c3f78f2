const User = require('../models/User');
const jwt = require('jsonwebtoken');
const { StatusCodes } = require('http-status-codes');
const mongoose = require('mongoose');
const config = require('../config');

// Generate JWT token
const generateToken = (id) => {
  return jwt.sign({ id }, config.jwtSecret, {
    expiresIn: config.jwtExpiresIn,
  });
};

// @desc    Google OAuth callback handler
// @route   GET /api/users/auth/google/callback
// @access  Public
const googleAuthCallback = (req, res) => {
  try {
    // Generate token for the authenticated user
    const token = generateToken(req.user._id);

    // Redirect to client with token
    res.redirect(`${config.clientURL}/google-auth-success?token=${token}&userId=${req.user._id}&name=${encodeURIComponent(req.user.name)}&email=${encodeURIComponent(req.user.email)}`);
  } catch (error) {
    console.error('Google auth callback error:', error);
    res.redirect(`${config.clientURL}/login?error=google_auth_failed`);
  }
};

// @desc    Handle Google login success
// @route   POST /api/users/google-login
// @access  Public
const handleGoogleLogin = async (req, res) => {
  try {
    const { googleId, email, name, picture } = req.body;

    // Check if user exists by googleId
    let user = await User.findOne({ googleId });

    if (!user) {
      // Check if user exists by email
      user = await User.findOne({ email });

      if (user) {
        // Update existing user with Google info
        user.googleId = googleId;
        user.picture = picture;
        await user.save();
      } else {
        // Create new user
        user = await User.create({
          name,
          email,
          googleId,
          picture
        });
      }
    }

    // Generate token
    const token = generateToken(user._id);

    res.status(StatusCodes.OK).json({
      _id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      language: user.language,
      picture: user.picture,
      token
    });
  } catch (error) {
    console.error('Google login error:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Google login failed. Please try again.'
    });
  }
};

// @desc    Register a new user
// @route   POST /api/users/register
// @access  Public
const registerUser = async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Check if user already exists
    const userExists = await User.findOne({ email });

    if (userExists) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'User already exists' });
    }

    // Create user
    const user = await User.create({
      name,
      email,
      password,
    });

    if (user) {
      // Create token
      const token = generateToken(user._id);

      res.status(StatusCodes.CREATED).json({
        _id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        language: user.language,
        token
      });
    } else {
      res.status(StatusCodes.BAD_REQUEST).json({ message: 'Invalid user data' });
    }
  } catch (error) {
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Login user
// @route   POST /api/users/login
// @access  Public
const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;

    // In development mode with DB connection issues, use dev mode authentication
    if (config.nodeEnv === 'development' && mongoose.connection.readyState !== 1) {
      console.log('Using development mode authentication (DB not connected)');

      // Create a mock user and token for development
      const mockToken = jwt.sign({ id: 'dev-user-123' }, config.jwtSecret, {
        expiresIn: config.jwtExpiresIn,
      });

      return res.status(StatusCodes.OK).json({
        _id: 'dev-user-123',
        name: 'Dev User',
        email: email || '<EMAIL>',
        role: 'admin',
        language: 'en',
        token: mockToken,
      });
    }

    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
        message: 'Database service unavailable. Please try again later.'
      });
    }

    // Normal login flow when database is connected
    // Use select('+password') to explicitly include the password field which is excluded by default
    const user = await User.findOne({ email }).select('+password');

    // Log login attempt (only in development)
    if (config.nodeEnv === 'development') {
      console.log(`Login attempt for ${email}`);
    }

    // Check if user exists
    if (!user) {
      if (config.nodeEnv === 'development') {
        console.log(`User not found: ${email}`);
      }
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid email or password' });
    }

    // Check if password matches
    // Add more detailed debugging in development mode
    if (config.nodeEnv === 'development') {
      console.log(`Attempting to match password for ${email}`);
      console.log(`Raw password input: ${password}`);
      console.log(`User password hash in DB: ${user.password.substring(0, 15)}...`);
    }

    // Use the matchPassword method from the User model
    const isMatch = await user.matchPassword(password);

    if (config.nodeEnv === 'development') {
      console.log(`Password match result for ${email}: ${isMatch}`);
    }

    if (isMatch) {
      // Create token
      const token = generateToken(user._id);

      // Set language preference from request if available
      if (req.headers['accept-language']) {
        const acceptLanguage = req.headers['accept-language'];
        const languages = acceptLanguage.split(',').map(lang => {
          const [code, q = 'q=1.0'] = lang.trim().split(';');
          return { code, quality: parseFloat(q.split('=')[1]) };
        });

        // Sort by quality
        languages.sort((a, b) => b.quality - a.quality);

        // Check if we support any of the languages
        const supportedLanguages = ['en', 'zh-TW'];
        const matchedLang = languages.find(lang =>
          supportedLanguages.includes(lang.code) ||
          supportedLanguages.includes(lang.code.split('-')[0])
        );

        if (matchedLang) {
          // If we have zh-TW or zh-HK or zh-*, use zh-TW
          if (matchedLang.code.startsWith('zh')) {
            user.language = 'zh-TW';
          } else {
            user.language = 'en';
          }
          await user.save();
        }
      }

      // Set language for this request
      if (user.language) {
        req.setLocale(user.language);
      }

      res.status(StatusCodes.OK).json({
        _id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        language: user.language,
        token,
      });
    } else {
      res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid email or password' });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Login failed. Please try again.'
    });
  }
};

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
const getUserProfile = async (req, res) => {
  try {
    // For in-memory database
    if (req.db) {
      return res.status(StatusCodes.OK).json({
        _id: 'dev-user-1',
        name: 'Developer User',
        email: '<EMAIL>',
        role: 'admin'
      });
    }

    const user = await User.findById(req.user.id);

    if (user) {
      res.status(StatusCodes.OK).json({
        _id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        language: user.language,
      });
    } else {
      res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
const updateUserProfile = async (req, res) => {
  try {
    // For in-memory database
    if (req.db) {
      return res.status(StatusCodes.OK).json({
        _id: 'dev-user-1',
        name: req.body.name || 'Developer User',
        email: req.body.email || '<EMAIL>',
        role: 'admin'
      });
    }

    const user = await User.findById(req.user.id);

    if (user) {
      user.name = req.body.name || user.name;
      user.email = req.body.email || user.email;

      if (req.body.password) {
        user.password = req.body.password;
      }

      // Update language preference if provided
      if (req.body.language && ['en', 'zh-TW'].includes(req.body.language)) {
        user.language = req.body.language;

        // Set language for this request
        req.setLocale(user.language);
      }

      const updatedUser = await user.save();

      res.status(StatusCodes.OK).json({
        _id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        language: updatedUser.language,
      });
    } else {
      res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }
  } catch (error) {
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
const getUsers = async (req, res) => {
  try {
    // For in-memory database
    if (req.db) {
      return res.status(StatusCodes.OK).json([
        {
          _id: 'dev-user-1',
          name: 'Developer User',
          email: '<EMAIL>',
          role: 'admin'
        },
        {
          _id: 'dev-user-2',
          name: 'Sample User',
          email: '<EMAIL>',
          role: 'user'
        }
      ]);
    }

    const users = await User.find({});
    res.status(StatusCodes.OK).json(users);
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
const deleteUser = async (req, res) => {
  try {
    // For in-memory database
    if (req.db) {
      return res.status(StatusCodes.OK).json({ message: 'User removed' });
    }

    const user = await User.findById(req.params.id);

    if (user) {
      await user.deleteOne();
      res.status(StatusCodes.OK).json({ message: 'User removed' });
    } else {
      res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
const updateUser = async (req, res) => {
  try {
    // For in-memory database
    if (req.db) {
      return res.status(StatusCodes.OK).json({
        _id: req.params.id,
        name: req.body.name || 'Updated User',
        email: req.body.email || '<EMAIL>',
        role: req.body.role || 'user'
      });
    }

    const user = await User.findById(req.params.id);

    if (user) {
      user.name = req.body.name || user.name;
      user.email = req.body.email || user.email;
      user.role = req.body.role || user.role;

      // Update language preference if provided
      if (req.body.language && ['en', 'zh-TW'].includes(req.body.language)) {
        user.language = req.body.language;
      }

      const updatedUser = await user.save();

      res.status(StatusCodes.OK).json({
        _id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        language: updatedUser.language,
      });
    } else {
      res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }
  } catch (error) {
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Search users by name or email
// @route   GET /api/users/search
// @access  Private
const searchUsers = async (req, res) => {
  try {
    const { q } = req.query;

    if (!q) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Search query is required' });
    }

    // For in-memory database
    if (req.db) {
      return res.status(StatusCodes.OK).json([
        {
          _id: 'dev-user-1',
          name: 'Developer User',
          email: '<EMAIL>',
          role: 'admin'
        },
        {
          _id: 'dev-user-2',
          name: 'Sample User',
          email: '<EMAIL>',
          role: 'user'
        }
      ]);
    }

    // Create a case-insensitive regex for the search query
    const searchRegex = new RegExp(q, 'i');

    // Search for users by name or email
    const users = await User.find({
      $or: [
        { name: { $regex: searchRegex } },
        { email: { $regex: searchRegex } }
      ]
    }).limit(10); // Limit to 10 results for performance

    res.status(StatusCodes.OK).json(users);
  } catch (error) {
    console.error('User search error:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

module.exports = {
  registerUser,
  loginUser,
  getUserProfile,
  searchUsers,
  updateUserProfile,
  getUsers,
  deleteUser,
  updateUser,
  googleAuthCallback,
  handleGoogleLogin
};