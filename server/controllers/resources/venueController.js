const asyncHandler = require('express-async-handler');
const { Venue, Seat } = require('../../models/resources');
const Event = require('../../models/Event');

// @desc    Get all venues for an event
// @route   GET /api/resources/venues
// @access  Private
const getVenues = asyncHandler(async (req, res) => {
  const { eventId } = req.query;

  if (!eventId) {
    res.status(400);
    throw new Error('Event ID is required');
  }

  // Check if event exists and user has access
  const event = await Event.findById(eventId);
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(403);
    throw new Error('Not authorized to access this event');
  }

  const venues = await Venue.find({ event: eventId });
  res.status(200).json(venues);
});

// @desc    Get a venue by ID
// @route   GET /api/resources/venues/:id
// @access  Private
const getVenueById = asyncHandler(async (req, res) => {
  const venue = await Venue.findById(req.params.id);

  if (!venue) {
    res.status(404);
    throw new Error('Venue not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(venue.event);
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(403);
    throw new Error('Not authorized to access this event');
  }

  res.status(200).json(venue);
});

// @desc    Create a new venue
// @route   POST /api/resources/venues
// @access  Private
const createVenue = asyncHandler(async (req, res) => {
  const {
    name,
    address,
    capacity,
    description,
    contactInfo,
    amenities,
    images,
    floorPlan,
    eventId
  } = req.body;

  if (!name || !address || !eventId) {
    res.status(400);
    throw new Error('Name, address, and event ID are required');
  }

  // Ensure capacity is a number
  const capacityNum = parseInt(capacity, 10);
  if (isNaN(capacityNum) || capacityNum < 0) {
    res.status(400);
    throw new Error('Capacity must be a valid number');
  }

  // Check if event exists and user has access
  const event = await Event.findById(eventId);
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(403);
    throw new Error('Not authorized to access this event');
  }

  const venue = await Venue.create({
    name,
    address,
    capacity,
    description,
    contactInfo,
    amenities,
    images,
    floorPlan: floorPlan || { width: 1000, height: 800, elements: [], background: null },
    event: eventId,
    createdBy: req.user._id,
    updatedBy: req.user._id
  });

  res.status(201).json(venue);
});

// @desc    Update a venue
// @route   PUT /api/resources/venues/:id
// @access  Private
const updateVenue = asyncHandler(async (req, res) => {
  const {
    name,
    address,
    capacity,
    description,
    contactInfo,
    amenities,
    images,
    floorPlan
  } = req.body;

  const venue = await Venue.findById(req.params.id);

  if (!venue) {
    res.status(404);
    throw new Error('Venue not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(venue.event);
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(403);
    throw new Error('Not authorized to update this venue');
  }

  venue.name = name || venue.name;
  venue.address = address || venue.address;
  venue.capacity = capacity || venue.capacity;
  venue.description = description !== undefined ? description : venue.description;
  venue.contactInfo = contactInfo || venue.contactInfo;
  venue.amenities = amenities || venue.amenities;
  venue.images = images || venue.images;
  venue.floorPlan = floorPlan || venue.floorPlan;
  venue.updatedBy = req.user._id;

  const updatedVenue = await venue.save();
  res.status(200).json(updatedVenue);
});

// @desc    Update venue floor plan
// @route   PUT /api/resources/venues/:id/floorplan
// @access  Private
const updateFloorPlan = asyncHandler(async (req, res) => {
  const { floorPlan } = req.body;
  const { id } = req.params;

  // Validate venue ID
  if (!id || id === 'undefined') {
    res.status(400);
    throw new Error('Invalid venue ID');
  }

  const venue = await Venue.findById(id);

  if (!venue) {
    res.status(404);
    throw new Error('Venue not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(venue.event);
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(403);
    throw new Error('Not authorized to update this venue');
  }

  venue.floorPlan = floorPlan;
  venue.updatedBy = req.user._id;

  const updatedVenue = await venue.save();
  res.status(200).json(updatedVenue);
});

// @desc    Delete a venue
// @route   DELETE /api/resources/venues/:id
// @access  Private
const deleteVenue = asyncHandler(async (req, res) => {
  const venue = await Venue.findById(req.params.id);

  if (!venue) {
    res.status(404);
    throw new Error('Venue not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(venue.event);
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(403);
    throw new Error('Not authorized to delete this venue');
  }

  // Delete all seats associated with this venue
  await Seat.deleteMany({ venue: venue._id });

  await venue.remove();
  res.status(200).json({ message: 'Venue removed' });
});

// @desc    Upload background image for venue floor plan
// @route   POST /api/resources/venues/:id/floorplan/background
// @access  Private
const uploadFloorPlanBackground = asyncHandler(async (req, res) => {
  if (!req.file) {
    res.status(400);
    throw new Error('No file uploaded');
  }

  const { id } = req.params;

  // Validate venue ID
  if (!id || id === 'undefined') {
    res.status(400);
    throw new Error('Invalid venue ID');
  }

  const venue = await Venue.findById(id);

  if (!venue) {
    res.status(404);
    throw new Error('Venue not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(venue.event);
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(403);
    throw new Error('Not authorized to update this venue');
  }

  // Create the file URL
  const fileUrl = `${req.protocol}://${req.get('host')}/${req.file.path}`;

  // Update the venue's floor plan background
  if (!venue.floorPlan) {
    venue.floorPlan = { width: 1000, height: 800, elements: [], background: fileUrl };
  } else {
    venue.floorPlan.background = fileUrl;
  }

  venue.updatedBy = req.user._id;
  const updatedVenue = await venue.save();

  res.status(200).json({
    success: true,
    background: fileUrl,
    venue: updatedVenue
  });
});

// Development routes for testing without authentication
const devCreateVenue = asyncHandler(async (req, res) => {
  console.log('Dev create venue route accessed with body:', req.body);

  const {
    name,
    address,
    capacity,
    description,
    contactInfo,
    amenities,
    images,
    floorPlan,
    eventId
  } = req.body;

  if (!name || !address || !eventId) {
    res.status(400);
    throw new Error('Name, address, and event ID are required');
  }

  // Ensure capacity is a number
  const capacityNum = parseInt(capacity, 10);
  if (isNaN(capacityNum) || capacityNum < 0) {
    res.status(400);
    throw new Error('Capacity must be a valid number');
  }

  // For development, create a mock user
  const mockUser = {
    _id: '6123456789abcdef01234567',
    name: 'Dev User',
    email: '<EMAIL>'
  };

  console.log('Creating venue with data:', {
    name,
    address,
    capacity: capacityNum,
    description,
    contactInfo,
    amenities,
    images,
    floorPlan: floorPlan || { width: 1000, height: 800, elements: [], background: null },
    event: eventId,
    createdBy: mockUser._id,
    updatedBy: mockUser._id
  });

  const venue = await Venue.create({
    name,
    address,
    capacity: capacityNum,
    description,
    contactInfo,
    amenities,
    images,
    floorPlan: floorPlan || { width: 1000, height: 800, elements: [], background: null },
    event: eventId,
    createdBy: mockUser._id,
    updatedBy: mockUser._id
  });

  console.log('Venue created successfully:', { id: venue._id, name: venue.name });
  res.status(201).json(venue);
});

const devUpdateVenue = asyncHandler(async (req, res) => {
  console.log('Dev update venue route accessed with ID:', req.params.id);
  console.log('Update data:', req.body);

  const venue = await Venue.findById(req.params.id);

  if (!venue) {
    res.status(404);
    throw new Error('Venue not found');
  }

  // For development, create a mock user
  const mockUser = {
    _id: '6123456789abcdef01234567',
    name: 'Dev User',
    email: '<EMAIL>'
  };

  const updatedVenue = await Venue.findByIdAndUpdate(
    req.params.id,
    {
      ...req.body,
      updatedBy: mockUser._id
    },
    { new: true, runValidators: true }
  );

  console.log('Venue updated successfully:', { id: updatedVenue._id, name: updatedVenue.name });
  res.status(200).json(updatedVenue);
});

const devDeleteVenue = asyncHandler(async (req, res) => {
  console.log('Dev delete venue route accessed with ID:', req.params.id);

  const venue = await Venue.findById(req.params.id);

  if (!venue) {
    res.status(404);
    throw new Error('Venue not found');
  }

  await Venue.findByIdAndDelete(req.params.id);

  console.log('Venue deleted successfully:', { id: req.params.id });
  res.status(200).json({ message: 'Venue deleted successfully' });
});

module.exports = {
  getVenues,
  getVenueById,
  createVenue,
  updateVenue,
  updateFloorPlan,
  deleteVenue,
  uploadFloorPlanBackground,
  devCreateVenue,
  devUpdateVenue,
  devDeleteVenue
};
