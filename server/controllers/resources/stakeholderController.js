const asyncHandler = require('express-async-handler');
const { Stakeholder } = require('../../models/resources');
const { StatusCodes } = require('http-status-codes');

// @desc    Get all stakeholders for an event
// @route   GET /api/resources/stakeholders
// @access  Private
const getStakeholders = asyncHandler(async (req, res) => {
  console.log('Get stakeholders request received');
  console.log('User in request:', req.user ? { id: req.user._id, name: req.user.name } : 'No user');

  const { eventId } = req.query;

  if (!eventId) {
    console.error('Missing required query parameter: eventId');
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Event ID is required');
  }

  try {
    const stakeholders = await Stakeholder.find({ event: eventId });
    console.log(`Found ${stakeholders.length} stakeholders for event ${eventId}`);
    res.status(StatusCodes.OK).json(stakeholders);
  } catch (error) {
    console.error('Error fetching stakeholders:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR);
    throw error;
  }
});

// @desc    Get a stakeholder by ID
// @route   GET /api/resources/stakeholders/:id
// @access  Private
const getStakeholderById = asyncHandler(async (req, res) => {
  console.log('Get stakeholder by ID request received:', req.params.id);
  console.log('User in request:', req.user ? { id: req.user._id, name: req.user.name } : 'No user');

  try {
    const stakeholder = await Stakeholder.findById(req.params.id);

    if (!stakeholder) {
      console.error('Stakeholder not found:', req.params.id);
      res.status(StatusCodes.NOT_FOUND);
      throw new Error('Stakeholder not found');
    }

    console.log('Stakeholder found:', stakeholder.name);
    res.status(StatusCodes.OK).json(stakeholder);
  } catch (error) {
    console.error('Error fetching stakeholder by ID:', error.message);
    if (error.kind === 'ObjectId') {
      res.status(StatusCodes.BAD_REQUEST);
      throw new Error('Invalid stakeholder ID format');
    }
    if (!res.statusCode || res.statusCode === 200) {
      res.status(StatusCodes.INTERNAL_SERVER_ERROR);
    }
    throw error;
  }
});

// @desc    Create a new stakeholder
// @route   POST /api/resources/stakeholders
// @access  Private
const createStakeholder = asyncHandler(async (req, res) => {
  console.log('Create stakeholder request received:', req.body);
  console.log('User in request:', req.user ? { id: req.user._id, name: req.user.name } : 'No user');

  const { name, email, phone, address, website, contactPoints, notes, eventId } = req.body;

  if (!name || !eventId) {
    console.error('Missing required fields:', { name, eventId });
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Name and event ID are required');
  }

  try {
    // Create the stakeholder
    const stakeholderData = {
      name,
      email,
      phone,
      address,
      website,
      contactPoints: contactPoints || [],
      notes,
      event: eventId,
      createdBy: req.user._id,
      updatedBy: req.user._id
    };

    console.log('Creating stakeholder with data:', stakeholderData);

    try {
      const stakeholder = await Stakeholder.create(stakeholderData);
      console.log('Stakeholder created successfully:', { id: stakeholder._id, name: stakeholder.name });
      res.status(StatusCodes.CREATED).json(stakeholder);
    } catch (err) {
      console.error('Error creating stakeholder:', err.message);
      if (err.name === 'ValidationError') {
        res.status(StatusCodes.BAD_REQUEST);
        throw new Error(`Validation error: ${err.message}`);
      }
      throw err;
    }
  } catch (error) {
    console.error('Error in createStakeholder:', error.message);
    if (!res.statusCode || res.statusCode === 200) {
      res.status(StatusCodes.INTERNAL_SERVER_ERROR);
    }
    throw error;
  }
});

// @desc    Update a stakeholder
// @route   PUT /api/resources/stakeholders/:id
// @access  Private
const updateStakeholder = asyncHandler(async (req, res) => {
  console.log('Update stakeholder request received:', { id: req.params.id, body: req.body });
  console.log('User in request:', req.user ? { id: req.user._id, name: req.user.name } : 'No user');

  const { name, email, phone, address, website, contactPoints, notes } = req.body;

  try {
    let stakeholder = await Stakeholder.findById(req.params.id);

    if (!stakeholder) {
      console.error('Stakeholder not found:', req.params.id);
      res.status(StatusCodes.NOT_FOUND);
      throw new Error('Stakeholder not found');
    }

    // Update stakeholder fields
    stakeholder.name = name || stakeholder.name;
    stakeholder.email = email || stakeholder.email;
    stakeholder.phone = phone || stakeholder.phone;
    stakeholder.address = address || stakeholder.address;
    stakeholder.website = website || stakeholder.website;
    stakeholder.contactPoints = contactPoints || stakeholder.contactPoints;
    stakeholder.notes = notes !== undefined ? notes : stakeholder.notes;
    stakeholder.updatedBy = req.user._id;

    console.log('Updating stakeholder with data:', {
      name: stakeholder.name,
      email: stakeholder.email,
      updatedBy: stakeholder.updatedBy
    });

    const updatedStakeholder = await stakeholder.save();
    console.log('Stakeholder updated successfully:', { id: updatedStakeholder._id, name: updatedStakeholder.name });
    res.status(StatusCodes.OK).json(updatedStakeholder);
  } catch (error) {
    console.error('Error updating stakeholder:', error.message);
    if (error.kind === 'ObjectId') {
      res.status(StatusCodes.BAD_REQUEST);
      throw new Error('Invalid stakeholder ID format');
    }
    if (!res.statusCode || res.statusCode === 200) {
      res.status(StatusCodes.INTERNAL_SERVER_ERROR);
    }
    throw error;
  }
});

// @desc    Delete a stakeholder
// @route   DELETE /api/resources/stakeholders/:id
// @access  Private
const deleteStakeholder = asyncHandler(async (req, res) => {
  console.log('Delete stakeholder request received:', req.params.id);
  console.log('User in request:', req.user ? { id: req.user._id, name: req.user.name } : 'No user');

  try {
    const stakeholder = await Stakeholder.findById(req.params.id);

    if (!stakeholder) {
      console.error('Stakeholder not found:', req.params.id);
      res.status(StatusCodes.NOT_FOUND);
      throw new Error('Stakeholder not found');
    }

    await stakeholder.deleteOne();
    console.log('Stakeholder deleted successfully:', req.params.id);
    res.status(StatusCodes.OK).json({ message: 'Stakeholder removed' });
  } catch (error) {
    console.error('Error deleting stakeholder:', error.message);
    if (error.kind === 'ObjectId') {
      res.status(StatusCodes.BAD_REQUEST);
      throw new Error('Invalid stakeholder ID format');
    }
    if (!res.statusCode || res.statusCode === 200) {
      res.status(StatusCodes.INTERNAL_SERVER_ERROR);
    }
    throw error;
  }
});

module.exports = {
  getStakeholders,
  getStakeholderById,
  createStakeholder,
  updateStakeholder,
  deleteStakeholder
};
