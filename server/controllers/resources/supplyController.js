const asyncHandler = require('express-async-handler');
const { Supply } = require('../../models/resources');
const Task = require('../../models/Task');
const Event = require('../../models/Event');
const { StatusCodes } = require('http-status-codes');

// @desc    Get all supplies for an event
// @route   GET /api/resources/supplies
// @access  Private
const getSupplies = asyncHandler(async (req, res) => {
  const { eventId } = req.query;

  if (!eventId) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Event ID is required');
  }

  // Check if event exists and user has access
  const event = await Event.findById(eventId);
  if (!event) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(StatusCodes.FORBIDDEN);
    throw new Error('Not authorized to access this event');
  }

  const supplies = await Supply.find({ event: eventId });
  res.status(StatusCodes.OK).json(supplies);
});

// @desc    Get a supply by ID
// @route   GET /api/resources/supplies/:id
// @access  Private
const getSupplyById = asyncHandler(async (req, res) => {
  const supply = await Supply.findById(req.params.id);

  if (!supply) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Supply not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(supply.event);
  if (!event) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(StatusCodes.FORBIDDEN);
    throw new Error('Not authorized to access this supply');
  }

  res.status(StatusCodes.OK).json(supply);
});

// @desc    Create a new supply
// @route   POST /api/resources/supplies
// @access  Private
const createSupply = asyncHandler(async (req, res) => {
  const {
    name,
    description,
    quantity,
    unit,
    estimatedCost,
    actualCost,
    currency,
    vendor,
    isPurchased,
    purchaseDate,
    category,
    notes,
    event,
    tasks
  } = req.body;

  if (!name || !event) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Name and event ID are required');
  }

  // Check if event exists and user has access
  const eventDoc = await Event.findById(event);
  if (!eventDoc) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (eventDoc.owner.toString() !== req.user._id.toString() &&
      !eventDoc.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(StatusCodes.FORBIDDEN);
    throw new Error('Not authorized to add supplies to this event');
  }

  // Create the supply
  const supply = await Supply.create({
    name,
    description,
    quantity: quantity || 1,
    unit: unit || 'piece',
    estimatedCost: estimatedCost || 0,
    actualCost: actualCost || 0,
    currency: currency || 'USD',
    vendor,
    isPurchased: isPurchased || false,
    purchaseDate,
    category: category || 'Other',
    notes,
    event,
    tasks: tasks || [],
    createdBy: req.user._id,
    updatedBy: req.user._id
  });

  // If tasks are provided, update the tasks with this supply
  if (tasks && tasks.length > 0) {
    await Promise.all(tasks.map(async (taskId) => {
      const task = await Task.findById(taskId);
      if (task) {
        // Add the supply to the task's supplies array if it exists
        if (!task.supplies) {
          task.supplies = [];
        }
        task.supplies.push(supply._id);
        await task.save();
      }
    }));
  }

  res.status(StatusCodes.CREATED).json(supply);
});

// @desc    Update a supply
// @route   PUT /api/resources/supplies/:id
// @access  Private
const updateSupply = asyncHandler(async (req, res) => {
  const supply = await Supply.findById(req.params.id);

  if (!supply) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Supply not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(supply.event);
  if (!event) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(StatusCodes.FORBIDDEN);
    throw new Error('Not authorized to update this supply');
  }

  // Handle tasks association updates
  const oldTasks = [...supply.tasks];
  const newTasks = req.body.tasks || [];

  // Find tasks to remove this supply from
  const tasksToRemove = oldTasks.filter(taskId => !newTasks.includes(taskId.toString()));

  // Find tasks to add this supply to
  const tasksToAdd = newTasks.filter(taskId => !oldTasks.map(id => id.toString()).includes(taskId));

  // Update tasks that need this supply removed
  await Promise.all(tasksToRemove.map(async (taskId) => {
    const task = await Task.findById(taskId);
    if (task && task.supplies) {
      task.supplies = task.supplies.filter(id => id.toString() !== supply._id.toString());
      await task.save();
    }
  }));

  // Update tasks that need this supply added
  await Promise.all(tasksToAdd.map(async (taskId) => {
    const task = await Task.findById(taskId);
    if (task) {
      if (!task.supplies) {
        task.supplies = [];
      }
      task.supplies.push(supply._id);
      await task.save();
    }
  }));

  // Update the supply with new data
  const updatedSupply = await Supply.findByIdAndUpdate(
    req.params.id,
    {
      ...req.body,
      updatedBy: req.user._id
    },
    { new: true, runValidators: true }
  );

  res.status(StatusCodes.OK).json(updatedSupply);
});

// @desc    Delete a supply
// @route   DELETE /api/resources/supplies/:id
// @access  Private
const deleteSupply = asyncHandler(async (req, res) => {
  const supply = await Supply.findById(req.params.id);

  if (!supply) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Supply not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(supply.event);
  if (!event) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(StatusCodes.FORBIDDEN);
    throw new Error('Not authorized to delete this supply');
  }

  // Remove this supply from all associated tasks
  if (supply.tasks && supply.tasks.length > 0) {
    await Promise.all(supply.tasks.map(async (taskId) => {
      const task = await Task.findById(taskId);
      if (task && task.supplies) {
        task.supplies = task.supplies.filter(id => id.toString() !== supply._id.toString());
        await task.save();
      }
    }));
  }

  await supply.remove();
  res.status(StatusCodes.OK).json({ message: 'Supply removed' });
});

// @desc    Add a supply to a task
// @route   POST /api/resources/supplies/:id/tasks/:taskId
// @access  Private
const addSupplyToTask = asyncHandler(async (req, res) => {
  const { id, taskId } = req.params;

  // Find the supply
  const supply = await Supply.findById(id);
  if (!supply) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Supply not found');
  }

  // Find the task
  const task = await Task.findById(taskId);
  if (!task) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Task not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(supply.event);
  if (!event) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(StatusCodes.FORBIDDEN);
    throw new Error('Not authorized to update this supply');
  }

  // Check if the task belongs to the same event as the supply
  if (task.event.toString() !== supply.event.toString()) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Task and supply must belong to the same event');
  }

  // Check if the supply is already associated with the task
  if (supply.tasks.some(t => t.toString() === taskId)) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Supply is already associated with this task');
  }

  // Add the task to the supply's tasks array
  supply.tasks.push(taskId);
  await supply.save();

  // Add the supply to the task's supplies array
  if (!task.supplies) {
    task.supplies = [];
  }
  task.supplies.push(id);
  await task.save();

  res.status(StatusCodes.OK).json(supply);
});

// @desc    Remove a supply from a task
// @route   DELETE /api/resources/supplies/:id/tasks/:taskId
// @access  Private
const removeSupplyFromTask = asyncHandler(async (req, res) => {
  const { id, taskId } = req.params;

  // Find the supply
  const supply = await Supply.findById(id);
  if (!supply) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Supply not found');
  }

  // Find the task
  const task = await Task.findById(taskId);
  if (!task) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Task not found');
  }

  // Check if event exists and user has access
  const event = await Event.findById(supply.event);
  if (!event) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Event not found');
  }

  // Verify user has access to this event
  if (event.owner.toString() !== req.user._id.toString() &&
      !event.collaborators.some(collab => collab.user.toString() === req.user._id.toString())) {
    res.status(StatusCodes.FORBIDDEN);
    throw new Error('Not authorized to update this supply');
  }

  // Remove the task from the supply's tasks array
  supply.tasks = supply.tasks.filter(t => t.toString() !== taskId);
  await supply.save();

  // Remove the supply from the task's supplies array
  if (task.supplies) {
    task.supplies = task.supplies.filter(s => s.toString() !== id);
    await task.save();
  }

  res.status(StatusCodes.OK).json(supply);
});

module.exports = {
  getSupplies,
  getSupplyById,
  createSupply,
  updateSupply,
  deleteSupply,
  addSupplyToTask,
  removeSupplyFromTask
};
