const Subscription = require('../models/Subscription');
const Transaction = require('../models/Transaction');
const User = require('../models/User');
const config = require('../config/config');
const stripe = require('stripe')(config.stripe.secretKey);
const asyncHandler = require('express-async-handler');

/**
 * @desc    Get subscription plans
 * @route   GET /api/payments/plans
 * @access  Public
 */
const getSubscriptionPlans = asyncHandler(async (req, res) => {
  // Return subscription plans with pricing information
  res.status(200).json({
    plans: [
      {
        id: 'free',
        name: 'Free',
        description: 'Perfect for small, simple events',
        price: 0,
        currency: 'USD',
        features: [
          'Up to 3 events',
          'Up to 50 guests per event',
          'Basic task management',
          'Simple venue planning',
          'Email support'
        ]
      },
      {
        id: 'pro',
        name: 'Pro',
        description: 'For professional event planners',
        price: config.stripe.products.pro.amount,
        currency: 'USD',
        stripePriceId: config.stripe.products.pro.priceId,
        features: [
          'Unlimited events',
          'Up to 200 guests per event',
          'Advanced task management',
          'Detailed venue planning',
          'Email & chat support',
          'Task dependencies & timelines',
          'Team collaboration (up to 5 users)',
          'Custom event templates'
        ]
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        description: 'For large organizations with complex events',
        price: config.stripe.products.enterprise.amount,
        currency: 'USD',
        stripePriceId: config.stripe.products.enterprise.priceId,
        features: [
          'Unlimited events',
          'Unlimited guests',
          'Premium task management',
          'Advanced venue planning',
          'Priority support',
          'Advanced task dependencies',
          'Unlimited team collaboration',
          'Custom branding',
          'API access'
        ]
      }
    ]
  });
});

/**
 * @desc    Get user's current subscription
 * @route   GET /api/payments/subscription
 * @access  Private
 */
const getCurrentSubscription = asyncHandler(async (req, res) => {
  const subscription = await Subscription.findOne({ 
    user: req.user._id,
    status: { $in: ['active', 'trialing'] }
  }).sort({ createdAt: -1 });

  if (!subscription) {
    return res.status(200).json({ 
      subscription: {
        planType: 'free',
        status: 'active',
        startDate: new Date(),
      } 
    });
  }

  res.status(200).json({ subscription });
});

/**
 * @desc    Create Stripe checkout session
 * @route   POST /api/payments/create-checkout-session
 * @access  Private
 */
const createCheckoutSession = asyncHandler(async (req, res) => {
  const { planId, trialPeriodDays } = req.body;
  
  if (!planId || !['pro', 'enterprise'].includes(planId)) {
    res.status(400);
    throw new Error('Invalid plan selected');
  }

  // Get the price ID from the configuration
  const priceId = config.stripe.products[planId].priceId;
  
  // Get the user
  const user = await User.findById(req.user._id);
  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }

  // Check if user already has a Stripe customer ID
  let customerId;
  const existingSubscription = await Subscription.findOne({ 
    user: req.user._id,
    status: { $in: ['active', 'trialing'] }
  });

  if (existingSubscription && existingSubscription.paymentGatewayData && existingSubscription.paymentGatewayData.customerId) {
    customerId = existingSubscription.paymentGatewayData.customerId;
  } else {
    // Create a new customer
    const customer = await stripe.customers.create({
      email: user.email,
      name: user.name,
      metadata: {
        userId: user._id.toString()
      }
    });
    customerId = customer.id;
  }

  // Create subscription parameters
  const subscriptionParams = {
    customer: customerId,
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${config.corsOrigin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${config.corsOrigin}/pricing`,
    metadata: {
      userId: user._id.toString(),
      planId: planId
    }
  };

  // Add trial period if specified
  if (trialPeriodDays && parseInt(trialPeriodDays) > 0) {
    subscriptionParams.subscription_data = {
      trial_period_days: parseInt(trialPeriodDays)
    };
  }

  // Create checkout session
  const session = await stripe.checkout.sessions.create(subscriptionParams);

  res.status(200).json({ sessionId: session.id, url: session.url });
});

/**
 * @desc    Handle Stripe webhook
 * @route   POST /api/payments/webhook
 * @access  Public
 */
const handleStripeWebhook = asyncHandler(async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      config.stripe.webhookSecret
    );
  } catch (err) {
    console.error(`Webhook Error: ${err.message}`);
    res.status(400).send(`Webhook Error: ${err.message}`);
    return;
  }

  // Handle the event
  switch (event.type) {
    case 'checkout.session.completed': {
      const session = event.data.object;
      await handleCheckoutSessionCompleted(session);
      break;
    }
    case 'invoice.paid': {
      const invoice = event.data.object;
      await handleInvoicePaid(invoice);
      break;
    }
    case 'invoice.payment_failed': {
      const invoice = event.data.object;
      await handleInvoicePaymentFailed(invoice);
      break;
    }
    case 'customer.subscription.deleted': {
      const subscription = event.data.object;
      await handleSubscriptionDeleted(subscription);
      break;
    }
    case 'customer.subscription.trial_will_end': {
      const subscription = event.data.object;
      // Find the subscription in our database
      const dbSubscription = await Subscription.findOne({
        'paymentGatewayData.subscriptionId': subscription.id
      });
      
      if (dbSubscription) {
        // Here you could send an email or notification to the user
        console.log(`Trial ending soon for subscription ${subscription.id}`);
        
        // Optional: Create a record of the notification
        await Transaction.create({
          user: dbSubscription.user,
          subscription: dbSubscription._id,
          amount: 0,
          currency: 'USD',
          type: 'trial_ending',
          status: 'succeeded',
          paymentGateway: 'stripe',
          description: `Trial period ending soon for ${dbSubscription.planType} plan`
        });
      }
      break;
    }
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.status(200).json({ received: true });
});

/**
 * @desc    Cancel subscription
 * @route   POST /api/payments/cancel-subscription
 * @access  Private
 */
const cancelSubscription = asyncHandler(async (req, res) => {
  // Find the active subscription
  const subscription = await Subscription.findOne({ 
    user: req.user._id,
    status: 'active'
  });

  if (!subscription) {
    res.status(404);
    throw new Error('No active subscription found');
  }

  if (subscription.planType === 'free') {
    res.status(400);
    throw new Error('Free plan cannot be canceled');
  }

  if (subscription.paymentGateway === 'stripe' && subscription.paymentGatewayData.subscriptionId) {
    // Cancel at period end in Stripe
    await stripe.subscriptions.update(subscription.paymentGatewayData.subscriptionId, {
      cancel_at_period_end: true
    });

    // Update our record
    subscription.cancelAtPeriodEnd = true;
    await subscription.save();

    await Transaction.create({
      user: req.user._id,
      subscription: subscription._id,
      amount: 0,
      currency: 'USD',
      type: 'subscription_canceled',
      status: 'succeeded',
      paymentGateway: 'stripe',
      paymentMethod: 'credit_card',
      description: `Subscription canceled: ${subscription.planType} plan`
    });

    res.status(200).json({ message: 'Subscription will be canceled at the end of the billing period', subscription });
  } else {
    res.status(400);
    throw new Error('Cannot cancel this subscription type');
  }
});

// Helper functions for webhook handlers
async function handleCheckoutSessionCompleted(session) {
  // Extract customer and subscription info
  const customerId = session.customer;
  const subscriptionId = session.subscription;
  const userId = session.metadata.userId;
  const planId = session.metadata.planId;

  // Get subscription details from Stripe
  const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);
  
  // Create subscription record
  const subscription = await Subscription.create({
    user: userId,
    planType: planId,
    status: stripeSubscription.status,
    startDate: new Date(stripeSubscription.current_period_start * 1000),
    currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
    currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
    cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
    paymentGateway: 'stripe',
    paymentGatewayData: {
      customerId,
      subscriptionId,
      priceId: config.stripe.products[planId].priceId
    }
  });

  // Create transaction record
  await Transaction.create({
    user: userId,
    subscription: subscription._id,
    amount: config.stripe.products[planId].amount,
    currency: 'USD',
    type: 'subscription_created',
    status: 'succeeded',
    paymentGateway: 'stripe',
    paymentMethod: 'credit_card',
    gatewayData: {
      customerId,
      subscriptionId
    },
    description: `Subscription created: ${planId} plan`
  });
}

async function handleInvoicePaid(invoice) {
  const subscriptionId = invoice.subscription;
  const customerId = invoice.customer;
  
  // Find the subscription in our database
  const subscription = await Subscription.findOne({
    'paymentGatewayData.subscriptionId': subscriptionId
  });

  if (subscription) {
    // Update subscription dates
    const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    subscription.status = stripeSubscription.status;
    subscription.currentPeriodStart = new Date(stripeSubscription.current_period_start * 1000);
    subscription.currentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);
    await subscription.save();

    // Create transaction record for the renewal
    await Transaction.create({
      user: subscription.user,
      subscription: subscription._id,
      amount: invoice.amount_paid / 100, // Convert from cents
      currency: invoice.currency.toUpperCase(),
      type: 'subscription_renewed',
      status: 'succeeded',
      paymentGateway: 'stripe',
      paymentMethod: 'credit_card',
      gatewayData: {
        customerId,
        subscriptionId,
        invoiceId: invoice.id,
        receiptUrl: invoice.hosted_invoice_url
      },
      description: `Subscription renewed: ${subscription.planType} plan`
    });
  }
}

async function handleInvoicePaymentFailed(invoice) {
  const subscriptionId = invoice.subscription;
  
  // Find the subscription in our database
  const subscription = await Subscription.findOne({
    'paymentGatewayData.subscriptionId': subscriptionId
  });

  if (subscription) {
    // Update subscription status
    const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);
    subscription.status = stripeSubscription.status;
    await subscription.save();

    // Create transaction record for the failed payment
    await Transaction.create({
      user: subscription.user,
      subscription: subscription._id,
      amount: invoice.amount_due / 100, // Convert from cents
      currency: invoice.currency.toUpperCase(),
      type: 'payment',
      status: 'failed',
      paymentGateway: 'stripe',
      paymentMethod: 'credit_card',
      gatewayData: {
        customerId: invoice.customer,
        subscriptionId,
        invoiceId: invoice.id
      },
      description: `Payment failed for ${subscription.planType} plan`
    });
  }
}

async function handleSubscriptionDeleted(subscription) {
  // Find the subscription in our database
  const dbSubscription = await Subscription.findOne({
    'paymentGatewayData.subscriptionId': subscription.id
  });

  if (dbSubscription) {
    // Update subscription status
    dbSubscription.status = 'canceled';
    dbSubscription.endDate = new Date(subscription.ended_at * 1000);
    await dbSubscription.save();

    // Create transaction record
    await Transaction.create({
      user: dbSubscription.user,
      subscription: dbSubscription._id,
      amount: 0,
      currency: 'USD',
      type: 'subscription_canceled',
      status: 'succeeded',
      paymentGateway: 'stripe',
      paymentMethod: 'credit_card',
      gatewayData: {
        customerId: subscription.customer,
        subscriptionId: subscription.id
      },
      description: `Subscription ended: ${dbSubscription.planType} plan`
    });
  }
}

module.exports = {
  getSubscriptionPlans,
  getCurrentSubscription,
  createCheckoutSession,
  handleStripeWebhook,
  cancelSubscription
}; 