const Event = require('../models/Event');
const Task = require('../models/Task');
const Template = require('../models/Template');
const mongoose = require('mongoose');
const { StatusCodes } = require('http-status-codes');

// @desc    Create new event
// @route   POST /api/events
// @access  Private
const createEvent = async (req, res) => {
  try {
    const { title, eventType, date, description, venue, budget, templateId } = req.body;
    
    // If using in-memory database
    if (req.db) {
      const newEvent = {
        _id: `event${Date.now()}`,
        title,
        eventType,
        date: new Date(date),
        description,
        venue,
        budget,
        user: req.user ? req.user._id : 'user1', // Default to first user in dev mode
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      req.db.events.push(newEvent);
      return res.status(201).json(newEvent);
    }
    
    // Otherwise use MongoDB
    const event = await Event.create({
      title,
      eventType,
      date,
      description,
      venue,
      budget,
      owner: req.user.id,
      collaborators: [{ user: req.user.id, role: 'Admin' }]
    });

    // If a template is specified, create tasks from that template
    if (templateId) {
      const template = await Template.findById(templateId);
      
      if (template) {
        // Create tasks from template tasks
        const eventDate = new Date(date);
        
        for (const templateTask of template.tasks) {
          // Calculate deadline based on relative days from event
          const deadlineDate = new Date(eventDate);
          deadlineDate.setDate(deadlineDate.getDate() + templateTask.relativeDeadline);
          
          // Create the task
          await Task.create({
            name: templateTask.name,
            taskType: templateTask.taskType,
            details: templateTask.details,
            softDeadline: deadlineDate,
            event: event._id,
            status: 'Not Started'
          });
        }
      }
    }

    res.status(StatusCodes.CREATED).json(event);
  } catch (error) {
    console.error('Error in createEvent:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

// @desc    Get all events
// @route   GET /api/events
// @access  Private
const getEvents = async (req, res) => {
  try {
    // Check if using in-memory database
    if (req.app.locals.db) {
      console.log('Using in-memory database for events');
      return res.status(StatusCodes.OK).json(req.app.locals.db.events);
    }
    
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('Database not connected, returning sample events data');
      return res.status(StatusCodes.OK).json([
        {
          _id: 'sample-event-1',
          title: 'Sample Wedding Event',
          eventType: 'Wedding',
          date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days in future
          description: 'Sample wedding event for development',
          venue: {
            name: 'Sample Venue',
            address: '123 Sample St, Example City'
          },
          budget: {
            total: 15000,
            currency: 'USD'
          },
          owner: req.user ? req.user.id : 'dev-user-1',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: 'sample-event-2',
          title: 'Sample Birthday Party',
          eventType: 'Birthday',
          date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days in future
          description: 'Sample birthday event for development',
          venue: {
            name: 'Home',
            address: '456 Home Ave, Example Town'
          },
          budget: {
            total: 500,
            currency: 'USD'
          },
          owner: req.user ? req.user.id : 'dev-user-1',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);
    }

    // If DB is connected, proceed with normal operation
    // Find events where user is either:
    // 1. The owner
    // 2. Has an accepted invitation
    const events = await Event.find({
      $or: [
        { owner: req.user.id },
        { 'invitations': { 
          $elemMatch: { 
            user: req.user.id, 
            status: 'Accepted' 
          } 
        }}
      ]
    }).populate('owner', 'name email');
    
    res.status(StatusCodes.OK).json(events);
  } catch (error) {
    console.error('Error fetching events:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Get a single event by ID
// @route   GET /api/events/:id
// @access  Private
const getEventById = async (req, res) => {
  try {
    // If using in-memory database
    if (req.db) {
      const event = req.db.events.find(e => e._id === req.params.id);
      if (!event) {
        return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
      }
      return res.json(event);
    }
    
    // Otherwise use MongoDB
    const event = await Event.findById(req.params.id)
      .populate('owner', 'name email')
      .populate('collaborators.user', 'name email');
      
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Check if user has access to this event
    const isOwner = event.owner._id.toString() === req.user.id;
    const isCollaborator = event.collaborators.some(
      collab => collab.user._id.toString() === req.user.id
    );
    
    if (!isOwner && !isCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to access this event' });
    }
    
    res.status(StatusCodes.OK).json(event);
  } catch (error) {
    console.error('Error in getEventById:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

// @desc    Update an event
// @route   PUT /api/events/:id
// @access  Private
const updateEvent = async (req, res) => {
  try {
    const { title, eventType, date, description, venue, budget } = req.body;
    
    // If using in-memory database
    if (req.db) {
      const eventIndex = req.db.events.findIndex(e => e._id === req.params.id);
      if (eventIndex === -1) {
        return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
      }
      
      req.db.events[eventIndex] = {
        ...req.db.events[eventIndex],
        title: title || req.db.events[eventIndex].title,
        eventType: eventType || req.db.events[eventIndex].eventType,
        date: date ? new Date(date) : req.db.events[eventIndex].date,
        description: description || req.db.events[eventIndex].description,
        venue: venue || req.db.events[eventIndex].venue,
        budget: budget || req.db.events[eventIndex].budget,
        updatedAt: new Date()
      };
      
      return res.json(req.db.events[eventIndex]);
    }
    
    // Otherwise use MongoDB
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Check if user is owner or admin collaborator
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.collaborators.some(
      collab => collab.user.toString() === req.user.id && collab.role === 'Admin'
    );
    
    if (!isOwner && !isAdminCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to update this event' });
    }
    
    // Update fields
    event.title = title || event.title;
    event.eventType = eventType || event.eventType;
    event.date = date || event.date;
    event.description = description || event.description;
    event.venue = venue || event.venue;
    event.budget = budget || event.budget;
    event.updatedAt = Date.now();
    
    const updatedEvent = await event.save();
    
    res.status(StatusCodes.OK).json(updatedEvent);
  } catch (error) {
    console.error('Error in updateEvent:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

// @desc    Delete an event
// @route   DELETE /api/events/:id
// @access  Private
const deleteEvent = async (req, res) => {
  try {
    // If using in-memory database
    if (req.db) {
      const eventIndex = req.db.events.findIndex(e => e._id === req.params.id);
      if (eventIndex === -1) {
        return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
      }
      
      req.db.events.splice(eventIndex, 1);
      
      // Also delete related tasks
      req.db.tasks = req.db.tasks.filter(t => t.event !== req.params.id);
      
      return res.json({ message: 'Event deleted successfully' });
    }
    
    // Otherwise use MongoDB
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Only the owner can delete an event
    if (event.owner.toString() !== req.user.id) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to delete this event' });
    }
    
    // Delete all associated tasks
    await Task.deleteMany({ event: req.params.id });
    
    // Delete the event
    await event.remove();
    
    res.status(StatusCodes.OK).json({ message: 'Event and associated tasks removed' });
  } catch (error) {
    console.error('Error in deleteEvent:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

// @desc    Add a collaborator to an event
// @route   POST /api/events/:id/collaborators
// @access  Private
const addCollaborator = async (req, res) => {
  try {
    const { userId, role } = req.body;
    
    if (!userId || !role) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Please provide both userId and role' });
    }
    
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Only owner or admin collaborators can add collaborators
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.collaborators.some(
      collab => collab.user.toString() === req.user.id && collab.role === 'Admin'
    );
    
    if (!isOwner && !isAdminCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to add collaborators' });
    }
    
    // Check if collaborator already exists
    const existingCollaborator = event.collaborators.find(
      collab => collab.user.toString() === userId
    );
    
    if (existingCollaborator) {
      existingCollaborator.role = role;
    } else {
      event.collaborators.push({ user: userId, role });
    }
    
    const updatedEvent = await event.save();
    
    res.status(StatusCodes.OK).json(updatedEvent);
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Remove a collaborator from an event
// @route   DELETE /api/events/:id/collaborators/:userId
// @access  Private
const removeCollaborator = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Only owner or admin collaborators can remove collaborators
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.collaborators.some(
      collab => collab.user.toString() === req.user.id && collab.role === 'Admin'
    );
    
    if (!isOwner && !isAdminCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to remove collaborators' });
    }
    
    // Owner cannot be removed as a collaborator
    if (req.params.userId === event.owner.toString()) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Cannot remove the event owner' });
    }
    
    // Filter out the collaborator
    event.collaborators = event.collaborators.filter(
      collab => collab.user.toString() !== req.params.userId
    );
    
    const updatedEvent = await event.save();
    
    res.status(StatusCodes.OK).json(updatedEvent);
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Get event tasks
// @route   GET /api/events/:id/tasks
// @access  Private
const getEventTasks = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Check if user has access to this event
    const isOwner = event.owner.toString() === req.user.id;
    const isCollaborator = event.collaborators.some(
      collab => collab.user.toString() === req.user.id
    );
    
    if (!isOwner && !isCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to access this event' });
    }
    
    const tasks = await Task.find({ event: req.params.id })
      .populate('assignee', 'name email')
      .sort({ softDeadline: 1 });
    
    res.status(StatusCodes.OK).json(tasks);
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Invite a user to an event
// @route   POST /api/events/:id/invitations
// @access  Private
const inviteUser = async (req, res) => {
  try {
    const { userId, role } = req.body;
    
    if (!userId || !role) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Please provide both userId and role' });
    }
    
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Only owner or admin collaborators can invite users
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.collaborators.some(
      collab => collab.user.toString() === req.user.id && collab.role === 'Admin'
    );
    
    if (!isOwner && !isAdminCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to invite users' });
    }
    
    // Check if user is already a collaborator
    const isCollaborator = event.collaborators.some(
      collab => collab.user.toString() === userId
    );
    
    if (isCollaborator) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'User is already a collaborator' });
    }
    
    // Check if invitation already exists
    const existingInvitation = event.invitations.find(
      invite => invite.user.toString() === userId && invite.status === 'Pending'
    );
    
    if (existingInvitation) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Invitation already sent to this user' });
    }
    
    // Add new invitation
    event.invitations.push({
      user: userId,
      role,
      status: 'Pending',
      invitedBy: req.user.id,
      invitedAt: new Date()
    });
    
    const updatedEvent = await event.save();
    
    res.status(StatusCodes.OK).json(updatedEvent);
  } catch (error) {
    console.error('Error in inviteUser:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

// @desc    Respond to an event invitation (accept or decline)
// @route   PUT /api/events/:id/invitations/:invitationId
// @access  Private
const respondToInvitation = async (req, res) => {
  try {
    const { status } = req.body;
    
    if (!status || !['Accepted', 'Declined'].includes(status)) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Please provide a valid status (Accepted or Declined)' });
    }
    
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Find the invitation for the current user
    const invitationIndex = event.invitations.findIndex(
      invite => invite._id.toString() === req.params.invitationId && invite.user.toString() === req.user.id
    );
    
    if (invitationIndex === -1) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Invitation not found or not authorized' });
    }
    
    const invitation = event.invitations[invitationIndex];
    
    // Update invitation status
    invitation.status = status;
    invitation.respondedAt = new Date();
    
    // If accepted, add user as collaborator
    if (status === 'Accepted') {
      // Check if user is already a collaborator (shouldn't happen but just in case)
      const existingCollaborator = event.collaborators.find(
        collab => collab.user.toString() === req.user.id
      );
      
      if (!existingCollaborator) {
        event.collaborators.push({
          user: req.user.id,
          role: invitation.role
        });
      }
    }
    
    const updatedEvent = await event.save();
    
    res.status(StatusCodes.OK).json(updatedEvent);
  } catch (error) {
    console.error('Error in respondToInvitation:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

// @desc    Get all invitations for an event
// @route   GET /api/events/:id/invitations
// @access  Private
const getEventInvitations = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id)
      .populate('invitations.user', 'name email')
      .populate('invitations.invitedBy', 'name email');
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Only owner or collaborators can view invitations
    const isOwner = event.owner.toString() === req.user.id;
    const isCollaborator = event.collaborators.some(
      collab => collab.user.toString() === req.user.id
    );
    
    if (!isOwner && !isCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to view invitations' });
    }
    
    res.status(StatusCodes.OK).json(event.invitations);
  } catch (error) {
    console.error('Error in getEventInvitations:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

// @desc    Get pending invitations for the current user
// @route   GET /api/events/invitations/pending
// @access  Private
const getUserPendingInvitations = async (req, res) => {
  try {
    const events = await Event.find({
      'invitations.user': req.user.id,
      'invitations.status': 'Pending'
    })
    .populate('owner', 'name email')
    .populate('invitations.invitedBy', 'name email');
    
    // Extract only the relevant information
    const pendingInvitations = events.map(event => {
      const invitation = event.invitations.find(
        invite => invite.user.toString() === req.user.id && invite.status === 'Pending'
      );
      
      return {
        eventId: event._id,
        eventTitle: event.title,
        eventType: event.eventType,
        eventDate: event.date,
        owner: event.owner,
        invitation: invitation
      };
    });
    
    res.status(StatusCodes.OK).json(pendingInvitations);
  } catch (error) {
    console.error('Error in getUserPendingInvitations:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

// @desc    Cancel an invitation
// @route   DELETE /api/events/:id/invitations/:invitationId
// @access  Private
const cancelInvitation = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }
    
    // Only owner or admin collaborators can cancel invitations
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.collaborators.some(
      collab => collab.user.toString() === req.user.id && collab.role === 'Admin'
    );
    
    if (!isOwner && !isAdminCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({ message: 'Not authorized to cancel invitations' });
    }
    
    // Find and remove the invitation
    const invitationIndex = event.invitations.findIndex(
      invite => invite._id.toString() === req.params.invitationId
    );
    
    if (invitationIndex === -1) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Invitation not found' });
    }
    
    // Remove the invitation
    event.invitations.splice(invitationIndex, 1);
    
    const updatedEvent = await event.save();
    
    res.status(StatusCodes.OK).json(updatedEvent);
  } catch (error) {
    console.error('Error in cancelInvitation:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Server Error', error: error.message });
  }
};

module.exports = {
  createEvent,
  getEvents,
  getEventById,
  updateEvent,
  deleteEvent,
  addCollaborator,
  removeCollaborator,
  getEventTasks,
  inviteUser,
  respondToInvitation,
  getEventInvitations,
  getUserPendingInvitations,
  cancelInvitation
}; 