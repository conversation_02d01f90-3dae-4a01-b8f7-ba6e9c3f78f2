const mongoose = require('mongoose');

/**
 * Transaction schema for recording payment transactions
 */
const transactionSchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    subscription: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Subscription'
    },
    amount: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      default: 'USD',
      required: true
    },
    type: {
      type: String,
      enum: ['subscription_created', 'subscription_renewed', 'subscription_canceled', 'subscription_updated', 'payment', 'refund'],
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'succeeded', 'failed', 'refunded'],
      default: 'pending',
      required: true
    },
    paymentGateway: {
      type: String,
      enum: ['stripe', 'paypal', 'manual'],
      default: 'stripe'
    },
    paymentMethod: {
      type: String,
      enum: ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'manual'],
      required: true
    },
    gatewayData: {
      paymentIntentId: String,
      chargeId: String,
      invoiceId: String,
      customerId: String,
      receiptUrl: String
    },
    description: {
      type: String
    },
    metadata: {
      type: Map,
      of: String
    }
  },
  {
    timestamps: true
  }
);

// Index for efficient queries
transactionSchema.index({ user: 1, createdAt: -1 });

module.exports = mongoose.model('Transaction', transactionSchema); 