const mongoose = require('mongoose');

/**
 * Subscription schema for handling payment subscriptions
 */
const subscriptionSchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    planType: {
      type: String,
      enum: ['free', 'pro', 'enterprise'],
      default: 'free',
      required: true
    },
    status: {
      type: String,
      enum: ['active', 'canceled', 'past_due', 'trialing', 'unpaid', 'incomplete'],
      default: 'active',
      required: true
    },
    startDate: {
      type: Date,
      default: Date.now,
      required: true
    },
    endDate: {
      type: Date
    },
    currentPeriodStart: {
      type: Date,
      default: Date.now
    },
    currentPeriodEnd: {
      type: Date
    },
    cancelAtPeriodEnd: {
      type: Boolean,
      default: false
    },
    paymentGateway: {
      type: String,
      enum: ['stripe', 'paypal', 'manual'],
      default: 'stripe'
    },
    paymentGatewayData: {
      customerId: String,
      subscriptionId: String,
      priceId: String
    },
    // Store metadata about the subscription
    metadata: {
      type: Map,
      of: String
    }
  },
  {
    timestamps: true
  }
);

module.exports = mongoose.model('Subscription', subscriptionSchema); 