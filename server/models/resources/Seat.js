const mongoose = require('mongoose');

const seatSchema = new mongoose.Schema({
  label: {
    type: String,
    required: true,
    trim: true
  },
  table: {
    type: String,
    required: true,
    trim: true
  },
  venue: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Venue',
    required: true
  },
  floorPlanElementId: {
    type: String,
    required: true
  },
  assignedGuest: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Guest'
  },
  event: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Event',
    required: true
  },
  properties: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const Seat = mongoose.model('Seat', seatSchema);

module.exports = Seat;
