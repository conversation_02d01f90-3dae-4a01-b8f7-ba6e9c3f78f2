const mongoose = require('mongoose');

// Schema for elements in the floor plan (tables, seats, etc.)
const floorPlanElementSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: ['table', 'seat', 'wall', 'entrance', 'stage', 'other']
  },
  x: {
    type: Number,
    required: true
  },
  y: {
    type: Number,
    required: true
  },
  width: {
    type: Number,
    required: true
  },
  height: {
    type: Number,
    required: true
  },
  rotation: {
    type: Number,
    default: 0
  },
  label: {
    type: String
  },
  capacity: {
    type: Number,
    default: 1
  },
  assignedGuests: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Guest'
  }],
  properties: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  }
});

// Main venue schema
const venueSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    type: String,
    required: true,
    trim: true
  },
  capacity: {
    type: Number,
    required: true,
    min: 1
  },
  description: {
    type: String,
    trim: true
  },
  contactInfo: {
    name: String,
    email: String,
    phone: String
  },
  amenities: [{
    type: String
  }],
  images: [{
    url: String,
    caption: String
  }],
  floorPlan: {
    width: {
      type: Number,
      default: 1000
    },
    height: {
      type: Number,
      default: 800
    },
    elements: [floorPlanElementSchema],
    background: {
      type: String,
      default: null
    }
  },
  event: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Event',
    required: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const Venue = mongoose.model('Venue', venueSchema);

module.exports = Venue;
