const mongoose = require('mongoose');

const attributeSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['Dietary', 'Allergies', 'Accessibility', 'Seating Preference', 'Special Needs', 'Other']
  },
  value: {
    type: String,
    required: true
  }
});

const guestSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email address']
  },
  phone: {
    type: String,
    trim: true
  },
  rsvpStatus: {
    type: String,
    enum: ['Pending', 'Confirmed', 'Declined', 'Maybe'],
    default: 'Pending'
  },
  attributes: [attributeSchema],
  notes: {
    type: String
  },
  event: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Event',
    required: true
  },
  assignedSeat: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Seat'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const Guest = mongoose.model('Guest', guestSchema);

module.exports = Guest;
