const mongoose = require('mongoose');

// Budget item schema for task budgets
const budgetItemSchema = new mongoose.Schema({
  description: {
    type: String,
    required: [true, 'Please add a budget item description'],
    trim: true,
  },
  estimatedAmount: {
    type: Number,
    default: 0,
  },
  actualAmount: {
    type: Number,
    default: 0,
  },
  currency: {
    type: String,
    default: 'USD',
  },
  isPaid: {
    type: Boolean,
    default: false,
  },
  category: {
    type: String,
    trim: true,
    default: 'Other',
  },
  notes: {
    type: String,
    trim: true,
  },
  paidDate: {
    type: Date,
  },
  vendor: {
    type: String,
    trim: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  }
});

// Schema for non-user assignees (people without accounts)
const nonUserAssigneeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    trim: true
  },
  phone: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  },
  isNonUserAssignee: {
    type: Boolean,
    default: true
  }
});

const taskSchema = mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please add a task name'],
      trim: true,
    },
    taskType: {
      type: String,
      required: [true, 'Please add a task type'],
    },
    startTime: {
      type: Date,
    },
    duration: {
      type: String, // Duration in format "hh:mm:ss"
      default: "00:00:00",
    },
    location: {
      type: String,
      trim: true,
    },
    details: {
      type: String,
      trim: true,
    },
    // Keep the original cost field for backward compatibility
    cost: {
      amount: {
        type: Number,
        default: 0,
      },
      currency: {
        type: String,
        default: 'USD',
      },
      isPaid: {
        type: Boolean,
        default: false,
      },
    },
    // Add new budgetItems array for multiple budget items
    budgetItems: [budgetItemSchema],
    // Modified assignees field to support both user references and non-user assignees
    assignees: [{
      // Use a schema-less Mixed type to allow both ObjectIds and objects
      type: mongoose.Schema.Types.Mixed
    }],
    softDeadline: {
      type: Date,
    },
    hardDeadline: {
      type: Date,
    },
    dependencies: [
      {
        type: String, // Task ID
      },
    ],
    // Direct subtasks array - store references to subtask IDs
    subtasks: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Task'
    }],
    // Keep parentTask for backward compatibility, but we primarily use the subtasks array
    // This field indicates if this task is a subtask of another task
    parentTask: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Task',
      default: null
    },
    status: {
      type: String,
      enum: ['Not Started', 'In Progress', 'Completed', 'Delayed', 'Cancelled'],
      default: 'Not Started',
    },
    attachments: [
      {
        filename: String,
        path: String,
        uploadedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    event: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Event',
      required: true,
    },
    reminders: [
      {
        date: Date,
        sent: {
          type: Boolean,
          default: false,
        },
      },
    ],
    // Array of supplies associated with this task
    supplies: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Supply'
    }],
  },
  {
    timestamps: true,
  }
);

// We don't need a virtual for subtasks since we have a real subtasks field
// that directly stores the references to subtask IDs

// Add pre-save hook to prevent circular references
taskSchema.pre('save', async function(next) {
  // If this is a new task or parentTask hasn't changed, proceed
  if (this.isNew || !this.isModified('parentTask') || !this.parentTask) {
    return next();
  }

  // Check for circular references
  const Task = this.constructor;
  let currentParent = this.parentTask;
  const visitedParents = new Set([this._id.toString()]);

  while (currentParent) {
    // If we've seen this parent before, we have a cycle
    if (visitedParents.has(currentParent.toString())) {
      return next(new Error('Circular reference detected in task hierarchy'));
    }

    visitedParents.add(currentParent.toString());

    // Get the parent task
    const parentTask = await Task.findById(currentParent);
    if (!parentTask) break;

    // Move up to the next parent
    currentParent = parentTask.parentTask;
  }

  next();
});

// Set toJSON and toObject options to include virtuals
taskSchema.set('toJSON', { virtuals: true });
taskSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Task', taskSchema);