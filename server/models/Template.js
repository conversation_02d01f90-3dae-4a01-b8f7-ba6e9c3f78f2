const mongoose = require('mongoose');

const templateSchema = mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Please add a template title'],
      trim: true,
    },
    eventType: {
      type: String,
      required: [true, 'Please add an event type'],
      enum: ['Wedding', 'Birthday', 'Corporate', 'Anniversary', 'Other'],
    },
    description: {
      type: String,
      trim: true,
    },
    tasks: [
      {
        name: {
          type: String,
          required: true,
        },
        taskType: {
          type: String,
          required: true,
        },
        details: {
          type: String,
        },
        relativeDeadline: {
          type: Number, // Days relative to event date (positive or negative)
          required: true,
        },
      },
    ],
    creator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model('Template', templateSchema); 