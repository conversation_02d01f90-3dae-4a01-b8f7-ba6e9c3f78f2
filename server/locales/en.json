{"errors": {"auth": {"invalidCredentials": "Invalid email or password", "emailAlreadyExists": "Email already exists", "userNotFound": "User not found", "unauthorized": "Unauthorized access", "tokenExpired": "Token expired, please login again", "invalidToken": "Invalid token"}, "validation": {"required": "{{field}} is required", "invalidEmail": "Invalid email format", "invalidPassword": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match"}, "events": {"notFound": "Event not found", "alreadyExists": "Event with this title already exists", "createFailed": "Failed to create event", "updateFailed": "Failed to update event", "deleteFailed": "Failed to delete event"}, "tasks": {"notFound": "Task not found", "createFailed": "Failed to create task", "updateFailed": "Failed to update task", "deleteFailed": "Failed to delete task"}, "resources": {"notFound": "Resource not found", "createFailed": "Failed to create resource", "updateFailed": "Failed to update resource", "deleteFailed": "Failed to delete resource"}, "server": {"internalError": "Internal server error", "notFound": "Resource not found", "badRequest": "Bad request", "databaseError": "Database error"}}, "success": {"language": {"updated": "Language updated successfully"}, "auth": {"login": "Login successful", "register": "Registration successful", "logout": "Logout successful"}, "events": {"created": "Event created successfully", "updated": "Event updated successfully", "deleted": "Event deleted successfully"}, "tasks": {"created": "Task created successfully", "updated": "Task updated successfully", "deleted": "Task deleted successfully"}, "resources": {"created": "Resource created successfully", "updated": "Resource updated successfully", "deleted": "Resource deleted successfully"}}}