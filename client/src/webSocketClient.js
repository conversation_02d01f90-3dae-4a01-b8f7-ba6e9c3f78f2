// This is a dummy WebSocket client that does nothing
// It's used to prevent errors when the webpack dev server tries to establish a WebSocket connection

class DummyWebSocket {
  constructor() {
    this.readyState = 3; // CLOSED
    this.onclose = null;
    this.onerror = null;
    this.onmessage = null;
    this.onopen = null;
    
    // Immediately trigger the close event
    setTimeout(() => {
      if (this.onclose) {
        this.onclose({ code: 1000, reason: 'Dummy WebSocket closed' });
      }
    }, 0);
  }

  close() {
    // Do nothing
  }

  send() {
    // Do nothing
  }
}

// Override the global WebSocket constructor if DISABLE_WEBSOCKET is true
if (process.env.DISABLE_WEBSOCKET === 'true') {
  window.WebSocket = DummyWebSocket;
}

export default DummyWebSocket;
