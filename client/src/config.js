// Configuration settings for the application
const config = {
  // API URL - change this based on environment
  API_URL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',

  // Feature flags
  USE_LOCAL_STORAGE_FALLBACK: false, // Disabled - always use the database
  ALWAYS_TRY_API_FIRST: true, // Always use the API, no fallback

  // Storage settings
  // IMPORTANT: Do not use localStorage for events, tasks, or assignees
  // Only authentication tokens should be stored in localStorage
  STORE_AUTH_TOKEN_ONLY: true,

  // OAuth settings
  GOOGLE_CLIENT_ID: process.env.REACT_APP_GOOGLE_CLIENT_ID || 'your-google-client-id',

  // Stripe settings
  STRIPE_PUBLISHABLE_KEY: process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_test_your_stripe_test_key',

  // Other settings
  DEFAULT_EVENT_IMAGE: '/images/default-event.jpg',

  // Currency settings
  CURRENCIES: [
    'USD',
    'EUR',
    'GBP',
    'CAD',
    'AUD',
    'HKD',
    'RMB',
    'TWD'
  ],
};

export default config;