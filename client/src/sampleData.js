import moment from 'moment';

// Sample users
export const sampleUsers = [
  {
    _id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123', // Note: This is only for development purposes
    role: 'user'
  },
  {
    _id: 'user-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123', // Note: This is only for development purposes
    role: 'user'
  },
  {
    _id: 'user-3',
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'password123', // Note: This is only for development purposes
    role: 'admin'
  }
];

// Sample templates
export const sampleTemplates = [
  {
    _id: 'template-1',
    name: 'Wedding Standard',
    eventType: 'Wedding',
    description: 'Standard wedding planning template with common tasks',
    tasks: [
      { name: 'Book Venue', taskType: 'Venue', relativeDeadline: -180, details: 'Find and book venue for ceremony and reception' },
      { name: '<PERSON>re Photographer', taskType: 'Photography', relativeDeadline: -150, details: 'Book professional photographer for the event' },
      { name: 'Order Wedding Cake', taskType: 'Catering', relativeDeadline: -90, details: 'Choose bakery and order wedding cake' },
      { name: 'Book Catering', taskType: 'Catering', relativeDeadline: -120, details: 'Select menu and book catering service' },
      { name: 'Send Invitations', taskType: 'Invitation', relativeDeadline: -60, details: 'Design, print and mail invitations' }
    ]
  },
  {
    _id: 'template-2',
    name: 'Birthday Party',
    eventType: 'Birthday',
    description: 'Template for planning birthday celebrations',
    tasks: [
      { name: 'Book Venue', taskType: 'Venue', relativeDeadline: -30, details: 'Find and reserve party venue' },
      { name: 'Order Cake', taskType: 'Catering', relativeDeadline: -14, details: 'Order birthday cake from bakery' },
      { name: 'Send Invitations', taskType: 'Invitation', relativeDeadline: -21, details: 'Create and send party invitations' }
    ]
  },
  {
    _id: 'template-3',
    name: 'Corporate Event',
    eventType: 'Corporate',
    description: 'Template for corporate events and meetings',
    tasks: [
      { name: 'Book Conference Room', taskType: 'Venue', relativeDeadline: -60, details: 'Reserve conference room or event space' },
      { name: 'Arrange Catering', taskType: 'Catering', relativeDeadline: -30, details: 'Select and book catering service' },
      { name: 'Prepare Agenda', taskType: 'Other', relativeDeadline: -21, details: 'Finalize meeting or event agenda' }
    ]
  }
];

// Sample tasks
export const sampleTasks = [
  {
    _id: 'task-1',
    name: 'Book Venue',
    taskType: 'Venue',
    startTime: new Date(2023, 5, 15, 10, 0),
    location: 'Wedding Palace',
    details: 'Need to book the venue for the wedding',
    cost: {
      amount: 5000,
      currency: 'USD',
      isPaid: true
    },
    assignee: {
      _id: 'user-1',
      name: 'John Doe',
      email: '<EMAIL>'
    },
    softDeadline: new Date(2023, 5, 15),
    hardDeadline: new Date(2023, 5, 20),
    dependencies: [],
    status: 'Completed',
    attachments: [],
    event: 'event-1'
  },
  {
    _id: 'task-2',
    name: 'Hire Photographer',
    taskType: 'Vendor',
    startTime: null,
    location: '',
    details: 'Find a professional wedding photographer',
    cost: {
      amount: 1500,
      currency: 'USD',
      isPaid: false
    },
    assignee: {
      _id: 'user-2',
      name: 'Jane Smith',
      email: '<EMAIL>'
    },
    softDeadline: new Date(2023, 6, 10),
    hardDeadline: new Date(2023, 6, 20),
    dependencies: ['task-1'],
    status: 'In Progress',
    attachments: [],
    event: 'event-1'
  },
  {
    _id: 'task-3',
    name: 'Select Catering Menu',
    taskType: 'Food',
    startTime: new Date(2023, 6, 5, 14, 0),
    location: 'Caterer Office',
    details: 'Choose menu items for the reception',
    cost: {
      amount: 3500,
      currency: 'USD',
      isPaid: false
    },
    assignee: null,
    softDeadline: new Date(2023, 6, 25),
    hardDeadline: null,
    dependencies: [],
    status: 'Not Started',
    attachments: [],
    event: 'event-1'
  }
];

// Sample guests
export const sampleGuests = [
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '************',
    rsvpStatus: 'Confirmed',
    attributes: [
      { type: 'Dietary', value: 'Vegetarian' },
      { type: 'Allergies', value: 'Nuts' }
    ]
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '************',
    rsvpStatus: 'Pending',
    attributes: [
      { type: 'Dietary', value: 'Vegan' },
      { type: 'Accessibility', value: 'Wheelchair' }
    ]
  },
  {
    id: 3,
    name: 'Michael Johnson',
    email: '<EMAIL>',
    phone: '************',
    rsvpStatus: 'Declined',
    attributes: [
      { type: 'Dietary', value: 'Gluten-Free' }
    ]
  }
];

// Sample venues
export const sampleVenues = [
  {
    id: 1,
    name: 'Grand Ballroom',
    address: '123 Main St, Anytown, USA',
    capacity: 200,
    description: 'Elegant ballroom with high ceilings and chandeliers',
    image: 'https://via.placeholder.com/300x200?text=Grand+Ballroom',
    floorPlan: null
  },
  {
    id: 2,
    name: 'Garden Terrace',
    address: '456 Park Ave, Anytown, USA',
    capacity: 150,
    description: 'Beautiful outdoor venue with garden views',
    image: 'https://via.placeholder.com/300x200?text=Garden+Terrace',
    floorPlan: null
  },
  {
    id: 3,
    name: 'Conference Center',
    address: '789 Business Blvd, Anytown, USA',
    capacity: 300,
    description: 'Modern conference center with state-of-the-art facilities',
    image: 'https://via.placeholder.com/300x200?text=Conference+Center',
    floorPlan: null
  }
];