import api from './api';

/**
 * Fetch all tasks for an event
 * @param {string} eventId - The ID of the event to fetch tasks for
 * @returns {Promise<Array>} - A promise that resolves to an array of tasks
 */
export const fetchTasks = async (eventId) => {
  try {
    // Always use the real database API call
    console.log('Fetching tasks from database for event:', eventId);
    const response = await api.get(`/tasks?eventId=${eventId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching tasks:', error);
    throw error; // Don't fallback to mock data, let the caller handle the error
  }
};

/**
 * Fetch a single task by ID
 * @param {string} taskId - The ID of the task to fetch
 * @returns {Promise<Object>} - A promise that resolves to the task object
 */
export const fetchTask = async (taskId) => {
  try {
    console.log(`Fetching task with ID: ${taskId}`);
    const response = await api.get(`/tasks/${taskId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching task:', error);
    throw error;
  }
};

/**
 * Create a new task
 * @param {Object} taskData - The task data
 * @returns {Promise<Object>} - A promise that resolves to the created task
 */
export const createTask = async (eventId, taskData) => {
  try {
    // Ensure the task has the eventId set
    const taskWithEvent = {
      ...taskData,
      event: eventId || taskData.event
    };

    // Ensure childTasks is included in the request payload
    if (!taskWithEvent.childTasks) {
      taskWithEvent.childTasks = [];
    } else if (Array.isArray(taskWithEvent.childTasks)) {
      // Make sure we only have string IDs in the childTasks array
      taskWithEvent.childTasks = taskWithEvent.childTasks
        .filter(id => id && typeof id === 'string');
    }
    console.log('Child tasks in create request payload:', taskWithEvent.childTasks);

    // Ensure supplies is included in the request payload
    if (!taskWithEvent.supplies) {
      taskWithEvent.supplies = [];
    } else if (Array.isArray(taskWithEvent.supplies)) {
      // Make sure we only have string IDs in the supplies array
      taskWithEvent.supplies = taskWithEvent.supplies
        .filter(id => id && typeof id === 'string')
        .map(id => id.toString()); // Ensure all IDs are strings
    }
    console.log('Supplies in create request payload:', taskWithEvent.supplies);

    console.log('Attempting to save task to server:', taskWithEvent);

    // Ensure user is authenticated
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('No authentication token found');
      throw new Error('Authentication required');
    }

    // Always save directly to the server, no local storage fallback
    const response = await api.post('/tasks', taskWithEvent, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('Task successfully saved to server:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error creating task:', error.response?.data || error.message);
    throw error;
  }
};

// We don't use localStorage for tasks - all tasks must be stored in the database
// Only authentication tokens are stored in localStorage

/**
 * Update an existing task
 * @param {string} taskId - The ID of the task to update
 * @param {Object} taskData - The updated task data
 * @returns {Promise<Object>} - A promise that resolves to the updated task
 */
export const updateTask = async (taskId, taskData) => {
  try {
    console.log(`Updating task with ID: ${taskId}`);
    console.log('Task data being sent to server:', JSON.stringify(taskData, null, 2));

    // Create a clean copy of the data to send to the server
    const dataToSend = { ...taskData };

    // Remove the _id from the payload since it's in the URL
    delete dataToSend._id;

    // Ensure subtasks is properly formatted as string IDs only
    if (dataToSend.subtasks) {
      // Make sure we only have string IDs in the subtasks array
      dataToSend.subtasks = dataToSend.subtasks
        .filter(id => id)
        .map(id => typeof id === 'string' ? id.toString() : (id._id ? id._id.toString() : id.toString()));
    }

    // Ensure subtasks is included in the request payload
    if (!dataToSend.subtasks) {
      dataToSend.subtasks = [];
    } else if (Array.isArray(dataToSend.subtasks)) {
      // Make sure we only have string IDs in the subtasks array
      dataToSend.subtasks = dataToSend.subtasks
        .filter(id => id && typeof id === 'string')
        .map(id => id.toString()); // Ensure all IDs are strings
    }
    console.log('Subtasks in request payload:', dataToSend.subtasks);

    // Ensure supplies is properly formatted as string IDs only
    if (dataToSend.supplies) {
      // Make sure we only have string IDs in the supplies array
      dataToSend.supplies = dataToSend.supplies
        .filter(id => id)
        .map(id => typeof id === 'string' ? id.toString() : (id._id ? id._id.toString() : id.toString()));
    }

    // Ensure supplies is included in the request payload
    if (!dataToSend.supplies) {
      dataToSend.supplies = [];
    } else if (Array.isArray(dataToSend.supplies)) {
      // Make sure we only have string IDs in the supplies array
      dataToSend.supplies = dataToSend.supplies
        .filter(id => id && typeof id === 'string')
        .map(id => id.toString()); // Ensure all IDs are strings
    }
    console.log('Supplies in request payload:', dataToSend.supplies);

    // Log the HTTP method and URL
    console.log('HTTP Method: PUT');
    console.log('API URL:', `/tasks/${taskId}`);
    console.log('Request payload:', JSON.stringify(dataToSend, null, 2));

    // Use the api instance which has proper error handling
    const response = await api.put(`/tasks/${taskId}`, dataToSend);

    console.log('Task update successful, received:', JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error('Error updating task:', error);
    console.error('Error details:', error.response ? error.response.data : 'No response data');
    throw error;
  }
};

/**
 * Delete a task
 * @param {string} taskId - The ID of the task to delete
 * @returns {Promise<Object>} - A promise that resolves to the deleted task
 */
export const deleteTask = async (taskId) => {
  const response = await api.delete(`/tasks/${taskId}`);
  return response.data;
};

/**
 * Delete multiple tasks
 * @param {string[]} taskIds - Array of task IDs to delete
 * @returns {Promise<Object>} - A promise that resolves to the deletion result
 */
export const batchDeleteTasks = async (taskIds) => {
  try {
    console.log(`Batch deleting ${taskIds.length} tasks`);
    // Use a POST request to the batch-delete endpoint
    const response = await api.post('/tasks/batch-delete', {
      taskIds
    });
    console.log('Batch delete response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error batch deleting tasks:', error);
    console.error('Error details:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Toggle task completion status
 * @param {string} taskId - The ID of the task to toggle
 * @returns {Promise<Object>} - A promise that resolves to the updated task
 */
export const toggleTaskCompletion = async (taskId) => {
  const response = await api.patch(`/tasks/${taskId}/toggle`);
  return response.data;
};

/**
 * Assign a task to a user
 * @param {string} taskId - The ID of the task to assign
 * @param {string} userId - The ID of the user to assign the task to
 * @returns {Promise<Object>} - A promise that resolves to the updated task
 */
export const assignTask = async (taskId, userId) => {
  const response = await api.patch(`/tasks/${taskId}/assign`, { userId });
  return response.data;
};

/**
 * Fetch tasks for calendar view with date range filtering
 * @param {Object} params - Query parameters
 * @param {string} params.startDate - Start date for the range (ISO string)
 * @param {string} params.endDate - End date for the range (ISO string)
 * @param {string} [params.eventId] - Optional event ID to filter tasks
 * @returns {Promise<Array>} - A promise that resolves to an array of tasks
 */
export const fetchTasksForCalendar = async ({ startDate, endDate, eventId }) => {
  try {
    console.log(`Fetching calendar tasks from ${startDate} to ${endDate}`);

    // Build query string
    let queryString = `startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`;
    if (eventId) {
      queryString += `&eventId=${encodeURIComponent(eventId)}`;
    }

    const response = await api.get(`/tasks/calendar?${queryString}`);
    console.log(`Retrieved ${response.data.length} tasks for calendar view`);
    return response.data;
  } catch (error) {
    console.error('Error fetching calendar tasks:', error);
    throw error;
  }
};

/**
 * Get mock tasks for development/testing
 * @param {string} eventId - The ID of the event to get tasks for
 * @returns {Array} - An array of mock tasks
 */
const getMockTasks = (eventId) => {
  return [
    {
      _id: 'task-1',
      name: 'Book Venue',
      eventId: eventId,
      status: 'completed',
      taskType: 'booking',
      priority: 'high',
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      details: 'Contact venue and confirm booking details',
      budget: { amount: 5000, currency: 'USD' },
      assignee: null,
      dependencies: [],
      attachments: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      _id: 'task-2',
      name: 'Send Invitations',
      eventId: eventId,
      status: 'in-progress',
      taskType: 'communication',
      priority: 'medium',
      deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      details: 'Design and send invitations to all guests',
      budget: { amount: 200, currency: 'USD' },
      assignee: null,
      dependencies: ['task-1'],
      attachments: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      _id: 'task-3',
      name: 'Arrange Catering',
      eventId: eventId,
      status: 'not-started',
      taskType: 'booking',
      priority: 'high',
      deadline: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString(),
      details: 'Contact caterers and finalize menu',
      budget: { amount: 3000, currency: 'USD' },
      assignee: null,
      dependencies: ['task-1'],
      attachments: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];
};

/**
 * Export tasks for an event
 * @param {string} eventId - The ID of the event to export tasks for
 * @returns {Promise<Array>} - A promise that resolves to an array of tasks
 */
export const exportTasks = async (eventId) => {
  try {
    console.log(`Exporting tasks for event: ${eventId}`);
    const response = await api.get(`/tasks/export/${eventId}`);
    return response.data;
  } catch (error) {
    console.error('Error exporting tasks:', error);
    throw error;
  }
};

/**
 * Import tasks for an event
 * @param {string} eventId - The ID of the event to import tasks for
 * @param {Array} tasks - Array of task objects to import
 * @returns {Promise<Object>} - A promise that resolves to the import result
 */
export const importTasks = async (eventId, tasks) => {
  try {
    console.log(`Importing ${tasks.length} tasks for event: ${eventId}`);
    const response = await api.post(`/tasks/import/${eventId}`, { tasks });
    return response.data;
  } catch (error) {
    console.error('Error importing tasks:', error);
    throw error;
  }
};

/**
 * Get task schema for import
 * @returns {Promise<Object>} - A promise that resolves to the task schema
 */
export const getTaskSchema = async () => {
  try {
    const response = await api.get('/tasks/schema');
    return response.data;
  } catch (error) {
    console.error('Error getting task schema:', error);
    throw error;
  }
};

/**
 * Get task statistics for an event
 * @param {string} eventId - The ID of the event to get statistics for
 * @returns {Promise<Object>} - A promise that resolves to task statistics
 */
export const getTaskStatistics = async (eventId) => {
  try {
    console.log(`Getting task statistics for event: ${eventId}`);
    // Fetch all tasks for the event
    const tasks = await fetchTasks(eventId);

    // Initialize counters
    const statistics = {
      total: tasks.length,
      notStarted: 0,
      inProgress: 0,
      completed: 0,
      delayed: 0,
      cancelled: 0
    };

    // Count tasks by status
    tasks.forEach(task => {
      switch(task.status) {
        case 'Not Started':
          statistics.notStarted++;
          break;
        case 'In Progress':
          statistics.inProgress++;
          break;
        case 'Completed':
          statistics.completed++;
          break;
        case 'Delayed':
          statistics.delayed++;
          break;
        case 'Cancelled':
          statistics.cancelled++;
          break;
        default:
          // Handle unknown status
          console.warn(`Unknown task status: ${task.status}`);
      }
    });

    // Calculate percentages
    if (statistics.total > 0) {
      statistics.percentNotStarted = (statistics.notStarted / statistics.total) * 100;
      statistics.percentInProgress = (statistics.inProgress / statistics.total) * 100;
      statistics.percentCompleted = (statistics.completed / statistics.total) * 100;
      statistics.percentDelayed = (statistics.delayed / statistics.total) * 100;
      statistics.percentCancelled = (statistics.cancelled / statistics.total) * 100;

      // Calculate overall progress (completed tasks / total active tasks)
      const activeTasks = statistics.total - statistics.cancelled;
      statistics.progress = activeTasks > 0 ? (statistics.completed / activeTasks) * 100 : 0;
    } else {
      // No tasks
      statistics.percentNotStarted = 0;
      statistics.percentInProgress = 0;
      statistics.percentCompleted = 0;
      statistics.percentDelayed = 0;
      statistics.percentCancelled = 0;
      statistics.progress = 0;
    }

    return statistics;
  } catch (error) {
    console.error('Error getting task statistics:', error);
    throw error;
  }
};

/**
 * We no longer use localStorage for task synchronization
 * All tasks must be directly saved to the database
 * Only authentication tokens are stored in localStorage
 */

// No offline sync functionality - all operations must succeed or fail immediately
// This ensures we're not storing task data in localStorage

// Make sure to export both named exports and default export
export default {
  fetchTasks,
  fetchTask,
  createTask,
  updateTask,
  deleteTask,
  batchDeleteTasks,
  toggleTaskCompletion,
  assignTask,
  exportTasks,
  importTasks,
  getTaskSchema,
  getTaskStatistics
};
