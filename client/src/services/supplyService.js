import api from './api';

/**
 * Fetch all supplies for an event
 * @param {string} eventId - The ID of the event to fetch supplies for
 * @returns {Promise<Array>} - A promise that resolves to an array of supplies
 */
export const fetchSupplies = async (eventId) => {
  try {
    console.log('Fetching supplies from database for event:', eventId);
    const response = await api.get(`/resources/supplies?eventId=${eventId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching supplies:', error);
    throw error;
  }
};

/**
 * Fetch a single supply by ID
 * @param {string} supplyId - The ID of the supply to fetch
 * @returns {Promise<Object>} - A promise that resolves to the supply object
 */
export const fetchSupply = async (supplyId) => {
  try {
    const response = await api.get(`/resources/supplies/${supplyId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching supply:', error);
    throw error;
  }
};

/**
 * Create a new supply
 * @param {Object} supplyData - The supply data
 * @returns {Promise<Object>} - A promise that resolves to the created supply
 */
export const createSupply = async (supplyData) => {
  try {
    console.log('Attempting to save supply to server:', supplyData);
    const response = await api.post('/resources/supplies', supplyData);
    console.log('Supply successfully saved to server:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error creating supply:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Update an existing supply
 * @param {string} supplyId - The ID of the supply to update
 * @param {Object} supplyData - The updated supply data
 * @returns {Promise<Object>} - A promise that resolves to the updated supply
 */
export const updateSupply = async (supplyId, supplyData) => {
  try {
    console.log(`Updating supply ${supplyId} with data:`, supplyData);
    const response = await api.put(`/resources/supplies/${supplyId}`, supplyData);
    console.log('Supply successfully updated:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating supply:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Delete a supply
 * @param {string} supplyId - The ID of the supply to delete
 * @returns {Promise<Object>} - A promise that resolves to the response data
 */
export const deleteSupply = async (supplyId) => {
  try {
    const response = await api.delete(`/resources/supplies/${supplyId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting supply:', error);
    throw error;
  }
};

/**
 * Add a supply to a task
 * @param {string} supplyId - The ID of the supply
 * @param {string} taskId - The ID of the task
 * @returns {Promise<Object>} - A promise that resolves to the updated supply
 */
export const addSupplyToTask = async (supplyId, taskId) => {
  try {
    const response = await api.post(`/resources/supplies/${supplyId}/tasks/${taskId}`);
    return response.data;
  } catch (error) {
    console.error('Error adding supply to task:', error);
    throw error;
  }
};

/**
 * Remove a supply from a task
 * @param {string} supplyId - The ID of the supply
 * @param {string} taskId - The ID of the task
 * @returns {Promise<Object>} - A promise that resolves to the updated supply
 */
export const removeSupplyFromTask = async (supplyId, taskId) => {
  try {
    const response = await api.delete(`/resources/supplies/${supplyId}/tasks/${taskId}`);
    return response.data;
  } catch (error) {
    console.error('Error removing supply from task:', error);
    throw error;
  }
};

// Make sure to export both named exports and default export
export default {
  fetchSupplies,
  fetchSupply,
  createSupply,
  updateSupply,
  deleteSupply,
  addSupplyToTask,
  removeSupplyFromTask
};
