import api from './api';
import config from '../config';

// Helper: Get sample events (only used when API is unavailable)
// This is a fallback mechanism that doesn't use localStorage


// Helper: Get default sample events
const getSampleEvents = () => {
  return [
    {
      _id: 'sample-event-1',
      title: 'Sample Wedding Event',
      eventType: 'Wedding',
      date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days in future
      description: 'Sample wedding event for development',
      venue: {
        name: 'Sample Venue',
        address: '123 Sample St, Example City'
      },
      budget: 15000,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: 'sample-event-2',
      title: 'Sample Birthday Party',
      eventType: 'Birthday',
      date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days in future
      description: 'Sample birthday event for development',
      venue: {
        name: 'Home',
        address: '456 Home Ave, Example Town'
      },
      budget: 500,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
};

export const fetchEvents = async () => {
  try {
    const response = await api.get('/events');
    return response.data;
  } catch (error) {
    console.error('Error fetching events:', error);
    throw error;
  }
};

export const fetchEventById = async (eventId) => {
  try {
    const response = await api.get(`/events/${eventId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching event ${eventId}:`, error);
    throw error;
  }
};

export const createEvent = async (eventData) => {
  try {
    const response = await api.post('/events', eventData);
    return response.data;
  } catch (error) {
    console.error('Error creating event:', error);
    throw error;
  }
};

export const updateEvent = async (eventId, eventData) => {
  try {
    const response = await api.put(`/events/${eventId}`, eventData);
    return response.data;
  } catch (error) {
    console.error(`Error updating event ${eventId}:`, error);
    throw error;
  }
};

export const deleteEvent = async (eventId) => {
  try {
    const response = await api.delete(`/events/${eventId}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting event ${eventId}:`, error);
    throw error;
  }
};

// Invitation related methods
export const inviteUser = async (eventId, userData) => {
  try {
    const response = await api.post(`/events/${eventId}/invitations`, userData);
    return response.data;
  } catch (error) {
    console.error(`Error inviting user to event ${eventId}:`, error);
    throw error;
  }
};

export const respondToInvitation = async (eventId, invitationId, status) => {
  try {
    const response = await api.put(`/events/${eventId}/invitations/${invitationId}`, { status });
    return response.data;
  } catch (error) {
    console.error(`Error responding to invitation:`, error);
    throw error;
  }
};

export const getEventInvitations = async (eventId) => {
  try {
    const response = await api.get(`/events/${eventId}/invitations`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching invitations for event ${eventId}:`, error);
    throw error;
  }
};

export const getUserPendingInvitations = async () => {
  try {
    const response = await api.get('/events/invitations/pending');
    return response.data;
  } catch (error) {
    console.error('Error fetching pending invitations:', error);
    throw error;
  }
};

export const cancelInvitation = async (eventId, invitationId) => {
  try {
    const response = await api.delete(`/events/${eventId}/invitations/${invitationId}`);
    return response.data;
  } catch (error) {
    console.error(`Error canceling invitation:`, error);
    throw error;
  }
}; 