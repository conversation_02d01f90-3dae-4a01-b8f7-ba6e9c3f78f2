import api from './api';

/**
 * Fetch all event templates
 * @returns {Promise<Array>} - A promise that resolves to an array of templates
 */
export const fetchTemplates = async () => {
  try {
    const response = await api.get('/templates');
    return response.data;
  } catch (error) {
    console.error('Error fetching templates:', error);
    throw error;
  }
};

/**
 * Fetch a template by ID
 * @param {string} templateId - The ID of the template to fetch
 * @returns {Promise<Object>} - A promise that resolves to the template
 */
export const fetchTemplateById = async (templateId) => {
  try {
    const response = await api.get(`/templates/${templateId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching template ${templateId}:`, error);
    throw error;
  }
};

/**
 * Create a new template
 * @param {Object} templateData - The template data
 * @returns {Promise<Object>} - A promise that resolves to the created template
 */
export const createTemplate = async (templateData) => {
  try {
    const response = await api.post('/templates', templateData);
    return response.data;
  } catch (error) {
    console.error('Error creating template:', error);
    throw error;
  }
};

/**
 * Update an existing template
 * @param {string} templateId - The ID of the template to update
 * @param {Object} templateData - The updated template data
 * @returns {Promise<Object>} - A promise that resolves to the updated template
 */
export const updateTemplate = async (templateId, templateData) => {
  try {
    const response = await api.put(`/templates/${templateId}`, templateData);
    return response.data;
  } catch (error) {
    console.error(`Error updating template ${templateId}:`, error);
    throw error;
  }
};

/**
 * Delete a template
 * @param {string} templateId - The ID of the template to delete
 * @returns {Promise<Object>} - A promise that resolves to the deleted template
 */
export const deleteTemplate = async (templateId) => {
  try {
    const response = await api.delete(`/templates/${templateId}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting template ${templateId}:`, error);
    throw error;
  }
};

// Make sure to export both named exports and default export
export default {
  fetchTemplates,
  fetchTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate
};
