// Service for handling resources (guests, venues, stakeholders) API calls

// Sample data for development
import { sampleGuests, sampleVenues } from '../sampleData';

// Sample stakeholders data for development
const sampleStakeholders = [
  {
    _id: '1',
    name: 'ABC Corporation',
    email: '<EMAIL>',
    phone: '************',
    address: '123 Business St, Business City, BC 12345',
    website: 'www.abccorp.com',
    contactPoints: [
      {
        name: '<PERSON>',
        role: 'Event Coordinator',
        email: '<EMAIL>',
        phone: '************',
        notes: 'Primary contact for event logistics'
      },
      {
        name: '<PERSON>',
        role: 'Marketing Director',
        email: '<EMAIL>',
        phone: '************',
        notes: 'Handles promotional materials'
      }
    ],
    notes: 'Main sponsor for the event'
  },
  {
    _id: '2',
    name: 'XYZ Industries',
    email: '<EMAIL>',
    phone: '************',
    address: '456 Corporate Ave, Business Park, BP 67890',
    website: 'www.xyzindustries.com',
    contactPoints: [
      {
        name: '<PERSON>',
        role: 'CEO',
        email: '<EMAIL>',
        phone: '************',
        notes: 'Decision maker for sponsorship'
      }
    ],
    notes: 'Potential sponsor, needs follow-up'
  }
];

// Guest Management API functions
export const fetchGuests = async (eventId) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/guests`).then(res => res.json());

  // For now, return sample data
  return Promise.resolve(sampleGuests || []);
};

export const createGuest = async (eventId, guestData) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/guests`, {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(guestData)
  // }).then(res => res.json());

  // For now, return the data with a new ID
  return Promise.resolve({
    ...guestData,
    id: Math.floor(Math.random() * 10000)
  });
};

export const updateGuest = async (eventId, guestId, guestData) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/guests/${guestId}`, {
  //   method: 'PUT',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(guestData)
  // }).then(res => res.json());

  // For now, return the updated data
  return Promise.resolve({
    ...guestData,
    id: guestId
  });
};

export const deleteGuest = async (eventId, guestId) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/guests/${guestId}`, {
  //   method: 'DELETE'
  // }).then(res => res.json());

  // For now, return success
  return Promise.resolve({ success: true });
};

// Venue Management API functions
export const fetchVenues = async (eventId) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/venues`).then(res => res.json());

  // For now, return sample data
  return Promise.resolve(sampleVenues || []);
};

export const createVenue = async (eventId, venueData) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/venues`, {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(venueData)
  // }).then(res => res.json());

  // For now, return the data with a new ID
  return Promise.resolve({
    ...venueData,
    id: Math.floor(Math.random() * 10000)
  });
};

export const updateVenue = async (eventId, venueId, venueData) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/venues/${venueId}`, {
  //   method: 'PUT',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(venueData)
  // }).then(res => res.json());

  // For now, return the updated data
  return Promise.resolve({
    ...venueData,
    id: venueId
  });
};

export const deleteVenue = async (eventId, venueId) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/venues/${venueId}`, {
  //   method: 'DELETE'
  // }).then(res => res.json());

  // For now, return success
  return Promise.resolve({ success: true });
};

// Floor Plan Management API functions
export const saveFloorPlan = async (eventId, venueId, floorPlanData) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/venues/${venueId}/floorplan`, {
  //   method: 'PUT',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(floorPlanData)
  // }).then(res => res.json());

  // For now, return success
  return Promise.resolve({
    success: true,
    floorPlan: floorPlanData
  });
};

export const assignGuestToSeat = async (eventId, venueId, seatId, guestId) => {
  // In a real app, this would make an API call
  // Example: return fetch(`/api/events/${eventId}/venues/${venueId}/seats/${seatId}/assign`, {
  //   method: 'PUT',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify({ guestId })
  // }).then(res => res.json());

  // For now, return success
  return Promise.resolve({
    success: true,
    seatId,
    guestId
  });
};

// Stakeholder Management API functions
export const fetchStakeholders = async (eventId) => {
  try {
    const token = localStorage.getItem('token');
    console.log('Fetching stakeholders for event:', eventId);

    const response = await fetch(`/api/resources/stakeholders?eventId=${eventId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Fetch response status:', response.status);

    if (!response.ok) {
      let errorMessage = `Failed to fetch stakeholders: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        try {
          const text = await response.text();
          if (text) errorMessage += ` - ${text}`;
        } catch (textError) {
          console.error('Could not parse error response:', textError);
        }
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();
    console.log('Fetched stakeholders:', data);
    return data;
  } catch (error) {
    console.error('Error fetching stakeholders:', error);
    // Fallback to sample data if API fails
    console.log('Falling back to sample stakeholder data');
    return sampleStakeholders || [];
  }
};

export const createStakeholder = async (eventId, stakeholderData) => {
  try {
    const token = localStorage.getItem('token');
    console.log('Creating stakeholder with data:', { ...stakeholderData, eventId });

    // Use the development route for testing
    const useDevRoute = true; // Set to false in production
    const endpoint = useDevRoute ? '/api/resources/stakeholders/dev-create' : '/api/resources/stakeholders';

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': useDevRoute ? '' : `Bearer ${token}`
      },
      body: JSON.stringify({ ...stakeholderData, eventId })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', [...response.headers.entries()]);

    if (!response.ok) {
      let errorMessage = `Failed to create stakeholder: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        try {
          const text = await response.text();
          if (text) errorMessage += ` - ${text}`;
        } catch (textError) {
          console.error('Could not parse error response:', textError);
        }
      }
      throw new Error(errorMessage);
    }

    const createdStakeholder = await response.json();
    console.log('Stakeholder created successfully:', createdStakeholder);
    return createdStakeholder;
  } catch (error) {
    console.error('Error creating stakeholder:', error);
    throw error;
  }
};

export const updateStakeholder = async (stakeholderId, stakeholderData) => {
  try {
    const token = localStorage.getItem('token');
    console.log('Updating stakeholder with data:', { id: stakeholderId, ...stakeholderData });

    // Use the development route for testing
    const useDevRoute = true; // Set to false in production
    const endpoint = useDevRoute
      ? `/api/resources/stakeholders/dev-update/${stakeholderId}`
      : `/api/resources/stakeholders/${stakeholderId}`;

    const response = await fetch(endpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': useDevRoute ? '' : `Bearer ${token}`
      },
      body: JSON.stringify(stakeholderData)
    });

    if (!response.ok) {
      let errorMessage = `Failed to update stakeholder: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        try {
          const text = await response.text();
          if (text) errorMessage += ` - ${text}`;
        } catch (textError) {
          console.error('Could not parse error response:', textError);
        }
      }
      throw new Error(errorMessage);
    }

    const updatedStakeholder = await response.json();
    console.log('Stakeholder updated successfully:', updatedStakeholder);
    return updatedStakeholder;
  } catch (error) {
    console.error('Error updating stakeholder:', error);
    throw error;
  }
};

export const deleteStakeholder = async (stakeholderId) => {
  try {
    const token = localStorage.getItem('token');
    console.log('Deleting stakeholder:', stakeholderId);

    // Use the development route for testing
    const useDevRoute = true; // Set to false in production
    const endpoint = useDevRoute
      ? `/api/resources/stakeholders/dev-delete/${stakeholderId}`
      : `/api/resources/stakeholders/${stakeholderId}`;

    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': useDevRoute ? '' : `Bearer ${token}`
      }
    });

    if (!response.ok) {
      let errorMessage = `Failed to delete stakeholder: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        try {
          const text = await response.text();
          if (text) errorMessage += ` - ${text}`;
        } catch (textError) {
          console.error('Could not parse error response:', textError);
        }
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    console.log('Stakeholder deleted successfully:', result);
    return result;
  } catch (error) {
    console.error('Error deleting stakeholder:', error);
    throw error;
  }
};
