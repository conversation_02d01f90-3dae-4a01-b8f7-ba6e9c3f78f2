import api from './api';
import i18n from '../i18n';

/**
 * Get the current language from the server
 * @returns {Promise<Object>} The current language and available languages
 */
export const getCurrentLanguage = async () => {
  try {
    const response = await api.get('/language');
    return response.data;
  } catch (error) {
    console.error('Error getting current language:', error);
    return { language: i18n.language, availableLanguages: ['en', 'zh-TW'] };
  }
};

/**
 * Set the language for guest users (cookie-based)
 * @param {string} language The language code to set
 * @returns {Promise<Object>} The result of the operation
 */
export const setLanguage = async (language) => {
  try {
    // Update the language on the server (don't await to make it non-blocking)
    const responsePromise = api.post('/language', { language });

    // Return a promise that resolves when the server request completes
    return responsePromise.then(response => response.data).catch(error => {
      console.error('Error setting language:', error);
      return {
        message: 'Language updated locally only',
        language: language
      };
    });
  } catch (error) {
    console.error('Error setting language:', error);
    return {
      message: 'Language updated locally only',
      language: language
    };
  }
};

/**
 * Set the language for authenticated users (persisted in database)
 * @param {string} language The language code to set
 * @returns {Promise<Object>} The result of the operation
 */
export const setUserLanguage = async (language) => {
  try {
    // Update the language on the server (user-specific endpoint) - non-blocking
    const responsePromise = api.post('/language/user', { language });

    // Return a promise that resolves when the server request completes
    return responsePromise.then(response => response.data).catch(error => {
      console.error('Error setting user language:', error);

      // If unauthorized, fall back to cookie-based language setting
      if (error.response && error.response.status === 401) {
        return setLanguage(language);
      }

      return {
        message: 'Language updated locally only',
        language: language
      };
    });
  } catch (error) {
    console.error('Error setting user language:', error);
    return {
      message: 'Language updated locally only',
      language: language
    };
  }
};

/**
 * Set the language based on authentication status
 * @param {string} language The language code to set
 * @param {boolean} isAuthenticated Whether the user is authenticated
 * @returns {Promise<Object>} The result of the operation
 */
export const setLanguageBasedOnAuth = async (language, isAuthenticated) => {
  if (isAuthenticated) {
    return setUserLanguage(language);
  } else {
    return setLanguage(language);
  }
};
