import axios from 'axios';
import config from '../config';

// Create axios instance with default config
const api = axios.create({
  baseURL: config.API_URL,
  headers: {
    'Content-Type': 'application/json',
    // Add connection keep-alive header for browser requests
    'Connection': 'keep-alive'
  },
  // Increase timeout to prevent quick failures
  timeout: 30000, // 30 seconds (increased from 15)
});

// Track if we already redirected to prevent refresh loops
let redirecting = false;

// Add request interceptor to include auth token in requests
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', {
      url: config.url,
      method: config.method.toUpperCase(),
      baseURL: config.baseURL,
      fullUrl: `${config.baseURL}${config.url}`,
      data: config.data ? JSON.parse(JSON.stringify(config.data)) : undefined
    });

    // Ensure method is correctly set
    if (config.url.includes('/tasks/') && config.url.split('/').length === 3 && config.data && !config.url.includes('batch-delete')) {
      console.log('Task update detected, ensuring method is PUT');
      config.method = 'put';
    }

    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Track failed requests for retry
const failedQueue = [];
let isRefreshing = false;

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log('Response successful from:', response.config.url);
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle connection errors
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout:', error.config?.url);
      // Retry timeout errors automatically
      if (!originalRequest._retry) {
        originalRequest._retry = true;
        console.log('Retrying after timeout:', originalRequest.url);
        return api(originalRequest);
      }
    } else if (error.code === 'ECONNRESET') {
      console.error('Connection reset by server:', error.config?.url);
      // Retry ECONNRESET errors with exponential backoff
      if (!originalRequest._resetRetry) {
        originalRequest._resetRetry = 0;
      }
      
      if (originalRequest._resetRetry < 3) { // Max 3 retries for ECONNRESET
        originalRequest._resetRetry++;
        const backoffDelay = Math.pow(2, originalRequest._resetRetry) * 1000; // Exponential backoff
        console.log(`Retrying after connection reset (attempt ${originalRequest._resetRetry}). Waiting ${backoffDelay}ms`);
        
        return new Promise(resolve => {
          setTimeout(() => {
            resolve(api(originalRequest));
          }, backoffDelay);
        });
      } else {
        // Store for later retry if max retries reached
        if (originalRequest.method !== 'get') {
          console.log('Max retries reached for ECONNRESET, queueing for later retry:', originalRequest.url);
          storeFailedRequest(originalRequest);
        }
      }
    } else if (error.code === 'ERR_NETWORK') {
      console.error('Network error - server may be down:', error.config?.url);
      // Trigger custom event for network error
      window.dispatchEvent(new CustomEvent('api:networkerror'));

      // Store the failed request for later retry
      if (!originalRequest._retry && originalRequest.method !== 'get') {
        console.log('Queueing request for later retry:', originalRequest.url);
        // Store non-GET requests for later retry
        storeFailedRequest(originalRequest);
      }
    } else {
      console.error('API request failed:', error.config?.url, error.message);
    }

    // Handle authentication errors without continuous redirects
    if (error.response && error.response.status === 401) {
      console.log('Auth error on URL:', error.config?.url);

      // Check if it's a login/register request that failed - don't redirect in this case
      const authPaths = ['/users/login', '/users/register'];
      const isAuthPath = authPaths.some(path => error.config?.url?.includes(path));

      if (!isAuthPath && !redirecting) {
        console.log('Authentication required, clearing user data');

        // Set the flag to prevent multiple redirects
        redirecting = true;

        // Clear user data
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Only redirect if we're not already on the login page
        if (!window.location.pathname.includes('/login')) {
          console.log('Redirecting to login page');
          setTimeout(() => {
            window.location.href = '/login';
            // Reset redirect flag after a delay
            setTimeout(() => {
              redirecting = false;
            }, 1000);
          }, 100);
        } else {
          // Already on login page, just reset the flag
          redirecting = false;
        }
      }
    }

    return Promise.reject(error);
  }
);

/**
 * Store a failed request for later retry
 * @param {Object} request - The failed request config
 */
const storeFailedRequest = (request) => {
  const storedRequests = JSON.parse(localStorage.getItem('failedApiRequests') || '[]');

  // Don't store duplicates
  const isDuplicate = storedRequests.some(req =>
    req.url === request.url &&
    req.method === request.method &&
    JSON.stringify(req.data) === JSON.stringify(request.data)
  );

  if (!isDuplicate) {
    storedRequests.push({
      url: request.url,
      method: request.method,
      data: request.data,
      headers: request.headers,
      timestamp: Date.now(),
      retryCount: 0
    });
    localStorage.setItem('failedApiRequests', JSON.stringify(storedRequests));
  }
};

/**
 * Retry all failed requests
 * @returns {Promise<Array>} - Results of retry attempts
 */
export const retryFailedRequests = async () => {
  const storedRequests = JSON.parse(localStorage.getItem('failedApiRequests') || '[]');
  if (storedRequests.length === 0) return [];

  console.log(`Attempting to retry ${storedRequests.length} failed requests`);

  const results = [];
  const remainingRequests = [];

  for (const request of storedRequests) {
    // Skip if retried too many times
    if (request.retryCount >= 5) continue;

    try {
      // Recreate the request
      const result = await api({
        url: request.url,
        method: request.method,
        data: request.data,
        headers: request.headers
      });

      results.push({
        success: true,
        url: request.url,
        data: result.data
      });
    } catch (error) {
      // Keep in queue with increased retry count
      request.retryCount++;
      remainingRequests.push(request);

      results.push({
        success: false,
        url: request.url,
        error: error.message
      });
    }
  }

  // Update the stored requests
  localStorage.setItem('failedApiRequests', JSON.stringify(remainingRequests));

  return results;
};

// Set up periodic retry of failed requests
setInterval(() => {
  // Only retry if we're online
  if (navigator.onLine) {
    retryFailedRequests();
  }
}, 60000); // Try every minute

// Also retry when coming back online
window.addEventListener('online', () => {
  console.log('Back online, retrying failed requests');
  retryFailedRequests();
});

export default api;