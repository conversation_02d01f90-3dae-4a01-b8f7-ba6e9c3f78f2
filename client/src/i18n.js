import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translations directly
import enTranslation from './translations/en.json';
import zhTWTranslation from './translations/zh-TW.json';

i18n
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    // Default language
    fallbackLng: 'zh-TW',
    // Debug mode in development
    debug: process.env.NODE_ENV === 'development',
    // Namespace for translations
    ns: ['translation'],
    defaultNS: 'translation',
    // Interpolation configuration
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    // Detection options
    detection: {
      // Order of language detection
      order: ['localStorage', 'cookie', 'navigator'],
      // Cache language in localStorage and cookie
      caches: ['localStorage', 'cookie'],
      // localStorage key
      lookupLocalStorage: 'i18nextLng',
      // Cookie name
      lookupCookie: 'lang',
      // <PERSON>ie options
      cookieOptions: {
        path: '/',
        sameSite: 'strict',
        // Set expiration date to 1 year from now
        expires: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
      },
    },
    // React options
    react: {
      useSuspense: false, // Disable suspense for better performance
    },
    // Load resources directly
    resources: {
      en: {
        translation: enTranslation
      },
      'zh-TW': {
        translation: zhTWTranslation
      }
    },
  });

export default i18n;
