import React, { createContext, useState, useEffect } from 'react';
import { getCurrentUser, logout as authLogout } from '../services/authService';
import { fetchEvents } from '../services/eventService';

// Create the context
export const AuthContext = createContext();

// Create a provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize user from localStorage on component mount
  useEffect(() => {
    const initializeAuth = async () => {
      const currentUser = getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
      }
      setIsLoading(false);
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (userData) => {
    setUser(userData);
    return userData;
  };

  // Logout function
  const handleLogout = () => {
    setUser(null);
    authLogout();
  };

  // Context value
  const value = {
    user,
    login,
    logout: handleLogout,
    isLoading,
    // Add event-related functions to the context
    loadEvents: async () => {
      try {
        const events = await fetchEvents();
        return events;
      } catch (error) {
        console.error('Error loading events from context:', error);
        return [];
      }
    }
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
