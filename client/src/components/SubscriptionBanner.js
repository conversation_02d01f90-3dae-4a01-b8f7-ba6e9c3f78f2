import React, { useState, useEffect, useContext } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  Button,
  Chip,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import { AuthContext } from '../contexts/AuthContext';
import api from '../services/api';

const SubscriptionBanner = () => {
  const { t } = useTranslation();
  const { user } = useContext(AuthContext);
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openCancelDialog, setOpenCancelDialog] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);
  const [cancelError, setCancelError] = useState(null);

  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setLoading(true);
        const response = await api.get('/payments/subscription');
        setSubscription(response.data.subscription);
      } catch (err) {
        console.error('Error fetching subscription:', err);
        setError(t('subscription.fetchError', 'Failed to load subscription details'));
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchSubscription();
    }
  }, [user, t]);

  const handleCancelSubscription = async () => {
    try {
      setCancelLoading(true);
      setCancelError(null);
      await api.post('/payments/cancel-subscription');
      
      // Refresh subscription data
      const response = await api.get('/payments/subscription');
      setSubscription(response.data.subscription);
      setOpenCancelDialog(false);
    } catch (err) {
      console.error('Error canceling subscription:', err);
      setCancelError(err.response?.data?.message || t('subscription.cancelError', 'Failed to cancel subscription'));
    } finally {
      setCancelLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
        <CircularProgress size={24} />
      </Box>
    );
  }

  if (error) {
    return (
      <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
        <Typography>{error}</Typography>
      </Paper>
    );
  }

  // Don't show anything for users on the free plan
  if (!subscription || subscription.planType === 'free') {
    return null;
  }

  // Format the end date if available
  const formattedEndDate = subscription.currentPeriodEnd 
    ? new Date(subscription.currentPeriodEnd).toLocaleDateString() 
    : null;

  return (
    <>
      <Paper 
        sx={{ 
          p: 2, 
          mb: 3, 
          display: 'flex', 
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: { xs: 'flex-start', md: 'center' },
          justifyContent: 'space-between',
          bgcolor: subscription.planType === 'enterprise' ? 'secondary.light' : 'primary.light',
          color: subscription.planType === 'enterprise' ? 'secondary.contrastText' : 'primary.contrastText'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: { xs: 2, md: 0 } }}>
          {subscription.planType === 'enterprise' ? <StarIcon /> : <StarIcon />}
          <Typography variant="subtitle1" sx={{ ml: 1 }}>
            {t('subscription.activePlan', 'Active Plan')}: 
            <strong> {subscription.planType === 'pro' ? 'Pro' : 'Enterprise'}</strong>
            
            {subscription.cancelAtPeriodEnd && (
              <Chip 
                size="small" 
                label={t('subscription.canceling', 'Canceling')} 
                color="warning"
                sx={{ ml: 1, height: 20 }}
              />
            )}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 1 }}>
          {formattedEndDate && (
            <Typography variant="body2" sx={{ mr: 2, alignSelf: 'center' }}>
              {subscription.cancelAtPeriodEnd 
                ? t('subscription.validUntil', 'Valid until:') 
                : t('subscription.renewsOn', 'Renews on:')} {formattedEndDate}
            </Typography>
          )}
          
          {!subscription.cancelAtPeriodEnd && (
            <Button 
              size="small" 
              variant="outlined" 
              color="inherit"
              onClick={() => setOpenCancelDialog(true)}
              sx={{ borderColor: 'currentColor' }}
            >
              {t('subscription.cancel', 'Cancel Subscription')}
            </Button>
          )}
          
          {subscription.planType !== 'enterprise' && (
            <Button
              size="small"
              variant="contained"
              component={RouterLink}
              to="/checkout?plan=enterprise"
              sx={{ 
                bgcolor: 'background.paper', 
                color: 'text.primary',
                '&:hover': {
                  bgcolor: 'background.default'
                }
              }}
            >
              {t('subscription.upgrade', 'Upgrade to Enterprise')}
            </Button>
          )}
        </Box>
      </Paper>

      {/* Cancel Subscription Dialog */}
      <Dialog
        open={openCancelDialog}
        onClose={() => setOpenCancelDialog(false)}
      >
        <DialogTitle>{t('subscription.cancelTitle', 'Cancel Subscription?')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('subscription.cancelConfirm', 'Are you sure you want to cancel your subscription? You will still have access until the end of the current billing period.')}
          </DialogContentText>
          {cancelError && (
            <Box sx={{ color: 'error.main', mt: 2 }}>
              {cancelError}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCancelDialog(false)} disabled={cancelLoading}>
            {t('common.back', 'Back')}
          </Button>
          <Button 
            onClick={handleCancelSubscription} 
            color="error" 
            disabled={cancelLoading}
            startIcon={cancelLoading && <CircularProgress size={20} />}
          >
            {cancelLoading 
              ? t('common.processing', 'Processing...')
              : t('subscription.confirmCancel', 'Confirm Cancellation')}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SubscriptionBanner;