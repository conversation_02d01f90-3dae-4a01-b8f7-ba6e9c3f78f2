import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Button,
  Grid,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Divider,
  Autocomplete,
  TextField,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Assignment as TaskIcon
} from '@mui/icons-material';

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'Not set';
  const date = new Date(dateString);
  return date.toLocaleString();
};

// Helper function to get status color
const getStatusColor = (status) => {
  switch (status) {
    case 'Completed':
      return 'success';
    case 'In Progress':
      return 'primary';
    case 'Not Started':
      return 'default';
    case 'Blocked':
      return 'error';
    case 'Cancelled':
      return 'warning';
    default:
      return 'default';
  }
};

const SubtasksSection = ({ subtasks, availableTasks, onChange, currentTaskId }) => {
  const { t } = useTranslation();
  const [openDialog, setOpenDialog] = useState(false);
  const [filteredAvailableTasks, setFilteredAvailableTasks] = useState([]);
  const [selectedTasks, setSelectedTasks] = useState([]);

  // Filter out the current task and existing subtasks from available tasks
  useEffect(() => {
    if (availableTasks && Array.isArray(availableTasks)) {
      console.log('[SubtasksSection] Filtering available tasks. Total tasks:', availableTasks.length);

      // Filter out the current task and tasks that are already subtasks
      const filtered = availableTasks.filter(task => {
        // Skip the current task
        if (task._id === currentTaskId) {
          console.log(`[SubtasksSection] Skipping current task: ${task.name} (${task._id})`);
          return false;
        }

        // Skip tasks that are already subtasks
        const isAlreadySubtask = subtasks.some(subtaskId => {
          const subtaskIdStr = typeof subtaskId === 'string' ? subtaskId :
            (subtaskId && subtaskId._id ? subtaskId._id.toString() : '');
          const result = subtaskIdStr === task._id.toString();
          if (result) {
            console.log(`[SubtasksSection] Task is already a subtask: ${task.name} (${task._id})`);
          }
          return result;
        });

        return !isAlreadySubtask;
      });

      console.log(`[SubtasksSection] Filtered available tasks: ${filtered.length} out of ${availableTasks.length}`);
      setFilteredAvailableTasks(filtered);
    }
  }, [availableTasks, subtasks, currentTaskId]);

  const handleOpenDialog = () => {
    setSelectedTasks([]);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleAddSubtasks = () => {
    console.log('[SubtasksSection] Adding new subtasks:', selectedTasks.map(t => t.name));

    // Extract IDs from selected tasks
    const newSubtaskIds = selectedTasks.map(task => task._id.toString());

    // Combine with existing subtasks, avoiding duplicates
    const existingSubtaskIds = subtasks.map(subtask =>
      typeof subtask === 'string' ? subtask : (subtask && subtask._id ? subtask._id.toString() : '')
    ).filter(id => id);

    console.log('[SubtasksSection] Existing subtask IDs:', existingSubtaskIds);
    console.log('[SubtasksSection] New subtask IDs:', newSubtaskIds);

    // Create a Set to remove duplicates
    const combinedSubtaskIds = [...new Set([...existingSubtaskIds, ...newSubtaskIds])];

    console.log('[SubtasksSection] Combined subtask IDs:', combinedSubtaskIds);

    // Update the parent component with the full task objects for new subtasks
    // and keep existing subtasks as they are
    const updatedSubtasks = [];

    // First, add all existing subtasks
    subtasks.forEach(subtask => {
      if (subtask) {
        updatedSubtasks.push(subtask);
      }
    });

    // Add new subtasks that aren't already in the list
    selectedTasks.forEach(task => {
      const taskId = task._id.toString();
      const alreadyExists = existingSubtaskIds.includes(taskId);

      if (!alreadyExists) {
        console.log(`[SubtasksSection] Adding new subtask: ${task.name} (${taskId})`);
        // Ensure the task has all required fields
        const completeTask = {
          ...task,
          name: task.name || 'Unnamed Task',
          taskType: task.taskType || 'Other',
          status: task.status || 'Not Started'
        };
        updatedSubtasks.push(completeTask); // Add the full task object
      }
    });

    console.log('[SubtasksSection] Final updated subtasks:', updatedSubtasks.length);
    console.log('[SubtasksSection] Final updated subtasks details:', updatedSubtasks.map(task => {
      if (typeof task === 'string') return { type: 'string', id: task };
      return {
        type: 'object',
        id: task._id?.toString() || 'unknown',
        name: task.name || 'unnamed',
        hasRequiredFields: Boolean(task._id && task.name)
      };
    }));

    // Update the parent component
    onChange(updatedSubtasks);
    handleCloseDialog();
  };

  const handleRemoveSubtask = (subtaskId) => {
    console.log(`[SubtasksSection] Removing subtask with ID: ${subtaskId}`);

    // Filter out the removed subtask
    const updatedSubtasks = subtasks.filter(subtask => {
      const id = typeof subtask === 'string' ? subtask :
        (subtask && subtask._id ? subtask._id.toString() : '');
      const result = id !== subtaskId.toString();
      if (!result) {
        console.log(`[SubtasksSection] Removing subtask:`, typeof subtask === 'string' ? subtask : JSON.stringify(subtask));
      }
      return result;
    });

    console.log(`[SubtasksSection] Updated subtasks after removal:`, updatedSubtasks.length);
    console.log('[SubtasksSection] Remaining subtasks details:', updatedSubtasks.map(task => {
      if (typeof task === 'string') return { type: 'string', id: task };
      return {
        type: 'object',
        id: task._id?.toString() || 'unknown',
        name: task.name || 'unnamed',
        hasRequiredFields: Boolean(task._id && task.name)
      };
    }));

    onChange(updatedSubtasks);
  };

  // Find full task objects for the subtasks
  const getSubtaskDetails = () => {
    if (!subtasks || !Array.isArray(subtasks)) {
      console.log('[SubtasksSection] No subtasks to process - not an array');
      return [];
    }

    if (subtasks.length === 0) {
      console.log('[SubtasksSection] No subtasks to process - empty array');
      return [];
    }

    // Debug log to see what's in the subtasks array
    console.log('[SubtasksSection] DEBUG - Subtasks array:', subtasks);
    console.log('[SubtasksSection] DEBUG - Subtasks array type:', typeof subtasks);
    console.log('[SubtasksSection] DEBUG - Subtasks array length:', subtasks.length);
    console.log('[SubtasksSection] DEBUG - First subtask:', subtasks[0]);
    console.log('[SubtasksSection] DEBUG - First subtask type:', typeof subtasks[0]);

    // Check if all subtasks are already full objects with required fields
    const allAreCompleteObjects = subtasks.every(subtask =>
      subtask && typeof subtask === 'object' && subtask._id && subtask.name && subtask.taskType && subtask.status
    );

    if (allAreCompleteObjects) {
      console.log('[SubtasksSection] All subtasks are already complete objects, returning as is');
      return subtasks;
    }

    // Check if all subtasks are objects with at least _id and name
    const allAreObjects = subtasks.every(subtask =>
      subtask && typeof subtask === 'object' && subtask._id && subtask.name
    );

    if (allAreObjects) {
      console.log('[SubtasksSection] All subtasks are objects but may need completion');
      // Ensure all objects have the required fields
      return subtasks.map(subtask => ({
        ...subtask,
        taskType: subtask.taskType || 'Other',
        status: subtask.status || 'Not Started'
      }));
    }

    console.log('[SubtasksSection] Processing subtasks:', JSON.stringify(subtasks, (key, value) => {
      // Handle circular references in objects
      if (key === '_id' && typeof value === 'object') return value.toString();
      return value;
    }, 2));
    console.log('[SubtasksSection] Subtasks types:', subtasks.map(s => typeof s));

    // Process each subtask - could be an ID string, a populated object, or an object with _id
    const processedSubtasks = [];

    // First, try to process subtasks as an array
    for (let index = 0; index < subtasks.length; index++) {
      const subtask = subtasks[index];
      console.log(`[SubtasksSection] Processing subtask ${index}:`, subtask);

      // Case 1: If subtask is already a populated object with all the necessary fields
      if (subtask && typeof subtask === 'object') {
        if (subtask._id) {
          // Even if some fields are missing, we can still use the object if it has an ID
          console.log(`[SubtasksSection] Subtask ${index} is a populated object:`, {
            id: subtask._id.toString(),
            name: subtask.name || 'Unnamed Task',
            type: subtask.taskType || 'Other',
            status: subtask.status || 'Not Started'
          });

          // Ensure the object has the minimum required fields
          const processedSubtask = {
            ...subtask,
            name: subtask.name || 'Unnamed Task',
            taskType: subtask.taskType || 'Other',
            status: subtask.status || 'Not Started'
          };

          processedSubtasks.push(processedSubtask);
          continue;
        } else {
          console.log(`[SubtasksSection] Subtask ${index} is an object but missing ID:`, subtask);
        }
      }

      // Case 2: If subtask is a string ID, find the corresponding task object
      const subtaskId = typeof subtask === 'string' ? subtask :
        (subtask && subtask._id ? subtask._id.toString() : '');

      console.log(`[SubtasksSection] Subtask ${index} extracted ID:`, subtaskId);

      if (subtaskId && availableTasks && Array.isArray(availableTasks)) {
        const foundTask = availableTasks.find(task => task._id.toString() === subtaskId);
        if (foundTask) {
          console.log(`[SubtasksSection] Found matching task for subtask ${index}:`, {
            id: foundTask._id.toString(),
            name: foundTask.name,
            type: foundTask.taskType,
            status: foundTask.status
          });
          processedSubtasks.push(foundTask);
          continue;
        } else {
          console.warn(`[SubtasksSection] No matching task found for subtask ID: ${subtaskId}`);

          // If we can't find the task in availableTasks, create a minimal task object
          // This ensures we at least display something for the subtask
          const minimalTask = {
            _id: subtaskId,
            name: `Task ${subtaskId.substring(0, 6)}...`,
            taskType: 'Other',
            status: 'Not Started'
          };
          processedSubtasks.push(minimalTask);
          continue;
        }
      } else {
        console.warn(`[SubtasksSection] Invalid subtask ID or no available tasks to match against`);
      }
    }

    console.log(`[SubtasksSection] Processed ${processedSubtasks.length} valid subtasks out of ${subtasks.length} total`);
    return processedSubtasks;
  };

  const subtaskDetails = getSubtaskDetails();
  console.log('[SubtasksSection] Final subtask details for display:', {
    count: subtaskDetails.length,
    ids: subtaskDetails.map(task => task._id.toString()),
    names: subtaskDetails.map(task => task.name),
    fullDetails: subtaskDetails.map(task => ({
      id: task._id.toString(),
      name: task.name,
      type: task.taskType,
      status: task.status
    }))
  });

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('taskForm.subtasks', 'Subtasks')}
      </Typography>

      <Divider sx={{ mb: 2 }} />

      {/* Debug info */}
      <Box sx={{ display: 'none' }}>
        <Typography variant="body2">
          Subtasks array length: {subtasks ? subtasks.length : 0}
        </Typography>
        <Typography variant="body2">
          Subtask details length: {subtaskDetails ? subtaskDetails.length : 0}
        </Typography>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleOpenDialog}
        >
          {t('taskForm.addSubtasks', 'Add Subtasks')}
        </Button>
      </Box>

      {!subtaskDetails || subtaskDetails.length === 0 ? (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {t('taskForm.noSubtasks', 'No subtasks added yet')}
        </Typography>
      ) : (
        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>{t('taskForm.name', 'Name')}</TableCell>
                <TableCell>{t('taskForm.type', 'Type')}</TableCell>
                <TableCell>{t('taskForm.status', 'Status')}</TableCell>
                <TableCell>{t('taskForm.startTime', 'Start Time')}</TableCell>
                <TableCell>{t('taskForm.assignees', 'Assignees')}</TableCell>
                <TableCell align="right">{t('common.actions', 'Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {subtaskDetails.map((task) => {
                console.log(`[SubtasksSection] Rendering subtask row:`, {
                  id: task._id,
                  name: task.name,
                  type: task.taskType,
                  status: task.status,
                  assignees: task.assignees ? task.assignees.length : 0
                });

                return (
                  <TableRow key={task._id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <TaskIcon fontSize="small" sx={{ mr: 1 }} />
                        {task.name}
                      </Box>
                    </TableCell>
                    <TableCell>{task.taskType || 'Other'}</TableCell>
                    <TableCell>
                      <Chip
                        label={task.status || 'Not Started'}
                        size="small"
                        color={getStatusColor(task.status)}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <TimeIcon fontSize="small" sx={{ mr: 1 }} />
                        {formatDate(task.startTime)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      {task.assignees && task.assignees.length > 0 ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                          {task.assignees.length} {task.assignees.length === 1 ? 'person' : 'people'}
                        </Box>
                      ) : (
                        'Unassigned'
                      )}
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleRemoveSubtask(task._id)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add Subtasks Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {t('taskForm.addSubtasks', 'Add Subtasks')}
            <IconButton onClick={handleCloseDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Autocomplete
                multiple
                options={filteredAvailableTasks}
                getOptionLabel={(option) => option.name || ''}
                value={selectedTasks}
                onChange={(_, newValue) => {
                  setSelectedTasks(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('taskForm.selectSubtasks', 'Select Subtasks')}
                    placeholder={t('taskForm.subtasksPlaceholder', 'Select tasks that are subtasks of this task')}
                  />
                )}
                renderOption={(props, option) => (
                  <li {...props}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                      <Typography variant="body1">{option.name}</Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                        <Chip
                          label={option.taskType || 'Other'}
                          size="small"
                          sx={{ mr: 1 }}
                        />
                        <Chip
                          label={option.status || 'Not Started'}
                          size="small"
                          color={getStatusColor(option.status)}
                        />
                      </Box>
                    </Box>
                  </li>
                )}
              />
            </Grid>
          </Grid>
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
            <Button onClick={handleCloseDialog} sx={{ mr: 1 }}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddSubtasks}
              disabled={selectedTasks.length === 0}
            >
              {t('common.add', 'Add')}
            </Button>
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default SubtasksSection;
