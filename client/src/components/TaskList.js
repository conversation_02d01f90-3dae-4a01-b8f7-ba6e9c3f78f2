import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Chip,
  IconButton,
  Collapse,
  Avatar,
  Tooltip,
  TablePagination,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Divider,
  Snackbar,
  Alert,
  Checkbox,
  Toolbar,
  alpha
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import CancelIcon from '@mui/icons-material/Cancel';
import DeleteIcon from '@mui/icons-material/Delete';
import { format } from 'date-fns';
import { deleteTask, batchDeleteTasks } from '../services/taskService';

const TaskList = ({ tasks, onTaskClick, onTaskUpdate, onBatchDelete }) => {
  const { t } = useTranslation();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [expandedRow, setExpandedRow] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState(null);
  const [error, setError] = useState('');
  const [localTasks, setLocalTasks] = useState([]);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  // Batch selection state
  const [selected, setSelected] = useState([]);
  const [openBatchDeleteDialog, setOpenBatchDeleteDialog] = useState(false);

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    const value = event.target.value;
    // Special case for "All"
    if (value === 'all') {
      setRowsPerPage(visibleTasks.length || 25); // Default to 25 if no tasks
    } else {
      setRowsPerPage(parseInt(value, 10));
    }
    setPage(0);
  };

  const handleExpandRow = (taskId) => {
    setExpandedRow(expandedRow === taskId ? null : taskId);
  };

  // Get status icon based on task status
  const getStatusIcon = (status) => {
    switch (status) {
      case 'Completed':
        return <CheckCircleIcon sx={{ color: 'success.main' }} />;
      case 'In Progress':
        return <HourglassEmptyIcon sx={{ color: 'info.main' }} />;
      case 'Delayed':
        return <WarningIcon sx={{ color: 'warning.main' }} />;
      case 'Cancelled':
        return <CancelIcon sx={{ color: 'error.main' }} />;
      default:
        return <ErrorIcon sx={{ color: 'text.secondary' }} />;
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return t('taskList.notSet');
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      console.error('Error formatting date:', error);
      return t('taskList.invalidDate');
    }
  };

  // Get assignee initials for avatar
  const getAssigneeInitials = (assignee) => {
    if (!assignee || !assignee.name) return '?';

    // Safely get initials even if name is incomplete
    const nameParts = assignee.name.split(' ');
    if (nameParts.length === 1) {
      return assignee.name.charAt(0) || '?';
    }

    return (
      (nameParts[0].charAt(0) || '') +
      (nameParts[nameParts.length - 1].charAt(0) || '')
    ).toUpperCase();
  };

  // Handle delete button click
  const handleDeleteClick = (task) => {
    setTaskToDelete(task);
    setOpenDeleteDialog(true);
  };

  // Handle delete confirmation
  const handleDeleteTask = async () => {
    if (taskToDelete) {
      try {
        setError(''); // Clear any previous errors
        console.log('Deleting task with ID:', taskToDelete._id);

        // Use the proper deleteTask function to perform a real delete
        await deleteTask(taskToDelete._id);

        // Update the local state by filtering out the deleted task
        setLocalTasks(prevTasks => prevTasks.filter(task => task._id !== taskToDelete._id));

        // Close the dialog and clear the taskToDelete
        setOpenDeleteDialog(false);
        setTaskToDelete(null);

        // Show success message
        setSnackbarMessage(t('taskList.taskDeletedSuccess'));
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error deleting task:', error);
        // Show error message in the UI
        setError(t('taskList.failedToDeleteTask', { message: error.message || 'Unknown error' }));

        // Keep the dialog open so the user can see the error
        // They can close it manually if they want
      }
    } else {
      setOpenDeleteDialog(false);
    }
  };

  // Batch selection handlers
  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = visibleTasks
        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
        .map(task => task._id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleSelectClick = (_, id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, id];
    } else {
      newSelected = selected.filter(taskId => taskId !== id);
    }

    setSelected(newSelected);
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  // Handle batch delete button click
  const handleBatchDeleteClick = () => {
    if (selected.length > 0) {
      setOpenBatchDeleteDialog(true);
    }
  };

  // Handle batch delete confirmation
  const handleBatchDeleteTasks = async () => {
    try {
      setError(''); // Clear any previous errors
      console.log('Batch deleting tasks:', selected);
      console.log('onBatchDelete prop exists:', !!onBatchDelete);

      if (onBatchDelete) {
        console.log('Using parent component batch delete handler');
        // Use the parent component's batch delete handler
        await onBatchDelete(selected);

        // Close the dialog and clear the selection
        setOpenBatchDeleteDialog(false);
        setSelected([]);
      } else {
        console.log('Using direct API call for batch delete');
        // Fallback to direct API call if no handler provided
        const result = await batchDeleteTasks(selected);
        console.log('Batch delete result:', result);

        // Update the local state by filtering out the deleted tasks
        setLocalTasks(prevTasks => prevTasks.filter(task => !selected.includes(task._id)));

        // Close the dialog and clear the selection
        setOpenBatchDeleteDialog(false);
        setSelected([]);

        // Show success message
        setSnackbarMessage(t('taskList.tasksDeletedSuccess', { count: result.deletedCount }));
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
      }
    } catch (error) {
      console.error('Error batch deleting tasks:', error);
      console.error('Error details:', error.response?.data || error.message);
      // Show error message in the UI
      setError(t('taskList.failedToDeleteTasks', { message: error.message || 'Unknown error' }));
    }
  };

  // Initialize and update localTasks when tasks prop changes
  useEffect(() => {
    setLocalTasks(tasks);
  }, [tasks]);

  // Handle snackbar close
  const handleSnackbarClose = (_, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  // Organize tasks into a hierarchy (parent tasks and subtasks)
  const organizeTaskHierarchy = (tasks) => {
    // Filter out deleted tasks first
    const nonDeletedTasks = tasks.filter(task => !task.deleted);

    // Separate top-level tasks and subtasks
    const topLevelTasks = nonDeletedTasks.filter(task => !task.parentTask);
    const subtasks = nonDeletedTasks.filter(task => task.parentTask);

    // Add subtasks to their parent tasks
    const tasksWithSubtasks = topLevelTasks.map(task => {
      const taskSubtasks = subtasks.filter(subtask =>
        subtask.parentTask === task._id
      );

      return {
        ...task,
        subtasks: taskSubtasks
      };
    });

    return tasksWithSubtasks;
  };

  // Get organized tasks
  const visibleTasks = organizeTaskHierarchy(localTasks);

  // Update rowsPerPage when visibleTasks changes if "All" is selected
  useEffect(() => {
    if (rowsPerPage === visibleTasks.length && visibleTasks.length > 0) {
      setRowsPerPage(visibleTasks.length);
    }
  }, [visibleTasks, rowsPerPage]);

  // Empty state
  if (!visibleTasks || visibleTasks.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          {t('taskList.noTasks')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {t('taskList.createOrAdjustFilters')}
        </Typography>
      </Paper>
    );
  }

  // Calculate if we have any selected items
  const numSelected = selected.length;
  const rowCount = visibleTasks
    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    .length;

  return (
    <Paper>
      {/* Batch Actions Toolbar */}
      {numSelected > 0 && (
        <Toolbar
          sx={{
            pl: { sm: 2 },
            pr: { xs: 1, sm: 1 },
            bgcolor: (theme) => alpha(theme.palette.primary.main, 0.1),
            color: 'primary.main',
            minHeight: 48,
          }}
        >
          <Typography
            sx={{ flex: '1 1 100%' }}
            color="inherit"
            variant="subtitle1"
            component="div"
          >
            {numSelected} {t('taskList.selected')}
          </Typography>

          <Tooltip title={t('taskList.batchDelete')}>
            <Button
              variant="contained"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleBatchDeleteClick}
              size="small"
            >
              {t('taskList.delete')}
            </Button>
          </Tooltip>
        </Toolbar>
      )}

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  color="primary"
                  indeterminate={numSelected > 0 && numSelected < rowCount}
                  checked={rowCount > 0 && numSelected === rowCount}
                  onChange={handleSelectAllClick}
                  inputProps={{
                    'aria-label': t('taskList.selectAll'),
                  }}
                />
              </TableCell>
              <TableCell>
                <IconButton size="small" disabled style={{ visibility: 'hidden' }}>
                  <KeyboardArrowDownIcon />
                </IconButton>
              </TableCell>
              <TableCell>{t('taskList.task')}</TableCell>
              <TableCell>{t('taskList.type')}</TableCell>
              <TableCell>{t('taskList.status')}</TableCell>
              <TableCell>{t('taskList.startTime')}</TableCell>
              <TableCell>{t('taskList.duration')}</TableCell>
              <TableCell>{t('taskList.endTime')}</TableCell>
              <TableCell>{t('taskList.assignee')}</TableCell>
              <TableCell>{t('taskList.deadline')}</TableCell>
              <TableCell align="right">{t('taskList.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {visibleTasks
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((task) => {
                const isItemSelected = isSelected(task._id);

                return (
                  <React.Fragment key={task._id}>
                    <TableRow
                      hover
                      role="checkbox"
                      aria-checked={isItemSelected}
                      tabIndex={-1}
                      selected={isItemSelected}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          color="primary"
                          checked={isItemSelected}
                          onClick={(event) => handleSelectClick(event, task._id)}
                          inputProps={{
                            'aria-labelledby': `task-${task._id}`,
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleExpandRow(task._id)}
                        >
                          {expandedRow === task._id ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                        </IconButton>
                      </TableCell>
                    <TableCell component="th" scope="row">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {task.name}
                        {task.subtasks && task.subtasks.length > 0 && (
                          <Chip
                            size="small"
                            label={`${task.subtasks.length} ${task.subtasks.length === 1 ? t('taskList.subtask', 'subtask') : t('taskList.subtasks', 'subtasks')}`}
                            sx={{ ml: 1, fontSize: '0.7rem' }}
                            color="primary"
                            variant="outlined"
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={task.taskType || t('taskList.unspecified')}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getStatusIcon(task.status)}
                        <Typography variant="body2">
                          {task.status ? t(`taskStatus.${task.status.toLowerCase().replace(' ', '')}`) : t('taskStatus.notStarted')}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {task.startTime ? (
                        <Typography variant="body2">
                          {formatDate(task.startTime)}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          {t('taskList.notSet')}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {task.duration && task.duration !== '00:00:00' ? (
                        <Typography variant="body2">
                          {task.duration}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          00:00:00
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {task.startTime && task.duration && task.duration !== '00:00:00' ? (
                        <Typography variant="body2">
                          {(() => {
                            // Calculate end time based on start time and duration
                            try {
                              const startTime = new Date(task.startTime);
                              const [hours, minutes, seconds] = task.duration.split(':').map(Number);

                              const endTime = new Date(startTime);
                              endTime.setHours(endTime.getHours() + hours);
                              endTime.setMinutes(endTime.getMinutes() + minutes);
                              endTime.setSeconds(endTime.getSeconds() + seconds);

                              return formatDate(endTime);
                            } catch (error) {
                              return 'Invalid';
                            }
                          })()}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          {t('taskList.notAvailable')}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {task.assignees && task.assignees.length > 0 ? (
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          {task.assignees.slice(0, 2).map((assignee, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                                {getAssigneeInitials(assignee)}
                              </Avatar>
                              <Typography variant="body2" noWrap>
                                {assignee.name}
                              </Typography>
                            </Box>
                          ))}
                          {task.assignees.length > 2 && (
                            <Typography variant="body2" color="text.secondary">
                              {t('taskList.more', { count: task.assignees.length - 2 })}
                            </Typography>
                          )}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          {t('taskList.unassigned')}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {task.hardDeadline ? (
                        <Typography variant="body2" color="error">
                          {formatDate(task.hardDeadline)}
                        </Typography>
                      ) : task.softDeadline ? (
                        <Typography variant="body2">
                          {formatDate(task.softDeadline)}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          {t('taskList.noDeadline')}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title={t('taskList.editTask')}>
                        <IconButton
                          size="small"
                          onClick={() => onTaskClick(task)}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('taskList.deleteTask')}>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteClick(task)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={7}>
                      <Collapse in={expandedRow === task._id} timeout="auto" unmountOnExit>
                        <Box sx={{ margin: 2 }}>
                          <Typography variant="h6" gutterBottom component="div">
                            {t('taskList.taskDetails')}
                          </Typography>
                          <Typography variant="body2" paragraph>
                            {task.details || t('taskList.noAdditionalDetails')}
                          </Typography>
                          <Divider sx={{ my: 1 }} />
                          <Typography variant="body2" color="text.secondary">
                            {t('taskList.dependencies')}
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                            {task.dependencies && task.dependencies.length > 0 ? (
                              task.dependencies.map(depId => {
                                const depTask = tasks.find(t => t._id === depId);
                                return (
                                  <Chip
                                    key={depId}
                                    label={depTask ? depTask.name : t('taskList.unknownTask')}
                                    color="default"
                                    size="small"
                                    sx={{ mr: 0.5 }}
                                  />
                                );
                              })
                            ) : (
                              <Typography variant="body2" color="text.secondary">
                                {t('taskList.noDependencies')}
                              </Typography>
                            )}
                          </Box>

                          {/* Assignees */}
                          {task.assignees && task.assignees.length > 0 && (
                            <Box sx={{ mt: 2, mb: 2 }}>
                              <Typography variant="body2" color="text.secondary">
                                {t('taskList.assignees')}
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                                {task.assignees.map((assignee, index) => (
                                  <Chip
                                    key={index}
                                    avatar={<Avatar>{getAssigneeInitials(assignee)}</Avatar>}
                                    label={assignee.name}
                                    variant="outlined"
                                    size="small"
                                  />
                                ))}
                              </Box>
                            </Box>
                          )}

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mt: 2 }}>
                            {task.location && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">
                                  {t('taskList.location')}
                                </Typography>
                                <Typography variant="body1">
                                  {task.location}
                                </Typography>
                              </Box>
                            )}

                            {/* Start Time */}
                            {task.startTime && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">
                                  {t('taskList.startTime')}
                                </Typography>
                                <Typography variant="body1">
                                  {formatDate(task.startTime)}
                                </Typography>
                              </Box>
                            )}

                            {/* Duration and Computed End Time */}
                            {task.startTime && task.duration && task.duration !== '00:00:00' && (
                              <Box>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography variant="body2" color="text.secondary">
                                    {t('taskList.duration')}
                                  </Typography>
                                  <Typography variant="body1">
                                    {task.duration}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography variant="body2" color="text.secondary">
                                    {t('taskList.endTime')}
                                  </Typography>
                                  <Typography variant="body1">
                                    {(() => {
                                      // Calculate end time based on start time and duration
                                      if (!task.startTime) return 'N/A';

                                      try {
                                        const startTime = new Date(task.startTime);
                                        const [hours, minutes, seconds] = task.duration.split(':').map(Number);

                                        const endTime = new Date(startTime);
                                        endTime.setHours(endTime.getHours() + hours);
                                        endTime.setMinutes(endTime.getMinutes() + minutes);
                                        endTime.setSeconds(endTime.getSeconds() + seconds);

                                        return formatDate(endTime);
                                      } catch (error) {
                                        return 'Invalid';
                                      }
                                    })()}
                                  </Typography>
                                </Box>
                              </Box>
                            )}

                            {task.softDeadline && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">
                                  {t('taskList.softDeadline')}
                                </Typography>
                                <Typography variant="body1">
                                  {formatDate(task.softDeadline)}
                                </Typography>
                              </Box>
                            )}

                            {task.hardDeadline && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">
                                  {t('taskList.hardDeadline')}
                                </Typography>
                                <Typography variant="body1">
                                  {formatDate(task.hardDeadline)}
                                </Typography>
                              </Box>
                            )}
                          </Box>

                          {/* Subtasks Section */}
                          {task.subtasks && task.subtasks.length > 0 && (
                            <Box sx={{ mt: 2, mb: 2 }}>
                              <Typography variant="body2" color="text.secondary">
                                {t('taskList.subtasks')}
                              </Typography>
                              <List sx={{ bgcolor: 'background.paper', borderRadius: 1, mt: 1 }}>
                                {task.subtasks.map((subtask) => (
                                  <React.Fragment key={subtask._id}>
                                    <ListItem
                                      secondaryAction={
                                        <IconButton edge="end" onClick={() => onTaskClick(subtask)}>
                                          <EditIcon fontSize="small" />
                                        </IconButton>
                                      }
                                    >
                                      <ListItemText
                                        primary={
                                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            {getStatusIcon(subtask.status)}
                                            <Typography variant="body2">
                                              {subtask.name}
                                            </Typography>
                                          </Box>
                                        }
                                        secondary={
                                          <Box sx={{ display: 'flex', flexDirection: 'column', mt: 0.5 }}>
                                            {subtask.assignees && subtask.assignees.length > 0 && (
                                              <Typography variant="caption" color="text.secondary">
                                                {t('taskList.assignedTo')}: {subtask.assignees.map(a => a.name).join(', ')}
                                              </Typography>
                                            )}
                                            {subtask.softDeadline && (
                                              <Typography variant="caption" color="text.secondary">
                                                {t('taskList.deadline')}: {formatDate(subtask.softDeadline)}
                                              </Typography>
                                            )}
                                          </Box>
                                        }
                                      />
                                    </ListItem>
                                    <Divider component="li" />
                                  </React.Fragment>
                                ))}
                              </List>
                            </Box>
                          )}

                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => onTaskClick(task)}
                            sx={{ mt: 1 }}
                          >
                            {t('taskList.editTask')}
                          </Button>
                        </Box>
                      </Collapse>
                    </TableCell>
                  </TableRow>
                </React.Fragment>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50, 100, 200, { label: 'All', value: 'all' }]}
        component="div"
        count={visibleTasks.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage={t('taskList.rowsPerPage', 'Tasks per page')}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => {
          setOpenDeleteDialog(false);
          setError('');
          setTaskToDelete(null);
        }}
      >
        <DialogTitle>
          {error ? t('taskList.errorDeletingTask') : t('taskList.confirmDeletion')}
        </DialogTitle>
        <DialogContent>
          {error ? (
            <Typography color="error">{error}</Typography>
          ) : (
            <Typography>{t('taskList.deleteConfirmation')}</Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setOpenDeleteDialog(false);
            setError('');
            setTaskToDelete(null);
          }} color="primary">
            {error ? t('taskList.close') : t('taskList.cancel')}
          </Button>
          {!error && (
            <Button onClick={handleDeleteTask} color="secondary">
              {t('taskList.delete')}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Batch Delete Confirmation Dialog */}
      <Dialog
        open={openBatchDeleteDialog}
        onClose={() => {
          setOpenBatchDeleteDialog(false);
          setError('');
        }}
      >
        <DialogTitle>
          {error ? t('taskList.errorDeletingTasks') : t('taskList.confirmBatchDeletion')}
        </DialogTitle>
        <DialogContent>
          {error ? (
            <Typography color="error">{error}</Typography>
          ) : (
            <Typography>
              {t('taskList.batchDeleteConfirmation', { count: selected.length })}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setOpenBatchDeleteDialog(false);
            setError('');
          }} color="primary">
            {error ? t('taskList.close') : t('taskList.cancel')}
          </Button>
          {!error && (
            <Button onClick={handleBatchDeleteTasks} color="error">
              {t('taskList.delete')}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

// Default props
TaskList.defaultProps = {
  onTaskClick: () => {},
  onTaskUpdate: null,
  onBatchDelete: null
};

export default TaskList;