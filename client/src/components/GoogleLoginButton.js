import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mui/material';
import { useGoogleLogin } from '@react-oauth/google';
import GoogleIcon from '@mui/icons-material/Google';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';
import config from '../config';

const GoogleLoginButton = ({ onLoginSuccess, onLoginFailure }) => {
  const navigate = useNavigate();
  const [isClientIdValid, setIsClientIdValid] = useState(
    config.GOOGLE_CLIENT_ID && config.GOOGLE_CLIENT_ID !== 'your-google-client-id'
  );

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      try {
        // Get user info from Google
        const userInfoResponse = await fetch(
          'https://www.googleapis.com/oauth2/v3/userinfo',
          {
            headers: {
              Authorization: `Bearer ${tokenResponse.access_token}`,
            },
          }
        );

        const userInfo = await userInfoResponse.json();

        // Send Google profile data to our backend
        const response = await api.post('/users/google-login', {
          googleId: userInfo.sub,
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture,
        });

        // Save token and user data to localStorage
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data));

        // Call the onLoginSuccess callback if provided
        if (onLoginSuccess) {
          onLoginSuccess(response.data);
        }

        // Redirect to dashboard
        navigate('/');
      } catch (error) {
        console.error('Google login error:', error);
        if (onLoginFailure) {
          onLoginFailure(error);
        }
      }
    },
    onError: (error) => {
      console.error('Google login error:', error);
      if (onLoginFailure) {
        onLoginFailure(error);
      }
    },
    // Only enable if we have a valid client ID
    onNonOAuthError: (error) => {
      console.error('Non-OAuth error:', error);
      setIsClientIdValid(false);
      if (onLoginFailure) {
        onLoginFailure(new Error('Google OAuth client not properly configured'));
      }
    }
  });

  // If the client ID is not valid, show a disabled button with tooltip
  if (!isClientIdValid) {
    return (
      <Tooltip title="Google OAuth client ID not configured. Please set up a valid Google OAuth client ID in your environment variables.">
        <span style={{ width: '100%' }}>
          <Button
            variant="outlined"
            color="primary"
            fullWidth
            size="large"
            disabled
            startIcon={<GoogleIcon />}
            sx={{ mt: 2 }}
          >
            Sign in with Google (Not Configured)
          </Button>
        </span>
      </Tooltip>
    );
  }

  return (
    <Button
      variant="outlined"
      color="primary"
      fullWidth
      size="large"
      onClick={handleGoogleLogin}
      startIcon={<GoogleIcon />}
      sx={{ mt: 2 }}
    >
      Sign in with Google
    </Button>
  );
};

export default GoogleLoginButton;
