import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Typography,
  Paper,
  Snackbar,
  Alert,
  IconButton,
  Tooltip,
  CircularProgress,
  Divider,
  Tabs,
  Tab
} from '@mui/material';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import InfoIcon from '@mui/icons-material/Info';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import { exportTasks, importTasks, getTaskSchema } from '../services/taskService';
import { fetchEventById } from '../services/eventService';
import api from '../services/api';

const TaskExportImport = ({ eventId, onTasksImported }) => {
  const { t } = useTranslation();
  const [openExportDialog, setOpenExportDialog] = useState(false);
  const [openImportDialog, setOpenImportDialog] = useState(false);
  const [exportedTasks, setExportedTasks] = useState('');
  const [importText, setImportText] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [taskSchema, setTaskSchema] = useState(null);
  const [openSchemaDialog, setOpenSchemaDialog] = useState(false);
  const [importTab, setImportTab] = useState(0);
  const [openLLMPromptDialog, setOpenLLMPromptDialog] = useState(false);
  const [eventDetails, setEventDetails] = useState(null);
  const [llmPrompt, setLlmPrompt] = useState('');

  const fileInputRef = useRef(null);

  // Handle export tasks
  const handleExportTasks = async () => {
    setLoading(true);
    setError('');
    try {
      const tasks = await exportTasks(eventId);
      const tasksJson = JSON.stringify(tasks, null, 2);
      setExportedTasks(tasksJson);
      setOpenExportDialog(true);
    } catch (error) {
      setError(t('tasks.exportImport.failedExport', { message: error.message }));
      setSnackbarMessage(t('tasks.exportImport.failedExport', { message: error.message }));
      setSnackbarSeverity('error');
      setOpenSnackbar(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle copy to clipboard
  const handleCopyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setSnackbarMessage(t('tasks.exportImport.copiedToClipboard'));
        setSnackbarSeverity('success');
        setOpenSnackbar(true);
      },
      (err) => {
        setSnackbarMessage(t('tasks.exportImport.failedToCopy'));
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
      }
    );
  };

  // Handle import tasks
  const handleImportTasks = async () => {
    setLoading(true);
    setError('');
    try {
      // Parse the JSON text
      let tasksToImport;
      try {
        tasksToImport = JSON.parse(importText);
      } catch (parseError) {
        throw new Error(t('errors.invalidJson', 'Invalid JSON format. Please check your input.'));
      }

      // Validate that it's an array
      if (!Array.isArray(tasksToImport)) {
        throw new Error(t('errors.invalidTasksArray', 'Import data must be an array of tasks.'));
      }

      // Import the tasks
      const result = await importTasks(eventId, tasksToImport);

      // Log the imported tasks for debugging
      console.log('Imported tasks result:', result);

      // Check if the server returned the imported tasks
      if (result.tasks && result.tasks.length > 0) {
        console.log('Server returned imported tasks with assignees:',
          result.tasks.map(task => ({
            name: task.name,
            assignees: task.assignees
          }))
        );
      }

      // Close dialog and show success message
      setOpenImportDialog(false);
      setImportText('');
      setSuccess(t('tasks.exportImport.successImport', { importedCount: result.importedCount, totalCount: result.totalCount }));
      setSnackbarMessage(t('tasks.exportImport.successImport', { importedCount: result.importedCount, totalCount: result.totalCount }));
      setSnackbarSeverity('success');
      setOpenSnackbar(true);

      // Notify parent component to refresh tasks with the imported tasks if available
      if (onTasksImported) {
        if (result.tasks && result.tasks.length > 0) {
          onTasksImported(result.tasks);
        } else {
          onTasksImported();
        }
      }
    } catch (error) {
      setError(t('tasks.exportImport.failedImport', { message: error.message }));
      setSnackbarMessage(t('tasks.exportImport.failedImport', { message: error.message }));
      setSnackbarSeverity('error');
      setOpenSnackbar(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle file upload
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target.result;
        setImportText(content);
      } catch (error) {
        setError(t('tasks.exportImport.failedRead', { message: error.message }));
        setSnackbarMessage(t('tasks.exportImport.failedRead', { message: error.message }));
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
      }
    };
    reader.onerror = () => {
      setError(t('tasks.exportImport.failedRead', { message: '' }));
      setSnackbarMessage(t('tasks.exportImport.failedRead', { message: '' }));
      setSnackbarSeverity('error');
      setOpenSnackbar(true);
    };
    reader.readAsText(file);
  };

  // Handle showing schema
  const handleShowSchema = async () => {
    setLoading(true);
    try {
      const schema = await getTaskSchema();
      setTaskSchema(schema);
      setOpenSchemaDialog(true);
    } catch (error) {
      setSnackbarMessage(t('tasks.exportImport.failedSchema', { message: error.message }));
      setSnackbarSeverity('error');
      setOpenSnackbar(true);
    } finally {
      setLoading(false);
    }
  };

  // Fetch event details
  useEffect(() => {
    if (eventId) {
      const fetchEventDetails = async () => {
        try {
          const data = await fetchEventById(eventId);
          console.log('Fetched event details:', data); // Debug log
          setEventDetails(data);
        } catch (error) {
          console.error('Error fetching event details:', error);
          setSnackbarMessage(t('events.errors.loadDetailsFailed'));
          setSnackbarSeverity('error');
          setOpenSnackbar(true);
        }
      };

      fetchEventDetails();
    }
  }, [eventId]);

  // Generate LLM prompt with event details and schema
  const generateLLMPrompt = async () => {
    setLoading(true);
    try {
      // Fetch event details synchronously to ensure we have them for the prompt
      if (eventId) {
        try {
          const data = await fetchEventById(eventId);
          console.log('Fetched event details in generateLLMPrompt:', data);
          // Directly set the state and use the data for prompt generation
          setEventDetails(data);

          // Get schema if not already loaded
          let schema = taskSchema;
          if (!schema) {
            schema = await getTaskSchema();
            setTaskSchema(schema);
          }

          // Format event dates
          const formatDate = (dateString) => {
            if (!dateString) return 'Not specified';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            });
          };

          // Format venue information
          const formatVenue = () => {
            if (!data?.venue) return 'Not specified';
            const venueName = data.venue.name || '';
            const venueAddress = data.venue.address || '';
            if (venueName && venueAddress) {
              return `${venueName}, ${venueAddress}`;
            } else if (venueName) {
              return venueName;
            } else if (venueAddress) {
              return venueAddress;
            }
            return 'Not specified';
          };

          // Format budget information
          const formatBudget = () => {
            if (!data?.budget) return 'Not specified';
            const total = data.budget.total || 0;
            const currency = data.budget.currency || 'USD';
            return `${Number(total).toLocaleString()} ${currency}`;
          };

          // Create the prompt using the directly fetched data
          const prompt = `I'm planning an event and need your help to generate a comprehensive task list with dependencies. Here are the details:

` +
            `EVENT DETAILS:
` +
            `Title: ${data?.title || 'Not specified'}
` +
            `Type: ${data?.eventType || 'Not specified'}
` +
            `Description: ${data?.description || 'Not specified'}
` +
            `Date: ${formatDate(data?.date)}
` +
            `Venue: ${formatVenue()}
` +
            `Budget: ${formatBudget()}

` +
            `Please generate a list of tasks for this event following this JSON schema:

` +
            `TASK SCHEMA:
` +
            `${JSON.stringify(schema.schema, null, 2)}

` +
            `SAMPLE TASKS:
` +
            `Basic task (no dependencies):
` +
            `${JSON.stringify(schema.sampleTask || schema.sample, null, 2)}

` +
            `Task with dependencies:
` +
            `${JSON.stringify(schema.sampleTaskWithDependencies || {
              name: 'Finalize Menu',
              taskType: 'Planning',
              details: 'Select final menu options with the caterer',
              softDeadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
              status: 'Not Started',
              dependencies: ['task-id-1', 'task-id-2'], // Example task IDs this task depends on
              cost: {
                amount: 0,
                currency: 'USD',
                isPaid: false
              }
            }, null, 2)}

` +
            `Please provide a JSON array of tasks with appropriate dependencies between them. ` +
            `Make sure to include tasks for all aspects of event planning including pre-event preparation, ` +
            `day-of logistics, and post-event activities. Include realistic deadlines relative to the event date.

` +
            `Important notes:
` +
            `1. Each task must follow the schema exactly
` +
            `2. Dependencies should be logical (no circular dependencies)
` +
            `3. For dependencies, you can use placeholder IDs like 'task-1', 'task-2', etc. - these will be replaced with real IDs during import
` +
            `4. Include at least 15-20 tasks with a mix of different types and priorities
` +
            `5. Ensure the response is valid JSON that I can directly copy and import`;

          setLlmPrompt(prompt);
          setOpenLLMPromptDialog(true);
        } catch (error) {
          console.error('Error fetching event details in generateLLMPrompt:', error);
          setSnackbarMessage(`Failed to fetch event details: ${error.message}`);
          setSnackbarSeverity('error');
          setOpenSnackbar(true);
        }
      } else {
        setSnackbarMessage(t('tasks.exportImport.noEventId'));
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
      }
    } catch (error) {
      console.error('Error in generateLLMPrompt:', error);
      setSnackbarMessage(t('tasks.exportImport.failedPrompt', { message: error.message }));
      setSnackbarSeverity('error');
      setOpenSnackbar(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle import tab change
  const handleImportTabChange = (event, newValue) => {
    setImportTab(newValue);
  };

  return (
    <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
      <Button
        variant="outlined"
        startIcon={<FileDownloadIcon />}
        onClick={handleExportTasks}
        disabled={loading}
      >
        {t('tasks.exportImport.exportTasks')}
      </Button>
      <Button
        variant="outlined"
        startIcon={<FileUploadIcon />}
        onClick={() => setOpenImportDialog(true)}
        disabled={loading}
      >
        {t('tasks.exportImport.importTasks')}
      </Button>
      {loading && <CircularProgress size={24} />}

      {/* Export Dialog */}
      <Dialog open={openExportDialog} onClose={() => setOpenExportDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{t('tasks.exportImport.exportDialogTitle')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('tasks.exportImport.exportDescription')}
          </DialogContentText>
          <Paper
            elevation={3}
            sx={{
              p: 2,
              mt: 2,
              maxHeight: '400px',
              overflow: 'auto',
              position: 'relative'
            }}
          >
            <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
              <Tooltip title={t('tasks.exportImport.copyToClipboard')}>
                <IconButton onClick={() => handleCopyToClipboard(exportedTasks)}>
                  <ContentCopyIcon />
                </IconButton>
              </Tooltip>
            </Box>
            <Typography component="pre" sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
              {exportedTasks}
            </Typography>
          </Paper>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenExportDialog(false)}>{t('tasks.exportImport.close')}</Button>
        </DialogActions>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={openImportDialog} onClose={() => setOpenImportDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{t('tasks.exportImport.importDialogTitle')}</DialogTitle>
        <DialogContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={importTab} onChange={handleImportTabChange}>
              <Tab label={t('tasks.exportImport.jsonInput')} />
              <Tab label={t('tasks.exportImport.fileUpload')} />
            </Tabs>
          </Box>

          {importTab === 0 && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <DialogContentText sx={{ mb: 0 }}>
                  {t('tasks.exportImport.pasteJson')}
                  <Tooltip title={t('tasks.exportImport.viewSchema')}>
                    <IconButton size="small" onClick={handleShowSchema}>
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                </DialogContentText>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<SmartToyIcon />}
                  onClick={generateLLMPrompt}
                  disabled={loading}
                >
                  {t('tasks.exportImport.generateWithLLM')}
                </Button>
              </Box>
              <TextField
                autoFocus
                margin="dense"
                id="import-json"
                label={t('tasks.exportImport.jsonTasks')}
                fullWidth
                multiline
                rows={10}
                variant="outlined"
                value={importText}
                onChange={(e) => setImportText(e.target.value)}
                error={!!error}
                helperText={error}
              />
            </>
          )}

          {importTab === 1 && (
            <>
              <DialogContentText>
                {t('tasks.exportImport.uploadJson')}
              </DialogContentText>
              <Box sx={{ mt: 2, mb: 2 }}>
                <input
                  type="file"
                  accept=".json,application/json"
                  style={{ display: 'none' }}
                  ref={fileInputRef}
                  onChange={handleFileUpload}
                />
                <Button
                  variant="outlined"
                  startIcon={<FileUploadIcon />}
                  onClick={() => fileInputRef.current.click()}
                >
                  {t('tasks.exportImport.chooseFile')}
                </Button>
              </Box>
              {importText && (
                <Paper elevation={2} sx={{ p: 2, mt: 2, maxHeight: '200px', overflow: 'auto' }}>
                  <Typography variant="subtitle2">{t('tasks.exportImport.fileContentPreview')}</Typography>
                  <Typography
                    component="pre"
                    sx={{
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word',
                      fontSize: '0.75rem'
                    }}
                  >
                    {importText.length > 500 ? `${importText.substring(0, 500)}...` : importText}
                  </Typography>
                </Paper>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenImportDialog(false)}>{t('tasks.exportImport.cancel')}</Button>
          <Button
            onClick={handleImportTasks}
            disabled={!importText.trim() || loading}
            variant="contained"
          >
            {t('tasks.exportImport.import')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Schema Dialog */}
      <Dialog open={openSchemaDialog} onClose={() => setOpenSchemaDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {t('tasks.exportImport.schemaDialogTitle')}
          <Typography variant="subtitle2" color="text.secondary">
            {t('tasks.exportImport.schemaSubtitle', 'Use this schema when generating tasks with an LLM')}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('tasks.exportImport.schemaDescription', 'Below is the JSON schema for tasks. You can copy this schema to provide to an LLM when generating tasks.')}
          </DialogContentText>

          {taskSchema && (
            <>
              <Typography variant="h6" sx={{ mt: 2 }}>{t('common.schema', 'Schema')}</Typography>
              <Paper
                elevation={3}
                sx={{
                  p: 2,
                  mt: 1,
                  maxHeight: '200px',
                  overflow: 'auto',
                  position: 'relative'
                }}
              >
                <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
                  <Tooltip title={t('tasks.exportImport.copyToClipboard')}>
                    <IconButton onClick={() => handleCopyToClipboard(JSON.stringify(taskSchema.schema, null, 2))}>
                      <ContentCopyIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Typography component="pre" sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', fontSize: '0.75rem' }}>
                  {JSON.stringify(taskSchema.schema, null, 2)}
                </Typography>
              </Paper>

              <Typography variant="h6" sx={{ mt: 3 }}>{t('common.sampleTask', 'Sample Task')}</Typography>
              <Paper
                elevation={3}
                sx={{
                  p: 2,
                  mt: 1,
                  maxHeight: '200px',
                  overflow: 'auto',
                  position: 'relative'
                }}
              >
                <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
                  <Tooltip title={t('tasks.exportImport.copyToClipboard')}>
                    <IconButton onClick={() => handleCopyToClipboard(JSON.stringify(taskSchema.sampleTask, null, 2))}>
                      <ContentCopyIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Typography component="pre" sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', fontSize: '0.75rem' }}>
                  {JSON.stringify(taskSchema.sampleTask, null, 2)}
                </Typography>
              </Paper>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenSchemaDialog(false)}>{t('tasks.exportImport.close')}</Button>
        </DialogActions>
      </Dialog>

      {/* LLM Prompt Dialog */}
      <Dialog open={openLLMPromptDialog} onClose={() => setOpenLLMPromptDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              {t('tasks.exportImport.llmPromptDialogTitle')}
              <Typography variant="subtitle2" color="text.secondary">
                {t('tasks.exportImport.llmPromptDescription', 'Copy this prompt and send it to an LLM to generate tasks for your event')}
              </Typography>
            </Typography>
            <Button
              variant="contained"
              startIcon={<ContentCopyIcon />}
              onClick={() => handleCopyToClipboard(llmPrompt)}
            >
              {t('tasks.exportImport.copyPrompt')}
            </Button>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Paper
            elevation={3}
            sx={{
              p: 2,
              mt: 1,
              maxHeight: '500px',
              overflow: 'auto',
              bgcolor: 'grey.100'
            }}
          >
            <Typography
              component="pre"
              sx={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                fontFamily: '"Roboto Mono", monospace'
              }}
            >
              {llmPrompt}
            </Typography>
          </Paper>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenLLMPromptDialog(false)}>{t('tasks.exportImport.close')}</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={() => setOpenSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setOpenSnackbar(false)}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TaskExportImport;
