import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Button,
  Checkbox,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';

// Sample default tasks for different event types
const DEFAULT_TASKS = {
  'Wedding': [
    { name: 'Book Venue', taskType: 'Venue', relativeDeadline: -180, details: 'Find and book venue for ceremony and reception' },
    { name: 'Hire Photographer', taskType: 'Photography', relativeDeadline: -150, details: 'Book professional photographer for the event' },
    { name: 'Order Wedding Cake', taskType: 'Catering', relativeDeadline: -90, details: 'Choose bakery and order wedding cake' },
    { name: 'Book Catering', taskType: 'Catering', relativeDeadline: -120, details: 'Select menu and book catering service' },
    { name: 'Send Invitations', taskType: 'Invitation', relativeDeadline: -60, details: 'Design, print and mail invitations' },
    { name: 'Hire Musicians/DJ', taskType: 'Entertainment', relativeDeadline: -120, details: 'Book entertainment for ceremony and reception' },
    { name: 'Order Flowers', taskType: 'Decoration', relativeDeadline: -45, details: 'Choose and order flowers for ceremony and reception' },
    { name: 'Book Transportation', taskType: 'Transportation', relativeDeadline: -60, details: 'Arrange transportation for wedding party' },
    { name: 'Buy Wedding Attire', taskType: 'Attire', relativeDeadline: -120, details: 'Purchase wedding dress/suit and accessories' },
    { name: 'Book Hair and Makeup', taskType: 'Beauty', relativeDeadline: -90, details: 'Schedule hair and makeup appointments' }
  ],
  'Birthday': [
    { name: 'Book Venue', taskType: 'Venue', relativeDeadline: -30, details: 'Find and reserve party venue' },
    { name: 'Order Cake', taskType: 'Catering', relativeDeadline: -14, details: 'Order birthday cake from bakery' },
    { name: 'Send Invitations', taskType: 'Invitation', relativeDeadline: -21, details: 'Create and send party invitations' },
    { name: 'Arrange Decorations', taskType: 'Decoration', relativeDeadline: -7, details: 'Buy or order party decorations' },
    { name: 'Plan Activities/Games', taskType: 'Entertainment', relativeDeadline: -14, details: 'Prepare games and activities for guests' }
  ],
  'Corporate': [
    { name: 'Book Conference Room', taskType: 'Venue', relativeDeadline: -60, details: 'Reserve conference room or event space' },
    { name: 'Arrange Catering', taskType: 'Catering', relativeDeadline: -30, details: 'Select and book catering service' },
    { name: 'Prepare Agenda', taskType: 'Other', relativeDeadline: -21, details: 'Finalize meeting or event agenda' },
    { name: 'Send Invitations', taskType: 'Invitation', relativeDeadline: -21, details: 'Send formal invitations to attendees' },
    { name: 'Arrange A/V Equipment', taskType: 'Other', relativeDeadline: -14, details: 'Reserve necessary audio-visual equipment' },
    { name: 'Book Speakers', taskType: 'Entertainment', relativeDeadline: -90, details: 'Contact and confirm guest speakers' }
  ],
  'Anniversary': [
    { name: 'Book Restaurant/Venue', taskType: 'Venue', relativeDeadline: -30, details: 'Make reservation or book event space' },
    { name: 'Order Cake', taskType: 'Catering', relativeDeadline: -14, details: 'Order special anniversary cake' },
    { name: 'Send Invitations', taskType: 'Invitation', relativeDeadline: -21, details: 'Send invitations to family and friends' },
    { name: 'Arrange Gift', taskType: 'Other', relativeDeadline: -14, details: 'Purchase anniversary gift' },
    { name: 'Book Photographer', taskType: 'Photography', relativeDeadline: -21, details: 'Hire photographer for the event' }
  ],
  'Other': [
    { name: 'Book Venue', taskType: 'Venue', relativeDeadline: -30, details: 'Secure location for the event' },
    { name: 'Arrange Food/Drinks', taskType: 'Catering', relativeDeadline: -14, details: 'Organize refreshments for guests' },
    { name: 'Send Invitations', taskType: 'Invitation', relativeDeadline: -21, details: 'Notify guests about the event' }
  ]
};

const DefaultTaskSelector = ({ eventType, eventDate, onTasksSelected }) => {
  const { t } = useTranslation();
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [availableTasks, setAvailableTasks] = useState([]);

  useEffect(() => {
    // Set available tasks based on event type
    if (eventType && DEFAULT_TASKS[eventType]) {
      setAvailableTasks(DEFAULT_TASKS[eventType]);

      // Pre-select all tasks as a convenience
      setSelectedTasks(DEFAULT_TASKS[eventType].map((_, index) => index));
    } else {
      setAvailableTasks([]);
      setSelectedTasks([]);
    }
  }, [eventType]);

  const handleToggleTask = (index) => {
    const currentIndex = selectedTasks.indexOf(index);
    const newSelectedTasks = [...selectedTasks];

    if (currentIndex === -1) {
      newSelectedTasks.push(index);
    } else {
      newSelectedTasks.splice(currentIndex, 1);
    }

    setSelectedTasks(newSelectedTasks);
  };

  const handleAddTasks = () => {
    // Prepare tasks with calculated deadlines if eventDate is available
    const tasksToAdd = selectedTasks.map(index => {
      const task = availableTasks[index];

      // Calculate actual deadline dates if event date is provided
      let softDeadline = null;
      let hardDeadline = null;

      if (eventDate && task.relativeDeadline) {
        const eventDateTime = new Date(eventDate);

        // Set soft deadline (with time component)
        softDeadline = new Date(eventDateTime);
        softDeadline.setDate(eventDateTime.getDate() + task.relativeDeadline);

        // Set hard deadline 7 days after soft deadline
        hardDeadline = new Date(softDeadline);
        hardDeadline.setDate(softDeadline.getDate() + 7);
      }

      return {
        ...task,
        softDeadline,
        hardDeadline,
        status: 'Not Started',
        dependencies: []
      };
    });

    onTasksSelected(tasksToAdd);
  };

  if (!eventType) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="body1" color="text.secondary">
          {t('tasks.defaultTasks.noDefaultTasks')}
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" gutterBottom>
        {t('tasks.defaultTasks.title')} - {eventType}
      </Typography>

      <Divider sx={{ mb: 2 }} />

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        {t('tasks.defaultTasks.description')}
      </Typography>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Button
          size="small"
          onClick={() => setSelectedTasks(Array.from(Array(availableTasks.length).keys()))}
          sx={{ mr: 1 }}
        >
          {t('tasks.defaultTasks.selectAll')}
        </Button>
        <Button
          size="small"
          onClick={() => setSelectedTasks([])}
        >
          {t('tasks.defaultTasks.deselectAll')}
        </Button>
      </Box>

      <List>
        {availableTasks.map((task, index) => (
          <ListItem key={index} sx={{ py: 0 }}>
            <ListItemIcon>
              <Checkbox
                edge="start"
                checked={selectedTasks.indexOf(index) !== -1}
                onChange={() => handleToggleTask(index)}
              />
            </ListItemIcon>
            <ListItemText
              primary={task.name}
              secondary={`${task.taskType} - ${task.relativeDeadline < 0 ? Math.abs(task.relativeDeadline) + ' days before event' : task.relativeDeadline + ' days after event'}`}
            />
          </ListItem>
        ))}
      </List>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
        <Button
          variant="outlined"
          onClick={() => onTasksSelected([])}
        >
          {t('tasks.defaultTasks.cancel')}
        </Button>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddTasks}
          disabled={selectedTasks.length === 0}
        >
          {t('tasks.defaultTasks.add')} ({selectedTasks.length})
        </Button>
      </Box>
    </Paper>
  );
};

export default DefaultTaskSelector;