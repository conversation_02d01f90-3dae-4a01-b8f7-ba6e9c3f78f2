import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardA<PERSON>,
  Chip,
  Grid,
  Alert,
  CircularProgress,
  Collapse,
  IconButton
} from '@mui/material';
import {
  Check as CheckIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { getUserPendingInvitations, respondToInvitation } from '../services/eventService';

const PendingInvitations = ({ onInvitationResponse }) => {
  const [pendingInvitations, setPendingInvitations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [expanded, setExpanded] = useState({});
  const [responseStatus, setResponseStatus] = useState({});

  useEffect(() => {
    loadPendingInvitations();
  }, []);

  const loadPendingInvitations = async () => {
    try {
      setLoading(true);
      const invitations = await getUserPendingInvitations();
      setPendingInvitations(invitations);
      
      // Initialize expanded state for each invitation
      const expandedState = {};
      invitations.forEach(invitation => {
        expandedState[invitation.eventId] = false;
      });
      setExpanded(expandedState);
      
      // Initialize response status for each invitation
      const statusState = {};
      invitations.forEach(invitation => {
        statusState[invitation.eventId] = {
          status: '',
          loading: false,
          error: ''
        };
      });
      setResponseStatus(statusState);
    } catch (err) {
      console.error('Error loading pending invitations:', err);
      setError('Failed to load pending invitations');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleExpand = (eventId) => {
    setExpanded(prev => ({
      ...prev,
      [eventId]: !prev[eventId]
    }));
  };

  const handleRespondToInvitation = async (eventId, invitationId, status) => {
    // Update response status to loading
    setResponseStatus(prev => ({
      ...prev,
      [eventId]: {
        ...prev[eventId],
        loading: true,
        error: ''
      }
    }));

    try {
      await respondToInvitation(eventId, invitationId, status);
      
      // Update response status to success
      setResponseStatus(prev => ({
        ...prev,
        [eventId]: {
          status,
          loading: false,
          error: ''
        }
      }));
      
      // Remove this invitation from the list after a short delay
      setTimeout(() => {
        setPendingInvitations(prev => 
          prev.filter(invitation => invitation.eventId !== eventId)
        );
        
        // Notify parent component if callback provided
        if (onInvitationResponse) {
          onInvitationResponse(eventId, status);
        }
      }, 2000);
    } catch (err) {
      console.error('Error responding to invitation:', err);
      
      // Update response status to error
      setResponseStatus(prev => ({
        ...prev,
        [eventId]: {
          status: '',
          loading: false,
          error: err.response?.data?.message || 'Failed to respond to invitation'
        }
      }));
    }
  };

  if (loading && pendingInvitations.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (pendingInvitations.length === 0) {
    return null; // Don't show anything if no pending invitations
  }

  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        Pending Event Invitations
      </Typography>
      
      <Grid container spacing={2}>
        {pendingInvitations.map((invitation) => {
          const eventId = invitation.eventId;
          const invitationId = invitation.invitation._id;
          const isExpanded = expanded[eventId];
          const response = responseStatus[eventId] || { status: '', loading: false, error: '' };
          
          return (
            <Grid item xs={12} sm={6} md={4} key={eventId}>
              <Card 
                variant="outlined"
                sx={{
                  position: 'relative',
                  opacity: response.status ? 0.7 : 1,
                  transition: 'opacity 0.3s'
                }}
              >
                {response.status && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(255,255,255,0.8)',
                      zIndex: 1
                    }}
                  >
                    <Alert 
                      severity={response.status === 'Accepted' ? 'success' : 'info'}
                      icon={response.status === 'Accepted' ? <CheckIcon /> : <CloseIcon />}
                    >
                      {response.status === 'Accepted' ? 'Accepted' : 'Declined'}
                    </Alert>
                  </Box>
                )}
                
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Typography variant="h6" component="div">
                      {invitation.eventTitle}
                    </Typography>
                    <Chip 
                      icon={<CalendarIcon />}
                      label={new Date(invitation.eventDate).toLocaleDateString()}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </Box>
                  
                  <Typography color="text.secondary" gutterBottom>
                    {invitation.eventType}
                  </Typography>
                  
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="body2">
                      <strong>Invited by:</strong> {invitation.invitation.invitedBy?.name || 'Unknown'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Role:</strong> {invitation.invitation.role}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Invited on:</strong> {new Date(invitation.invitation.invitedAt).toLocaleDateString()}
                    </Typography>
                  </Box>
                  
                  <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        <strong>Event Owner:</strong> {invitation.owner?.name || 'Unknown'}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Owner Email:</strong> {invitation.owner?.email || 'Unknown'}
                      </Typography>
                    </Box>
                  </Collapse>
                </CardContent>
                
                <CardActions sx={{ justifyContent: 'space-between' }}>
                  <Box>
                    <Button
                      size="small"
                      color="primary"
                      startIcon={<CheckIcon />}
                      onClick={() => handleRespondToInvitation(eventId, invitationId, 'Accepted')}
                      disabled={!!response.status || response.loading}
                    >
                      Accept
                    </Button>
                    <Button
                      size="small"
                      color="error"
                      startIcon={<CloseIcon />}
                      onClick={() => handleRespondToInvitation(eventId, invitationId, 'Declined')}
                      disabled={!!response.status || response.loading}
                    >
                      Decline
                    </Button>
                  </Box>
                  
                  <IconButton
                    onClick={() => handleToggleExpand(eventId)}
                    aria-expanded={isExpanded}
                    aria-label="show more"
                    size="small"
                  >
                    <ExpandMoreIcon sx={{
                      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                      transition: 'transform 0.2s'
                    }} />
                  </IconButton>
                </CardActions>
                
                {response.loading && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
                    <CircularProgress size={24} />
                  </Box>
                )}
                
                {response.error && (
                  <Alert severity="error" sx={{ mx: 2, mb: 2 }}>
                    {response.error}
                  </Alert>
                )}
              </Card>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};

export default PendingInvitations;
