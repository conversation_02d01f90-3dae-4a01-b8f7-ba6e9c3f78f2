import React, { useCallback } from 'react';
import { <PERSON><PERSON>, Position, NodeResizer } from 'reactflow';
import {
  Box,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import LinkIcon from '@mui/icons-material/Link';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { format } from 'date-fns';

const SubtaskNode = ({ data, selected }) => {
  const { task, onEdit, onConnect, isCreatingEdge } = data;

  /* helpers ---------------------------------------------------------- */
  const fmt = (d) => (d ? format(new Date(d), 'MMM d') : '');
  const statusClr = (s) =>
    ({ Completed: 'success', 'In Progress': 'info', Delayed: 'warning', Cancelled: 'error' }[s] ??
    'default');

  const onResize = useCallback((_, p) => console.log('Subtask resized', p), []);

  /* render ----------------------------------------------------------- */
  return (
    <Box
      sx={{
        p: 1,
        borderRadius: 1,
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor:
          selected || isCreatingEdge ? 'primary.main' : 'divider',
        boxShadow: selected || isCreatingEdge ? 3 : 1,
        transition: 'all .2s ease',
        '&:hover': {
          boxShadow: 3,
          borderColor: 'primary.light',
          transform: 'translateY(-2px)',
        },
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'visible',
        zIndex: 10,
        borderLeft: '4px solid',
        borderLeftColor: 'primary.light',
      }}
    >
      <NodeResizer
        minWidth={120}
        minHeight={80}
        isVisible={selected}
        onResize={onResize}
        handleStyle={{ width: 6, height: 6, borderRadius: 3, background: '#1976d2', borderColor: '#fff' }}
        lineStyle={{ borderWidth: 1, borderColor: '#1976d2', borderStyle: 'dashed' }}
      />

      {['source', 'target'].map((k) => (
        <Handle
          key={k}
          type={k}
          position={k === 'source' ? Position.Right : Position.Left}
          id={`sub-${k}-${task._id}`}
          style={{ background: '#555', width: 8, height: 8 }}
        />
      ))}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
        <Typography variant="body2" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
          {task.name}
        </Typography>
        <Box>
          <Tooltip title="Create dependency">
            <IconButton size="small" sx={{ p: .25 }} onClick={() => onConnect(task._id)}>
              <LinkIcon fontSize="inherit" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Edit">
            <IconButton size="small" sx={{ p: .25 }} onClick={() => onEdit(task)}>
              <EditIcon fontSize="inherit" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Chip label={task.taskType} size="small" sx={{ mb: .5, height: 20, fontSize: '.65rem' }} />

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: .25, flexGrow: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="caption" color="text.secondary">Status:</Typography>
          <Chip size="small" label={task.status} color={statusClr(task.status)} sx={{ height: 18, fontSize: '.6rem' }} />
        </Box>

        {task.startTime && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="caption" color="text.secondary">
              <AccessTimeIcon sx={{ fontSize: '.8rem', mr: .25, verticalAlign: 'text-bottom' }} />
            </Typography>
            <Typography variant="caption">
              {fmt(task.startTime)}
              {task.duration && task.duration !== '00:00:00' &&
                ` (${task.duration.split(':').slice(0, 2).join(':')})`}
            </Typography>
          </Box>
        )}

        {task.assignees?.length > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 'auto', pt: .5 }}>
            {task.assignees.length <= 2 ? (
              task.assignees.map((a, i) => (
                <Avatar key={i} sx={{ width: 16, height: 16, fontSize: '.6rem', ml: i ? -.5 : 0 }}>
                  {a.name ? a.name[0] : '?'}
                </Avatar>
              ))
            ) : (
              <Chip size="small" label={`${task.assignees.length} people`} sx={{ height: 16, fontSize: '.6rem' }} />
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default SubtaskNode;