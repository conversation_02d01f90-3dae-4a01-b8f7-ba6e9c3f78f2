import React, { useContext, useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { EventContext } from '../contexts/EventContext';
import SuppliesPage from '../pages/SuppliesPage';

export default function SuppliesWrapper() {
  const { eventId } = useParams();
  const { selectedEventId, setSelectedEventId } = useContext(EventContext);
  const navigate = useNavigate();
  const [initialized, setInitialized] = useState(false);

  // Initialize component
  useEffect(() => {
    console.log('SuppliesWrapper: Initializing with URL eventId:', eventId);
    console.log('SuppliesWrapper: Context selectedEventId:', selectedEventId);

    // If we have an event ID in the URL, use it
    if (eventId) {
      console.log('SuppliesWrapper: Setting selectedEventId from URL:', eventId);
      setSelectedEventId(eventId);
      localStorage.setItem('selectedEventId', eventId);
    }
    // If we don't have an event ID in the URL but we have one in context, update the URL
    else if (selectedEventId) {
      console.log('SuppliesWrapper: Updating URL with selectedEventId:', selectedEventId);
      navigate(`/supplies/${selectedEventId}`, { replace: true });
    }
    // If we don't have an event ID in either place, redirect to events page
    else {
      console.log('SuppliesWrapper: No event ID found, redirecting to events page');
      navigate('/events');
      return;
    }

    setInitialized(true);
  }, []);

  // Handle URL and selectedEventId changes after initialization
  useEffect(() => {
    if (!initialized) return;
    console.log('SuppliesWrapper: Checking URL and context state after initialization');
    console.log('SuppliesWrapper: URL eventId:', eventId);
    console.log('SuppliesWrapper: Context selectedEventId:', selectedEventId);

    // If we have an event ID in the URL, it takes precedence
    if (eventId) {
      // Only update if they're different to avoid infinite loops
      if (eventId !== selectedEventId) {
        console.log('SuppliesWrapper: Setting selectedEventId to match URL:', eventId);
        setSelectedEventId(eventId);
        localStorage.setItem('selectedEventId', eventId);
      }
    }
    // If we have a selected event ID but it's not in the URL, update the URL
    else if (selectedEventId) {
      console.log('SuppliesWrapper: Updating URL to match selectedEventId:', selectedEventId);
      navigate(`/supplies/${selectedEventId}`, { replace: true });
    }

    // Always ensure localStorage is in sync with the selected event ID
    if (selectedEventId) {
      localStorage.setItem('selectedEventId', selectedEventId);
    }
  }, [eventId, initialized, selectedEventId, navigate, setSelectedEventId]);

  return <SuppliesPage />;
}
