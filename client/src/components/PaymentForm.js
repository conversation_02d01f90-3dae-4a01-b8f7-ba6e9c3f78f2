import React, { useState } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import {
  Box,
  Button,
  CircularProgress,
  Typography,
  Alert,
  Paper,
  Divider
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import api from '../services/api';

const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      color: '#32325d',
      fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
      fontSmoothing: 'antialiased',
      fontSize: '16px',
      '::placeholder': {
        color: '#aab7c4'
      }
    },
    invalid: {
      color: '#fa755a',
      iconColor: '#fa755a'
    }
  }
};

const PaymentForm = ({ planId, trialDays = 0, onSuccess, onCancel }) => {
  const { t } = useTranslation();
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create checkout session with trial period if specified
      const response = await api.post('/payments/create-checkout-session', {
        planId,
        trialPeriodDays: trialDays
      });

      const { sessionId, url } = response.data;

      // If we get URL, redirect to Stripe Checkout
      if (url) {
        window.location.href = url;
      } else {
        setError('Failed to create checkout session');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'An error occurred while processing your payment');
      console.error('Payment error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Typography variant="h5" component="h2" gutterBottom>
        {trialDays > 0 
          ? t('payment.titleWithTrial', 'Start Your {{days}}-Day Free Trial', { days: trialDays })
          : t('payment.title', 'Secure Payment')
        }
      </Typography>
      
      <Divider sx={{ my: 2 }} />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            {t('payment.cardDetails', 'Card Details')}
          </Typography>
          {trialDays > 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {t('payment.trialNotice', 'Your card will not be charged until your {{days}}-day free trial ends. You can cancel anytime before then.', { days: trialDays })}
            </Typography>
          )}
          <Box sx={{ 
            p: 2, 
            border: '1px solid #e0e0e0', 
            borderRadius: 1,
            '&:focus-within': {
              borderColor: 'primary.main'
            }
          }}>
            <CardElement options={CARD_ELEMENT_OPTIONS} />
          </Box>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button 
            variant="outlined" 
            onClick={onCancel}
            disabled={loading}
          >
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={!stripe || loading}
            startIcon={loading && <CircularProgress size={20} color="inherit" />}
          >
            {loading
              ? t('payment.processing', 'Processing...')
              : trialDays > 0 
                ? t('payment.startTrial', 'Start Free Trial')
                : t('payment.payNow', 'Pay Now')}
          </Button>
        </Box>
      </form>
    </Paper>
  );
};

export default PaymentForm; 