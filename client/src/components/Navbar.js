import React, { useState, useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Box,
  Menu,
  MenuItem,
  Divider,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  FormControl,
  InputLabel,
  FormHelperText,
  useTheme,
  Tooltip,
  Avatar,
  Badge,
  Stack
} from '@mui/material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
// Removed unused MenuIcon and DashboardIcon imports
import EventIcon from '@mui/icons-material/Event';
import TaskIcon from '@mui/icons-material/Task';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import AddIcon from '@mui/icons-material/Add';
import LogoutIcon from '@mui/icons-material/Logout';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import CloseIcon from '@mui/icons-material/Close';
import PeopleIcon from '@mui/icons-material/People';
import PlaceIcon from '@mui/icons-material/Place';
import BusinessIcon from '@mui/icons-material/Business';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import { EventContext } from '../contexts/EventContext';
import EventForm from './EventForm';
import { formatDate } from '../utils/dateUtils';
import { fetchEvents, createEvent } from '../services/eventService';
import { logout, getCurrentUser } from '../services/authService';
import SignalWifiOffIcon from '@mui/icons-material/SignalWifiOff';
import NotificationBell from './NotificationBell';
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import LanguageSelector from './LanguageSelector';
import MenuIcon from '@mui/icons-material/Menu';
import Drawer from '@mui/material/Drawer';
import useMediaQuery from '@mui/material/useMediaQuery';

const Navbar = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { selectedEventId, setSelectedEventId, currentEvent, setCurrentEvent } = useContext(EventContext);

  const [anchorEl, setAnchorEl] = useState(null);
  const [eventMenuAnchor, setEventMenuAnchor] = useState(null);
  const [openEventDialog, setOpenEventDialog] = useState(false);
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [networkStatus, setNetworkStatus] = useState('online');
  const [user, setUser] = useState(null);
  const [hoveredEventId, setHoveredEventId] = useState(null);
  const isTabletOrMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Check if user is logged in
  useEffect(() => {
    const user = getCurrentUser();
    setUser(user);
  }, []);

  // Load events from API instead of localStorage
  useEffect(() => {
    const loadEvents = async () => {
      console.log('Navbar: Loading events...');
      setLoading(true);
      try {
        const eventsData = await fetchEvents();
        // Check if we got actual data back
        if (Array.isArray(eventsData) && eventsData.length > 0) {
          console.log('Navbar: Events loaded successfully:', eventsData.length, 'events');
          setEvents(eventsData);

          // If we have a selectedEventId but no currentEvent, try to set the currentEvent
          if (selectedEventId && !currentEvent) {
            console.log('Navbar: Have selectedEventId but no currentEvent, finding event...');
            const event = eventsData.find(e => e._id === selectedEventId);
            if (event) {
              console.log('Navbar: Found matching event, setting currentEvent:', event.title);
              setCurrentEvent(event);
            } else {
              console.log('Navbar: No matching event found for ID:', selectedEventId);
            }
          }
        } else {
          console.log('No events found in the response');
          // Set to empty array to avoid undefined errors
          setEvents([]);
        }
      } catch (error) {
        console.error('Error loading events:', error);
        // Set to empty array on error
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    loadEvents();
  }, [selectedEventId]);

  // Update current event when selectedEventId changes
  useEffect(() => {
    if (selectedEventId && events.length > 0 && setCurrentEvent) {
      const event = events.find(e => e._id === selectedEventId);
      if (event) {
        setCurrentEvent(event);
      }
    }
  }, [selectedEventId, events, setCurrentEvent]);

  // Add a network status detector
  useEffect(() => {
    const handleOnline = () => setNetworkStatus('online');
    const handleOffline = () => setNetworkStatus('offline');
    const handleApiNetworkError = () => setNetworkStatus('offline');

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('api:networkerror', handleApiNetworkError);

    // Check initial network status
    if (!navigator.onLine) {
      setNetworkStatus('offline');
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('api:networkerror', handleApiNetworkError);
    };
  }, []);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    // Close the menu
    handleMenuClose();
    // Redirect to login page
    navigate('/login');
  };

  const handleEventMenuOpen = (event) => {
    setEventMenuAnchor(event.currentTarget);
  };

  const handleEventMenuClose = () => {
    setEventMenuAnchor(null);
  };

  const handleOpenEventDialog = () => {
    setOpenEventDialog(true);
    handleEventMenuClose();
  };

  const handleCloseEventDialog = () => {
    setOpenEventDialog(false);
  };

  const handleEventSelect = (eventId) => {
    console.log('Navbar: Selecting event:', eventId);

    // Update the context
    setSelectedEventId(eventId);

    // Always update localStorage to ensure consistency
    localStorage.setItem('selectedEventId', eventId);
    console.log('Navbar: Saved selected event ID to localStorage:', eventId);

    handleEventMenuClose();

    // If we're on a page that needs an event ID, navigate to that page with the new event ID
    if (location.pathname.includes('/tasks') || location.pathname.includes('/budget')) {
      navigate(`${location.pathname.split('/')[1]}/${eventId}`);
    }
    // Handle venue and guest pages specifically
    else if (location.pathname.includes('/resources/venues')) {
      navigate(`/resources/venues/${eventId}`);
    }
    else if (location.pathname.includes('/resources/guests')) {
      navigate(`/resources/guests/${eventId}`);
    }
  };

  // Update handleEventSubmit to use API
  const handleEventSubmit = async (eventData) => {
    try {
      setLoading(true);
      // Create event on the server
      const newEvent = await createEvent(eventData);

      // Update local state
      setEvents(prevEvents => [...prevEvents, newEvent]);

      // Select the new event
      setSelectedEventId(newEvent._id);
      setCurrentEvent(newEvent);

      // Always update localStorage to ensure consistency
      localStorage.setItem('selectedEventId', newEvent._id);
      console.log('Navbar: Saved new event ID to localStorage:', newEvent._id);

      // Close the dialog
      handleCloseEventDialog();

      // Navigate to the tasks page for this event
      navigate(`/tasks/${newEvent._id}`);
    } catch (error) {
      console.error('Error creating event:', error);
      // Handle error (you could set an error state and display a message)
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatEventDate = (dateString) => {
    if (!dateString) return t('events.noDateSet');
    const formattedDate = formatDate(dateString, 'MMM d, yyyy');
    return formattedDate || t('events.invalidDate');
  };

  return (
    <AppBar position="static" sx={{ background: 'linear-gradient(90deg, #6200ea, #03dac6)' }}>
      <Toolbar>
        {isTabletOrMobile && (
          <IconButton
            color="inherit"
            edge="start"
            onClick={() => setDrawerOpen(true)}
            sx={{ mr: { xs: 1, sm: 2 } }}
          >
            <MenuIcon />
          </IconButton>
        )}
        <Typography variant="h6" component={RouterLink} to="/" sx={{ flexGrow: 0, textDecoration: 'none', color: 'inherit', mr: { xs: 2, sm: 4 } }}>
          {t('app.title')}
        </Typography>
        {!isTabletOrMobile && user && (
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            <Button
              color="inherit"
              onClick={handleEventMenuOpen}
              endIcon={<ArrowDropDownIcon />}
              startIcon={<EventIcon />}
              sx={{
                textTransform: 'none',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.3)',
                },
                px: 2,
                borderRadius: 2,
                minWidth: 200
              }}
            >
              {currentEvent ? (
                <Box sx={{ textAlign: 'left' }}>
                  <Typography variant="body1" fontWeight="bold" noWrap>
                    {currentEvent.title || 'Unnamed Event'}
                  </Typography>
                  <Typography variant="caption" noWrap sx={{ display: 'block' }}>
                    {formatEventDate(currentEvent.date) || 'No date set'}
                  </Typography>
                </Box>
              ) : (
                <Typography>
                  {loading ? t('events.loadingEvents') : events.length === 0 ? t('events.noEventsAvailable') : t('events.select')}
                </Typography>
              )}
            </Button>

            <Button
              color="inherit"
              component={RouterLink}
              to="/events"
              startIcon={<EventIcon />}
              sx={{ ml: 1 }}
            >
              {t('navigation.events')}
            </Button>

            <Menu
              anchorEl={eventMenuAnchor}
              open={Boolean(eventMenuAnchor)}
              onClose={handleEventMenuClose}
              PaperProps={{
                sx: { width: 320, maxHeight: 500 }
              }}
            >
              <MenuItem onClick={handleOpenEventDialog}>
                <ListItemIcon>
                  <AddIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>{t('events.create')}</ListItemText>
              </MenuItem>

              {events.length > 0 ? (
                <>
                  <Divider />
                  {events.map(event => (
                    <Box
                      key={event._id}
                      onMouseEnter={() => setHoveredEventId(event._id)}
                      onMouseLeave={() => setHoveredEventId(null)}
                      sx={{ position: 'relative' }}
                    >
                      <MenuItem
                        onClick={() => handleEventSelect(event._id)}
                        selected={selectedEventId === event._id}
                        sx={{
                          '&:hover': {
                            backgroundColor: theme.palette.action.hover
                          },
                          pr: 8, // Add padding to the right to make space for the button
                          position: 'relative', // Ensure proper positioning context
                          '& .MuiListItemText-root': {
                            maxWidth: '70%' // Limit text width to prevent overlap
                          }
                        }}
                      >
                        <ListItemIcon>
                          <EventIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={event.title || 'Unnamed Event'}
                          secondary={formatEventDate(event.date) || 'No date set'}
                        />
                      </MenuItem>
                      {hoveredEventId === event._id && (
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            position: 'absolute',
                            right: 12,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            transition: 'opacity 0.2s ease',
                            opacity: 1,
                            zIndex: 2
                          }}
                        >
                          <Button
                            size="small"
                            variant="outlined"
                            color="primary"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent event selection
                              handleEventMenuClose();
                              navigate(`/events/${event._id}`);
                            }}
                            sx={{
                              py: 0.5, // Reduce vertical padding
                              minWidth: '80px' // Set minimum width
                            }}
                          >
                            Details
                          </Button>
                        </Box>
                      )}
                    </Box>
                  ))}
                </>
              ) : loading ? (
                <MenuItem disabled>
                  <ListItemText>{t('events.loadingEvents')}</ListItemText>
                </MenuItem>
              ) : (
                <MenuItem disabled>
                  <ListItemText>{t('events.noEvents')}</ListItemText>
                </MenuItem>
              )}
            </Menu>

            {/* Add a network status indicator */}
            {networkStatus === 'offline' && (
              <Tooltip title="Offline Mode - Using local data">
                <IconButton color="error">
                  <SignalWifiOffIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        )}
        {!isTabletOrMobile && !user && <Box sx={{ flexGrow: 1 }}></Box>}
        {!isTabletOrMobile && user ? (
          <Box sx={{ display: 'flex' }}>
            <Button
              color="inherit"
              component={RouterLink}
              to={selectedEventId ? `/tasks/${selectedEventId}` : '/events'}
              startIcon={<TaskIcon />}
              disabled={!selectedEventId && !localStorage.getItem('selectedEventId')}
            >
              {t('navigation.tasks')}
            </Button>

            <Button
              color="inherit"
              component={RouterLink}
              to="/calendar"
              startIcon={<CalendarMonthIcon />}
              disabled={!selectedEventId && !localStorage.getItem('selectedEventId')}
            >
              {t('navigation.calendar')}
            </Button>

            <Button
              color="inherit"
              component={RouterLink}
              to="/budget"
              startIcon={<AccountBalanceWalletIcon />}
              disabled={!selectedEventId && !localStorage.getItem('selectedEventId')}
            >
              {t('navigation.budget')}
            </Button>

            <Button
              color="inherit"
              component={RouterLink}
              to={selectedEventId ? `/resources/guests/${selectedEventId}` : "/events"}
              startIcon={<PeopleIcon />}
              disabled={!selectedEventId && !localStorage.getItem('selectedEventId')}
            >
              {t('navigation.guests')}
            </Button>

            <Button
              color="inherit"
              component={RouterLink}
              to={selectedEventId ? `/resources/venues/${selectedEventId}` : "/events"}
              startIcon={<PlaceIcon />}
              disabled={!selectedEventId && !localStorage.getItem('selectedEventId')}
            >
              {t('navigation.venues')}
            </Button>

            <Button
              color="inherit"
              component={RouterLink}
              to={selectedEventId ? `/resources/stakeholders/${selectedEventId}` : "/events"}
              startIcon={<BusinessIcon />}
              disabled={!selectedEventId && !localStorage.getItem('selectedEventId')}
            >
              {t('navigation.stakeholders')}
            </Button>

            <Button
              color="inherit"
              component={RouterLink}
              to={selectedEventId ? `/supplies/${selectedEventId}` : "/supplies"}
              startIcon={<ShoppingCartIcon />}
              disabled={!selectedEventId && !localStorage.getItem('selectedEventId')}
            >
              {t('navigation.supplies', 'Supplies')}
            </Button>
          </Box>
        ) : !isTabletOrMobile && (
          <Box sx={{ display: 'flex' }}>
            <Button
              color="inherit"
              component={RouterLink}
              to="/pricing"
            >
              {t('navigation.pricing')}
            </Button>
            <Button
              color="inherit"
              component={RouterLink}
              to="/about"
            >
              {t('navigation.about') || 'About Us'}
            </Button>
          </Box>
        )}
        <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center' }}>
          {/* Show Sign In/Sign Up buttons when not logged in */}
          {!user ? (
            <Stack direction="row" spacing={1}>
              <Button
                color="inherit"
                component={RouterLink}
                to="/login"
                startIcon={<LoginIcon />}
                sx={{
                  borderRadius: 2,
                  px: 2,
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                {t('auth.login')}
              </Button>
              <Button
                variant="contained"
                component={RouterLink}
                to="/register"
                startIcon={<PersonAddIcon />}
                sx={{
                  borderRadius: 2,
                  px: 2,
                  bgcolor: 'white',
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.9)'
                  }
                }}
              >
                {t('auth.register')}
              </Button>
            </Stack>
          ) : (
            <>
              {/* Notification Bell - Only show when logged in */}
              <NotificationBell />
              <LanguageSelector />

              {/* User Menu - Only show when logged in */}
              <Box sx={{ ml: 2 }}>
                <Tooltip title="Account settings">
                  <IconButton
                    onClick={handleMenuOpen}
                    size="small"
                    sx={{ ml: 2 }}
                    aria-controls="account-menu"
                    aria-haspopup="true"
                    aria-expanded={Boolean(anchorEl) ? 'true' : undefined}
                  >
                    <Avatar sx={{ width: 32, height: 32 }}>{user?.name?.charAt(0) || 'U'}</Avatar>
                  </IconButton>
                </Tooltip>
                <Menu
                  anchorEl={anchorEl}
                  id="account-menu"
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                  onClick={handleMenuClose}
                  PaperProps={{
                    elevation: 0,
                    sx: {
                      overflow: 'visible',
                      filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                      mt: 1.5,
                      '& .MuiAvatar-root': {
                        width: 32,
                        height: 32,
                        ml: -0.5,
                        mr: 1,
                      },
                      '&:before': {
                        content: '""',
                        display: 'block',
                        position: 'absolute',
                        top: 0,
                        right: 14,
                        width: 10,
                        height: 10,
                        bgcolor: 'background.paper',
                        transform: 'translateY(-50%) rotate(45deg)',
                        zIndex: 0,
                      },
                    },
                  }}
                  transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                  anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                >
                  <MenuItem onClick={handleMenuClose} component={RouterLink} to="/profile">
                    <ListItemIcon>
                      <AccountCircleIcon fontSize="small" />
                    </ListItemIcon>
                    {t('auth.profile')}
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={() => { handleMenuClose(); handleLogout(); }}>
                    <ListItemIcon>
                      <LogoutIcon fontSize="small" />
                    </ListItemIcon>
                    {t('auth.logout')}
                  </MenuItem>
                </Menu>
              </Box>
            </>
          )}
        </Box>
        <Drawer anchor="left" open={drawerOpen} onClose={() => setDrawerOpen(false)}>
          <Box sx={{ width: 250 }} role="presentation">
            {/* Event selection in Drawer for mobile/tablet */}
            {isTabletOrMobile && user && (
              <Box sx={{ p: 2, borderBottom: '1px solid #eee' }}>
                <Button
                  color="primary"
                  variant="outlined"
                  fullWidth
                  onClick={handleEventMenuOpen}
                  endIcon={<ArrowDropDownIcon />}
                  startIcon={<EventIcon />}
                  sx={{
                    textTransform: 'none',
                    borderRadius: 2,
                    minWidth: 200,
                    justifyContent: 'flex-start',
                    mb: 1
                  }}
                >
                  {currentEvent ? (
                    <Box sx={{ textAlign: 'left' }}>
                      <Typography variant="body1" fontWeight="bold" noWrap>
                        {currentEvent.title || 'Unnamed Event'}
                      </Typography>
                      <Typography variant="caption" noWrap sx={{ display: 'block' }}>
                        {formatEventDate(currentEvent.date) || 'No date set'}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography>
                      {loading ? t('events.loadingEvents') : events.length === 0 ? t('events.noEventsAvailable') : t('events.select')}
                    </Typography>
                  )}
                </Button>
                <Menu
                  anchorEl={eventMenuAnchor}
                  open={Boolean(eventMenuAnchor)}
                  onClose={handleEventMenuClose}
                  PaperProps={{
                    sx: { width: 320, maxHeight: 500 }
                  }}
                >
                  <MenuItem onClick={handleOpenEventDialog}>
                    <ListItemIcon>
                      <AddIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>{t('events.create')}</ListItemText>
                  </MenuItem>
                  {events.length > 0 ? (
                    <>
                      <Divider />
                      {events.map(event => (
                        <Box
                          key={event._id}
                          onMouseEnter={() => setHoveredEventId(event._id)}
                          onMouseLeave={() => setHoveredEventId(null)}
                          sx={{ position: 'relative' }}
                        >
                          <MenuItem
                            onClick={() => handleEventSelect(event._id)}
                            selected={selectedEventId === event._id}
                            sx={{
                              '&:hover': {
                                backgroundColor: theme.palette.action.hover
                              },
                              pr: 8,
                              position: 'relative',
                              '& .MuiListItemText-root': {
                                maxWidth: '70%'
                              }
                            }}
                          >
                            <ListItemIcon>
                              <EventIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={event.title || 'Unnamed Event'}
                              secondary={formatEventDate(event.date) || 'No date set'}
                            />
                          </MenuItem>
                          {hoveredEventId === event._id && (
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'flex-end',
                                position: 'absolute',
                                right: 12,
                                top: '50%',
                                transform: 'translateY(-50%)',
                                transition: 'opacity 0.2s ease',
                                opacity: 1,
                                zIndex: 2
                              }}
                            >
                              <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEventMenuClose();
                                  navigate(`/events/${event._id}`);
                                }}
                                sx={{
                                  py: 0.5,
                                  minWidth: '80px'
                                }}
                              >
                                Details
                              </Button>
                            </Box>
                          )}
                        </Box>
                      ))}
                    </>
                  ) : loading ? (
                    <MenuItem disabled>
                      <ListItemText>{t('events.loadingEvents')}</ListItemText>
                    </MenuItem>
                  ) : (
                    <MenuItem disabled>
                      <ListItemText>{t('events.noEvents')}</ListItemText>
                    </MenuItem>
                  )}
                </Menu>
              </Box>
            )}
            {/* Main navigation items for logged-in users */}
            {isTabletOrMobile && user && (
              <Box>
                <MenuItem component={RouterLink} to={selectedEventId ? `/tasks/${selectedEventId}` : '/events'} onClick={() => setDrawerOpen(false)}>{t('navigation.tasks')}</MenuItem>
                <MenuItem component={RouterLink} to="/calendar" onClick={() => setDrawerOpen(false)}>{t('navigation.calendar')}</MenuItem>
                <MenuItem component={RouterLink} to="/budget" onClick={() => setDrawerOpen(false)}>{t('navigation.budget')}</MenuItem>
                <MenuItem component={RouterLink} to={selectedEventId ? `/resources/guests/${selectedEventId}` : "/events"} onClick={() => setDrawerOpen(false)}>{t('navigation.guests')}</MenuItem>
                <MenuItem component={RouterLink} to={selectedEventId ? `/resources/venues/${selectedEventId}` : "/events"} onClick={() => setDrawerOpen(false)}>{t('navigation.venues')}</MenuItem>
                <MenuItem component={RouterLink} to={selectedEventId ? `/resources/stakeholders/${selectedEventId}` : "/events"} onClick={() => setDrawerOpen(false)}>{t('navigation.stakeholders')}</MenuItem>
                <MenuItem component={RouterLink} to={selectedEventId ? `/supplies/${selectedEventId}` : "/supplies"} onClick={() => setDrawerOpen(false)}>{t('navigation.supplies', 'Supplies')}</MenuItem>
                <Divider />
                <MenuItem component={RouterLink} to="/profile" onClick={() => setDrawerOpen(false)}>{t('auth.profile')}</MenuItem>
                <MenuItem onClick={() => { setDrawerOpen(false); handleLogout(); }}>{t('auth.logout')}</MenuItem>
              </Box>
            )}
            {/* ... rest of Drawer menu items ... */}
          </Box>
        </Drawer>
      </Toolbar>

      {/* Create Event Dialog */}
      <Dialog
        open={openEventDialog}
        onClose={handleCloseEventDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {t('events.create')}
            <IconButton onClick={handleCloseEventDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <EventForm onSubmit={handleEventSubmit} />
        </DialogContent>
      </Dialog>
    </AppBar>
  );
};

export default Navbar;