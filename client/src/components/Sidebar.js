import React, { useContext } from 'react';
import {
  Drawer,
  Toolbar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Tooltip,
  Badge
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
// Dashboard icon removed as per user request
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import ListAltIcon from '@mui/icons-material/ListAlt';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import AssignmentIcon from '@mui/icons-material/Assignment';
import { EventContext } from '../contexts/EventContext';

const drawerWidth = 240;

const Sidebar = ({ open }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { selectedEventId } = useContext(EventContext);

  const handleNavigation = (path) => {
    navigate(path);
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        [`& .MuiDrawer-paper`]: {
          width: drawerWidth,
          boxSizing: 'border-box',
          ...(open ? {} : {
            width: theme => theme.spacing(7),
            overflowX: 'hidden'
          })
        },
      }}
      open={open}
    >
      <Toolbar /> {/* This creates space for the AppBar */}
      <Box sx={{ overflow: 'auto' }}>
        <List>
          <ListItem
            button
            selected={location.pathname === '/' || location.pathname === '/events'}
            onClick={() => handleNavigation('/')}
          >
            <ListItemIcon>
              <ListAltIcon />
            </ListItemIcon>
            <ListItemText primary="Events" />
          </ListItem>

          <ListItem
            button
            selected={location.pathname.includes('/tasks')}
            onClick={() => {
              if (selectedEventId) {
                handleNavigation(`/events/${selectedEventId}/tasks`);
              } else {
                handleNavigation('/events');
              }
            }}
            disabled={!selectedEventId}
          >
            <ListItemIcon>
              <AssignmentIcon />
            </ListItemIcon>
            <ListItemText primary="Tasks" />
            {!selectedEventId && (
              <Tooltip title="Select an event first">
                <Box component="span" sx={{ ml: 1, color: 'text.secondary', fontSize: '0.75rem' }}>
                  (Select event)
                </Box>
              </Tooltip>
            )}
          </ListItem>

          <ListItem
            button
            selected={location.pathname === '/calendar'}
            onClick={() => handleNavigation('/calendar')}
            disabled={!selectedEventId}
          >
            <ListItemIcon>
              <CalendarMonthIcon />
            </ListItemIcon>
            <ListItemText primary="Calendar" />
            {!selectedEventId && (
              <Tooltip title="Select an event first">
                <Box component="span" sx={{ ml: 1, color: 'text.secondary', fontSize: '0.75rem' }}>
                  (Select event)
                </Box>
              </Tooltip>
            )}
          </ListItem>

          <ListItem
            button
            selected={location.pathname === '/templates'}
            onClick={() => handleNavigation('/templates')}
          >
            <ListItemIcon>
              <AccountTreeIcon />
            </ListItemIcon>
            <ListItemText primary="Templates" />
          </ListItem>
        </List>

        <Divider />

        <List>
          <ListItem button>
            <ListItemIcon>
              <SettingsIcon />
            </ListItemIcon>
            <ListItemText primary="Settings" />
          </ListItem>

          <ListItem button>
            <ListItemIcon>
              <LogoutIcon />
            </ListItemIcon>
            <ListItemText primary="Logout" />
          </ListItem>
        </List>
      </Box>
    </Drawer>
  );
};

export default Sidebar;