import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container,
  Stack,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import EventIcon from '@mui/icons-material/Event';
import LanguageSelector from './LanguageSelector';

const PublicNavbar = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t } = useTranslation();

  return (
    <AppBar position="static" sx={{ background: 'linear-gradient(90deg, #6200ea, #03dac6)' }}>
      <Container maxWidth="lg">
        <Toolbar disableGutters>
          {/* Logo and Brand */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EventIcon sx={{ mr: 1, fontSize: 28 }} />
            <Typography
              variant="h6"
              component={RouterLink}
              to="/"
              sx={{
                flexGrow: 0,
                textDecoration: 'none',
                color: 'inherit',
                fontWeight: 'bold',
                letterSpacing: '0.5px'
              }}
            >
              {t('app.title')}
            </Typography>
          </Box>

          {/* Navigation Links */}
          <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: isMobile ? 'flex-end' : 'center' }}>
            {!isMobile && (
              <>
                <Button
                  color="inherit"
                  component={RouterLink}
                  to="/"
                  sx={{ mx: 1 }}
                >
                  {t('navigation.home') || 'Home'}
                </Button>
                <Button
                  color="inherit"
                  component={RouterLink}
                  to="/pricing"
                  sx={{ mx: 1 }}
                >
                  {t('navigation.pricing')}
                </Button>
                <Button
                  color="inherit"
                  component={RouterLink}
                  to="/features"
                  sx={{ mx: 1 }}
                >
                  {t('navigation.features')}
                </Button>
                <Button
                  color="inherit"
                  component={RouterLink}
                  to="/about"
                  sx={{ mx: 1 }}
                >
                  {t('navigation.about') || 'About Us'}
                </Button>
              </>
            )}
          </Box>

          {/* Auth Buttons */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LanguageSelector />
            <Stack direction="row" spacing={1} sx={{ ml: 1 }}>
              <Button
                color="inherit"
                component={RouterLink}
                to="/login"
                startIcon={!isMobile && <LoginIcon />}
                sx={{
                  borderRadius: 2,
                  px: isMobile ? 1 : 2,
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                {t('auth.login')}
              </Button>
              <Button
                variant="contained"
                component={RouterLink}
                to="/register"
                startIcon={!isMobile && <PersonAddIcon />}
                sx={{
                  borderRadius: 2,
                  px: isMobile ? 1 : 2,
                  bgcolor: 'white',
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.9)'
                  }
                }}
              >
                {isMobile ? t('auth.register') : `${t('auth.register')} ${t('common.free') || 'Free'}`}
              </Button>
            </Stack>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default PublicNavbar;
