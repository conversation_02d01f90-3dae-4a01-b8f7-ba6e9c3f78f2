import React, { useCallback } from 'react';
import { <PERSON><PERSON>, Position, NodeResizer } from 'reactflow';
import {
  Box,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  Divider,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import LinkIcon from '@mui/icons-material/Link';
import { format } from 'date-fns';

export const HEADER_H = 225;          // used by graph as well

const ContainerNode = ({ data, selected }) => {
  const { task, onEdit, onConnect, isCreatingEdge } = data;

  const fmt = (d) => (d ? format(new Date(d), 'MMM d') : '');
  const statusClr = (s) =>
    ({ Completed: 'success', 'In Progress': 'info', Delayed: 'warning', Cancelled: 'error' }[s] ??
    'default');

  const onResize = useCallback((_, p) => console.log('Container resized', p), []);

  return (
    <Box
      sx={{
        borderRadius: 1,
        background: 'rgba(255,255,255,.9)',
        border: '1px solid',
        borderColor: selected || isCreatingEdge ? 'primary.main' : 'divider',
        boxShadow: selected || isCreatingEdge ? 4 : 2,
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        height: '100%',
        width: '100%',
      }}
    >
      <NodeResizer
        minWidth={300}
        minHeight={350}
        isVisible={selected}
        onResize={onResize}
        keepAspectRatio={false}
        handleStyle={{ width: 8, height: 8, borderRadius: 4, background: '#1976d2', borderColor: '#fff' }}
        lineStyle={{ borderWidth: 1, borderColor: '#1976d2', borderStyle: 'dashed' }}
      />

      <Handle type="source" position={Position.Right} style={{ background: '#555', width: 10, height: 10 }} />
      <Handle type="target" position={Position.Left}  style={{ background: '#555', width: 10, height: 10 }} />

      {/* header -------------------------------------------------------- */}
      <Box sx={{ p: 1.5, background: 'background.paper', zIndex: 5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: .5 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>{task.name}</Typography>
          <Box>
            <Tooltip title="Create dependency">
              <IconButton size="small" onClick={() => onConnect()}>
                <LinkIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit">
              <IconButton size="small" onClick={() => onEdit(task)}>
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, mb: .5 }}>
          <Chip label={task.taskType} size="small" />
          <Chip
            label={`${task.subtasks?.length ?? 0} subtasks`}
            size="small"
            color="primary"
            variant="outlined"
            sx={{ fontSize: '.75rem' }}
          />
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: .5 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">Status:</Typography>
            <Chip size="small" label={task.status} color={statusClr(task.status)} />
          </Box>

          {task.startTime && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">Start:</Typography>
              <Typography variant="body2">{fmt(task.startTime)}</Typography>
            </Box>
          )}

          {task.duration && task.duration !== '00:00:00' && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">Duration:</Typography>
              <Typography variant="body2">{task.duration.split(':').slice(0, 2).join(':')}</Typography>
            </Box>
          )}

          {task.assignees?.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">Assignee:</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {task.assignees.length === 1 ? (
                  <>
                    <Avatar sx={{ width: 20, height: 20, fontSize: '.75rem', mr: .5 }}>
                      {task.assignees[0].name ? task.assignees[0].name[0] : '?'}
                    </Avatar>
                    <Typography variant="body2" noWrap sx={{ maxWidth: 100 }}>{task.assignees[0].name}</Typography>
                  </>
                ) : (
                  <Chip size="small" label={`${task.assignees.length} people`} sx={{ fontSize: '.7rem' }} />
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Box>

      <Divider sx={{ borderStyle: 'dashed' }} />

      {/* sub‑task area -------------------------------------------------- */}
      <Box
        sx={{
          flexGrow: 1,
          position: 'relative',
          p: 2,
          background: 'rgba(245,245,245,.8)',
          zIndex: 1,
          border: '1px dashed rgba(0,0,0,.25)',
          borderRadius: '0 0 4px 4px',
          backgroundImage:
            'linear-gradient(rgba(0,0,0,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(0,0,0,.03) 1px,transparent 1px)',
          backgroundSize: '20px 20px',
        }}
        className="subtasks-container"
      >
        <div
          style={{
            position: 'absolute',
            top: 5,
            left: 10,
            fontSize: 12,
            fontWeight: 'bold',
            color: 'rgba(0,0,0,.5)',
            background: 'rgba(255,255,255,.7)',
            padding: '3px 6px',
            borderRadius: 4,
          }}
        >
          {task.subtasks?.length ? `${task.subtasks.length} subtasks` : 'No subtasks'}
        </div>
      </Box>
    </Box>
  );
};

export default ContainerNode;