import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  CardContent,
  CardActionArea,
  CardActions,
  Typography,
  Box,
  Chip,
  Divider,
  Button,
  Tooltip,
  LinearProgress,
  Stack
} from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import PeopleIcon from '@mui/icons-material/People';
import InfoIcon from '@mui/icons-material/Info';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import { useNavigate } from 'react-router-dom';
import { getTaskStatistics } from '../services/taskService';
import BudgetProgressBar from './BudgetProgressBar';

const EventCard = ({ event, onClick }) => {
  const { t } = useTranslation();
  const eventDate = new Date(event.date);
  const daysRemaining = Math.ceil((eventDate - new Date()) / (1000 * 60 * 60 * 24));
  const navigate = useNavigate();
  const [taskStats, setTaskStats] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch task statistics when component mounts
  useEffect(() => {
    const fetchTaskStats = async () => {
      try {
        if (event._id) {
          const stats = await getTaskStatistics(event._id);
          setTaskStats(stats);
        }
      } catch (error) {
        console.error('Error fetching task statistics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTaskStats();
  }, [event._id]);

  const handleManageMembers = (e) => {
    e.stopPropagation(); // Prevent card click event
    navigate(`/events/${event._id}`, { state: { openMemberManager: true } });
  };

  const handleViewDetails = (e) => {
    e.stopPropagation(); // Prevent card click event
    navigate(`/events/${event._id}`);
  };

  // Get progress bar color based on completion percentage
  const getProgressColor = (progress) => {
    if (progress >= 75) return 'success';
    if (progress >= 40) return 'info';
    return 'warning';
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardActionArea onClick={onClick || handleViewDetails} sx={{ flexGrow: 1 }}>
        <CardContent>
          <Box sx={{ mb: 2 }}>
            <Chip
              label={event.eventType}
              color="primary"
              size="small"
              sx={{ mb: 1 }}
            />
            <Typography variant="h5" component="div" gutterBottom>
              {event.title}
            </Typography>
          </Box>

          <Divider sx={{ my: 1 }} />

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <CalendarTodayIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              {eventDate.toLocaleDateString()}
              {daysRemaining > 0 ? t('eventCard.daysRemaining', ' ({{days}} days remaining)', { days: daysRemaining }) : t('eventCard.pastEvent', ' (Past event)')}
            </Typography>
          </Box>

          {event.venue && event.venue.name && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <LocationOnIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {event.venue.name}
              </Typography>
            </Box>
          )}

          {event.budget && event.budget.total && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AttachMoneyIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Budget: {event.budget.total.toLocaleString()} {event.budget.currency}
              </Typography>
            </Box>
          )}

          {/* Task Progress Section */}
          <Box sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="body2" color="text.secondary">
                {t('events.taskProgress', 'Task Progress')}
              </Typography>
              {taskStats && (
                <Typography variant="body2" color="text.secondary">
                  {taskStats.completed} / {taskStats.total - taskStats.cancelled} {t('common.tasks', 'tasks')}
                </Typography>
              )}
            </Box>

            {loading ? (
              <LinearProgress />
            ) : taskStats && taskStats.total > 0 ? (
              <>
                <LinearProgress
                  variant="determinate"
                  value={taskStats.progress}
                  color={getProgressColor(taskStats.progress)}
                  sx={{ height: 8, borderRadius: 4 }}
                />

                <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                  <Chip
                    icon={<CheckCircleIcon />}
                    label={`${taskStats.completed} ${t('taskStatus.completed', 'Completed')}`}
                    size="small"
                    color="success"
                    sx={{ height: 24, fontSize: '0.7rem' }}
                  />
                  <Chip
                    icon={<HourglassEmptyIcon />}
                    label={`${taskStats.inProgress} ${t('taskStatus.inProgress', 'In Progress')}`}
                    size="small"
                    color="info"
                    sx={{ height: 24, fontSize: '0.7rem' }}
                  />
                </Stack>
              </>
            ) : (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                {t('events.noTasks', 'No tasks created yet')}
              </Typography>
            )}
          </Box>

          {/* Budget Progress Bar */}
          {event.budget && event.budget.total > 0 && (
            <BudgetProgressBar eventId={event._id} eventBudget={event.budget} />
          )}

          {event.description && (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                mt: 2,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              }}
            >
              {event.description}
            </Typography>
          )}
        </CardContent>
      </CardActionArea>
      <Divider />
      <CardActions sx={{ justifyContent: 'space-between', px: 2, py: 1 }}>
        <Tooltip title={t('eventCard.tooltips.manageMembers', 'Manage event members and invitations')}>
          <Button
            size="small"
            color="primary"
            startIcon={<PeopleIcon />}
            onClick={handleManageMembers}
          >
            {t('eventCard.members', 'Members')}
          </Button>
        </Tooltip>
        <Tooltip title={t('eventCard.tooltips.viewDetails', 'View event details')}>
          <Button
            size="small"
            color="primary"
            startIcon={<InfoIcon />}
            onClick={handleViewDetails}
          >
            {t('eventCard.details', 'Details')}
          </Button>
        </Tooltip>
      </CardActions>
    </Card>
  );
};

export default EventCard;