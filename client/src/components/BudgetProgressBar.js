import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  LinearProgress,
  Tooltip,
  Paper,
  Stack,
  Divider
} from '@mui/material';
import { getEventBudget, calculateBudgetSummary } from '../services/budgetService';

const BudgetProgressBar = ({ eventId, eventBudget }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [budgetSummary, setBudgetSummary] = useState({
    totalEstimated: 0,
    totalActual: 0
  });

  useEffect(() => {
    const fetchBudgetData = async () => {
      try {
        if (eventId) {
          const budgetData = await getEventBudget(eventId);
          const summary = calculateBudgetSummary(budgetData);
          setBudgetSummary(summary);
        }
      } catch (error) {
        console.error('Error fetching budget data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBudgetData();
  }, [eventId]);

  // Format currency
  const formatCurrency = (amount, currency = 'USD') => {
    return `${amount.toLocaleString()} ${currency}`;
  };

  // Calculate progress percentages
  const calculateActualProgress = () => {
    if (!eventBudget || !eventBudget.total || eventBudget.total === 0) {
      return 0;
    }
    return Math.min(100, (budgetSummary.totalActual / eventBudget.total) * 100);
  };

  const calculateAllocatedProgress = () => {
    if (!eventBudget || !eventBudget.total || eventBudget.total === 0) {
      return 0;
    }
    return Math.min(100, (budgetSummary.totalEstimated / eventBudget.total) * 100);
  };

  // Determine budget status
  const isActualOverBudget = eventBudget && eventBudget.total && budgetSummary.totalActual > eventBudget.total;
  const isAllocatedOverBudget = eventBudget && eventBudget.total && budgetSummary.totalEstimated > eventBudget.total;

  // Calculate differences
  const actualOverBudgetAmount = isActualOverBudget ? budgetSummary.totalActual - eventBudget.total : 0;
  const allocatedOverBudgetAmount = isAllocatedOverBudget ? budgetSummary.totalEstimated - eventBudget.total : 0;
  const unallocatedAmount = Math.max(0, eventBudget?.total - budgetSummary.totalEstimated);
  const unspentAllocatedAmount = Math.max(0, budgetSummary.totalEstimated - budgetSummary.totalActual);

  if (loading) {
    return <LinearProgress />;
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
        <Typography variant="body2" color="text.secondary" fontWeight="medium">
          {t('budget.title', 'Budget')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {formatCurrency(eventBudget?.total || 0, eventBudget?.currency)}
        </Typography>
      </Box>

      {/* Level 1: Total Project Budget */}
      <Paper
        elevation={0}
        sx={{
          p: 1,
          mb: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.03)',
          borderRadius: 1,
          border: '1px solid rgba(0, 0, 0, 0.08)'
        }}
      >
        <Stack spacing={1.5}>
          {/* Level 2: Allocated Budget */}
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="caption" color="text.secondary">
                {t('budget.allocated', 'Allocated Budget')}
              </Typography>
              <Tooltip title={
                isAllocatedOverBudget
                  ? t('budget.overAllocated', 'Over-allocated by {{amount}}', { amount: formatCurrency(allocatedOverBudgetAmount, eventBudget?.currency) })
                  : t('budget.unallocated', 'Unallocated: {{amount}}', { amount: formatCurrency(unallocatedAmount, eventBudget?.currency) })
              }>
                <Typography variant="caption" color={isAllocatedOverBudget ? 'warning.main' : 'text.secondary'}>
                  {formatCurrency(budgetSummary.totalEstimated, eventBudget?.currency)}
                </Typography>
              </Tooltip>
            </Box>
            <Tooltip title={t('budget.allocatedTooltip', '{{percent}}% of total budget allocated', { percent: calculateAllocatedProgress().toFixed(0) })}>
              <LinearProgress
                variant="determinate"
                value={calculateAllocatedProgress()}
                color={isAllocatedOverBudget ? 'warning' : 'info'}
                sx={{
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: 'rgba(0, 0, 0, 0.08)'
                }}
              />
            </Tooltip>
          </Box>

          {/* Level 3: Actual Cost */}
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="caption" color="text.secondary">
                {t('budget.actual', 'Actual Cost')}
              </Typography>
              <Tooltip title={
                isActualOverBudget
                  ? t('budget.overBudget', 'Over budget by {{amount}}', { amount: formatCurrency(actualOverBudgetAmount, eventBudget?.currency) })
                  : t('budget.underBudget', 'Under budget by {{amount}}', { amount: formatCurrency(eventBudget?.total - budgetSummary.totalActual, eventBudget?.currency) })
              }>
                <Typography variant="caption" color={isActualOverBudget ? 'error.main' : 'text.secondary'}>
                  {formatCurrency(budgetSummary.totalActual, eventBudget?.currency)}
                </Typography>
              </Tooltip>
            </Box>
            <Tooltip title={t('budget.actualTooltip', '{{percent}}% of total budget spent', { percent: calculateActualProgress().toFixed(0) })}>
              <LinearProgress
                variant="determinate"
                value={calculateActualProgress()}
                color={isActualOverBudget ? 'error' : 'success'}
                sx={{
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: 'rgba(0, 0, 0, 0.08)'
                }}
              />
            </Tooltip>
          </Box>
        </Stack>
      </Paper>

      {/* Budget Summary */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
        {isActualOverBudget && (
          <Typography variant="caption" color="error.main">
            {t('budget.overBudgetWarning', 'Over budget by {{amount}}', { amount: formatCurrency(actualOverBudgetAmount, eventBudget?.currency) })}
          </Typography>
        )}
        {!isActualOverBudget && budgetSummary.totalEstimated > budgetSummary.totalActual && (
          <Typography variant="caption" color="success.main">
            {t('budget.unspentAllocated', 'Unspent allocated: {{amount}}', { amount: formatCurrency(unspentAllocatedAmount, eventBudget?.currency) })}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default BudgetProgressBar;
