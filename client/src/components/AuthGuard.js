import React from 'react';
import { Navigate } from 'react-router-dom';

const AuthGuard = ({ children }) => {
  // Simple check if user exists in localStorage
  const isAuthenticated = !!localStorage.getItem('user');
  
  if (!isAuthenticated) {
    // User is not authenticated, redirect to login
    return <Navigate to="/login" replace />;
  }
  
  // User is authenticated, render the children
  return children;
};

export default AuthGuard; 