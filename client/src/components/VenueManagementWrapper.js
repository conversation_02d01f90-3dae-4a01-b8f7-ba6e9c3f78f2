import React, { useContext, useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { EventContext } from '../contexts/EventContext';
import VenueManagement from '../pages/resources/VenueManagement';

export default function VenueManagementWrapper() {
  const { eventId } = useParams();
  const { selectedEventId, setSelectedEventId } = useContext(EventContext);
  const navigate = useNavigate();
  const [initialized, setInitialized] = useState(false);

  // On component mount, check localStorage for the selected event ID
  useEffect(() => {
    const savedEventId = localStorage.getItem('selectedEventId');
    console.log('VenueManagementWrapper: Component mounted, checking localStorage:', savedEventId);
    console.log('VenueManagementWrapper: Current URL eventId:', eventId);
    console.log('VenueManagementWrapper: Current context selectedEventId:', selectedEventId);

    // If we have an event ID in the URL, it takes precedence
    if (eventId) {
      console.log('VenueManagementWrapper: Using event ID from URL:', eventId);
      if (eventId !== selectedEventId) {
        setSelectedEventId(eventId);
        // Also update localStorage to ensure consistency
        localStorage.setItem('selectedEventId', eventId);
      }
    }
    // If we have a saved event ID in localStorage but not in the context, restore it
    else if (savedEventId && (!selectedEventId || selectedEventId !== savedEventId)) {
      console.log('VenueManagementWrapper: Restoring event ID from localStorage:', savedEventId);
      setSelectedEventId(savedEventId);
      // Redirect to include the event ID in the URL
      navigate(`/resources/venues/${savedEventId}`);
    }
    // If we have a selected event ID in the context but not in the URL, redirect
    else if (selectedEventId) {
      console.log('VenueManagementWrapper: Redirecting to include event ID in URL:', selectedEventId);
      navigate(`/resources/venues/${selectedEventId}`);
    }
    // If we don't have any event ID, redirect to events page
    else {
      console.log('VenueManagementWrapper: No event selected, redirecting to events page');
      navigate('/events');
    }

    setInitialized(true);
  }, []); // Empty dependency array means this runs once on mount

  // Handle URL and selectedEventId changes after initialization
  useEffect(() => {
    if (!initialized) return;
    console.log('VenueManagementWrapper: Checking URL and context state after initialization');
    console.log('VenueManagementWrapper: URL eventId:', eventId);
    console.log('VenueManagementWrapper: Context selectedEventId:', selectedEventId);

    // If we have an event ID in the URL, it takes precedence
    if (eventId) {
      // Only update if they're different to avoid infinite loops
      if (eventId !== selectedEventId) {
        console.log('VenueManagementWrapper: Setting selectedEventId to match URL:', eventId);
        setSelectedEventId(eventId);
        localStorage.setItem('selectedEventId', eventId);
      }
    }
    // If we have a selected event ID but it's not in the URL, update the URL
    else if (selectedEventId) {
      console.log('VenueManagementWrapper: Updating URL to match selectedEventId:', selectedEventId);
      navigate(`/resources/venues/${selectedEventId}`, { replace: true });
    }

    // Always ensure localStorage is in sync with the selected event ID
    if (selectedEventId) {
      localStorage.setItem('selectedEventId', selectedEventId);
    }
  }, [eventId, initialized, selectedEventId, navigate]);

  return <VenueManagement />;
}
