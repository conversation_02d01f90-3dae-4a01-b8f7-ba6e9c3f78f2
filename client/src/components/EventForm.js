import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  TextField,
  MenuItem,
  Button,
  Typography,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  Paper,
  Divider,
  FormHelperText,
  InputAdornment
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import config from '../config';

const EVENT_TYPES = [
  'Wedding',
  'Birthday',
  'Corporate',
  'Anniversary',
  'Other'
];

const EventForm = ({ event, onSubmit, templates }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    title: '',
    eventType: '',
    date: null,
    description: '',
    venue: {
      name: '',
      address: ''
    },
    budget: {
      total: '',
      currency: 'USD'
    },
    templateId: ''
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (event) {
      setFormData({
        title: event.title || '',
        eventType: event.eventType || '',
        date: event.date ? new Date(event.date) : null,
        description: event.description || '',
        venue: event.venue || {
          name: '',
          address: ''
        },
        budget: event.budget || {
          total: '',
          currency: 'USD'
        },
        templateId: ''
      });
    }
  }, [event]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle nested objects
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: value
        }
      });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleDateChange = (newDate) => {
    setFormData({ ...formData, date: newDate });
  };

  const validate = () => {
    const newErrors = {};

    if (!formData.title) {
      newErrors.title = 'Event title is required';
    }

    if (!formData.eventType) {
      newErrors.eventType = 'Event type is required';
    }

    if (!formData.date) {
      newErrors.date = 'Event date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validate()) {
      onSubmit(formData);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Paper sx={{ p: 3 }}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Event Title */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('eventForm.title', 'Event Title')}
                name="title"
                value={formData.title}
                onChange={handleChange}
                error={!!errors.title}
                helperText={errors.title}
                required
              />
            </Grid>

            {/* Event Type */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.eventType}>
                <InputLabel>{t('eventForm.eventType', 'Event Type')} *</InputLabel>
                <Select
                  name="eventType"
                  value={formData.eventType}
                  onChange={handleChange}
                  label={`${t('eventForm.eventType', 'Event Type')} *`}
                  required
                >
                  {EVENT_TYPES.map(type => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
                {errors.eventType && <FormHelperText>{errors.eventType}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Event Date */}
            <Grid item xs={12} sm={6}>
              <DatePicker
                label={`${t('eventForm.date', 'Event Date')} *`}
                value={formData.date}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    error: !!errors.date,
                    helperText: errors.date
                  }
                }}
              />
            </Grid>

            {/* Venue Name */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('eventForm.venueName', 'Venue Name')}
                name="venue.name"
                value={formData.venue.name}
                onChange={handleChange}
                placeholder={t('eventForm.venueNamePlaceholder', 'Where will the event be held?')}
              />
            </Grid>

            {/* Venue Address */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('eventForm.venueAddress', 'Venue Address')}
                name="venue.address"
                value={formData.venue.address}
                onChange={handleChange}
                placeholder={t('eventForm.venueAddressPlaceholder', 'Full address of the venue')}
              />
            </Grid>

            {/* Budget Amount */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('eventForm.budget', 'Budget')}
                name="budget.total"
                type="number"
                value={formData.budget.total}
                onChange={handleChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      {formData.budget.currency}
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>

            {/* Budget Currency */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Currency</InputLabel>
                <Select
                  name="budget.currency"
                  value={formData.budget.currency}
                  onChange={handleChange}
                  label="Currency"
                >
                  {config.CURRENCIES.map(currency => (
                    <MenuItem key={currency} value={currency}>{currency}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Template Selection */}
            {templates && templates.length > 0 && (
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Template (Optional)</InputLabel>
                  <Select
                    name="templateId"
                    value={formData.templateId}
                    onChange={handleChange}
                    label="Template (Optional)"
                  >
                    <MenuItem value="">No Template</MenuItem>
                    {templates.map(template => (
                      <MenuItem key={template._id} value={template._id}>
                        {template.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>
                    Select a template to pre-populate tasks for this event
                  </FormHelperText>
                </FormControl>
              </Grid>
            )}

            {/* Description */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('eventForm.description', 'Description')}
                name="description"
                value={formData.description}
                onChange={handleChange}
                multiline
                rows={4}
                placeholder={t('eventForm.descriptionPlaceholder', 'Provide additional details about the event')}
              />
            </Grid>

            {/* Submit Button */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                >
                  {event ? t('eventForm.updateEvent', 'Update Event') : t('eventForm.createEvent', 'Create Event')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </LocalizationProvider>
  );
};

export default EventForm;