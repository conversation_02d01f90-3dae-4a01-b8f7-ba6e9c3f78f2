import React, { useState, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { setLanguage, setUserLanguage } from '../services/languageService';
import { AuthContext } from '../contexts/AuthContext';
import {
  Button,
  Menu,
  MenuItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import TranslateIcon from '@mui/icons-material/Translate';
import CheckIcon from '@mui/icons-material/Check';

const LanguageSelector = () => {
  const { t, i18n } = useTranslation();
  const { user } = useContext(AuthContext);
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const changeLanguage = (lng) => {
    // Change the language locally for immediate feedback
    i18n.changeLanguage(lng);
    handleClose();

    // Then update the server in the background (don't wait for it)
    if (user) {
      // For authenticated users, update the preference in the database
      setUserLanguage(lng).catch(error => {
        console.error('Error saving language preference:', error);
      });
    } else {
      // For guests, just update the cookie
      setLanguage(lng).catch(error => {
        console.error('Error saving language preference:', error);
      });
    }
  };

  // Normalize language code to match our translation keys
  const normalizeLanguage = (langCode) => {
    // If the language code is zh-HK or zh-CN, map it to zh-TW
    if (langCode.startsWith('zh')) {
      return 'zh-TW';
    }
    // For other languages, use the base language code (e.g., 'en' from 'en-US')
    return langCode.split('-')[0] === 'en' ? 'en' : langCode;
  };

  const currentLanguage = normalizeLanguage(i18n.language);

  return (
    <>
      <Button
        color="inherit"
        onClick={handleClick}
        startIcon={<TranslateIcon />}
        sx={{ ml: 1 }}
      >
        {t(`languages.${currentLanguage}`, currentLanguage)}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        <MenuItem
          onClick={() => changeLanguage('en')}
          selected={currentLanguage === 'en'}
        >
          <ListItemText primary={t('languages.en')} />
          {currentLanguage === 'en' && (
            <ListItemIcon sx={{ minWidth: 'auto', ml: 1 }}>
              <CheckIcon fontSize="small" />
            </ListItemIcon>
          )}
        </MenuItem>
        <MenuItem
          onClick={() => changeLanguage('zh-TW')}
          selected={currentLanguage === 'zh-TW'}
        >
          <ListItemText primary={t('languages.zh-TW')} />
          {currentLanguage === 'zh-TW' && (
            <ListItemIcon sx={{ minWidth: 'auto', ml: 1 }}>
              <CheckIcon fontSize="small" />
            </ListItemIcon>
          )}
        </MenuItem>
      </Menu>
    </>
  );
};

export default LanguageSelector;
