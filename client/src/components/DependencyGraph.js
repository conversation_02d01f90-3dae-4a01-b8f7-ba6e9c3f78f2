/* eslint-disable no-console */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  useTheme,
} from '@mui/material';
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  MarkerType,
  Panel,
  applyNodeChanges,
  ReactFlowProvider,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';

import AddIcon from '@mui/icons-material/Add';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import FitScreenIcon from '@mui/icons-material/FitScreen';
import GridViewIcon from '@mui/icons-material/GridView';

import TaskNode from './TaskNode';
import ContainerNode, { HEADER_H } from './ContainerNode';
import SubtaskNode from './SubtaskNode';
import CustomEdge from './CustomEdge';

import dagre from 'dagre';

/* ------------------------------------------------------------------ */
/*  node / edge registration                                          */
/* ------------------------------------------------------------------ */

const nodeTypes = {
  taskNode: TaskNode,
  containerNode: ContainerNode,
  subtaskNode: SubtaskNode,
};
const edgeTypes = { custom: CustomEdge };

/* ------------------------------------------------------------------ */
/*  constants                                                         */
/* ------------------------------------------------------------------ */

const NODE_W = 230;
const NODE_H = 200;
const SUB_W = 180;
const SUB_H = 100;
const PAD = 20;

const handleRx = /^sub-(?:source|target)-(.+)$/;

/* ------------------------------------------------------------------ */
/*  helpers                                                           */
/* ------------------------------------------------------------------ */

const convertLegacy = (raw) => {
  const byId = Object.fromEntries(raw.map((t) => [t._id, { ...t }]));
  raw.forEach((t) => {
    if (!t.parentTask) return;
    const p = byId[t.parentTask];
    if (p) {
      p.subtasks = p.subtasks ?? [];
      p.subtasks.push(byId[t._id]);
    }
  });
  return Object.values(byId).filter((t) => !t.parentTask);
};

const calcContainerDims = (task) => {
  if (!task.subtasks?.length) return { width: NODE_W, height: NODE_H };

  const cols = 2;
  const rows = Math.ceil(task.subtasks.length / cols);

  const width = Math.max(350, cols * SUB_W + (cols + 1) * PAD * 2);
  const height =
    HEADER_H + PAD + rows * SUB_H + (rows - 1) * PAD * 2 + PAD * 2;

  return { width, height: Math.max(350, height) };
};

const ensureContainers = (nodes) => {
  const byParent = {};
  nodes.forEach((n) => {
    if (n.type === 'subtaskNode' && n.parentNode) {
      (byParent[n.parentNode] = byParent[n.parentNode] || []).push(n);
    }
  });

  return nodes.map((n) => {
    if (n.type !== 'containerNode') return n;
    const kids = byParent[n.id] || [];
    if (!kids.length) return n;

    let maxX = 0;
    let maxY = 0;
    kids.forEach((k) => {
      const w = k.style?.width || SUB_W;
      const h = k.style?.height || SUB_H;
      maxX = Math.max(maxX, k.position.x + w);
      maxY = Math.max(maxY, k.position.y + h);
    });

    maxX += PAD * 2;
    maxY += PAD * 2;

    return {
      ...n,
      style: {
        ...n.style,
        width: Math.max(350, maxX),
        height: Math.max(350, maxY),
      },
    };
  });
};

const layout = (nodes, edges, dir = 'LR') => {
  const g = new dagre.graphlib.Graph();
  g.setDefaultEdgeLabel(() => ({}));
  g.setGraph({ rankdir: dir, nodesep: 100, ranksep: 150 });

  nodes.forEach((n) => {
    const dims =
      n.type === 'containerNode'
        ? calcContainerDims(n.data.task)
        : n.type === 'subtaskNode'
          ? { width: SUB_W, height: SUB_H }
          : { width: NODE_W, height: NODE_H };
    g.setNode(n.id, dims);
  });
  edges.forEach((e) => g.setEdge(e.source, e.target));
  dagre.layout(g);

  return nodes.map((n) => {
    if (n.type === 'subtaskNode') return n; // relative to parent
    const { width, height } =
      n.type === 'containerNode'
        ? calcContainerDims(n.data.task)
        : { width: NODE_W, height: NODE_H };
    const p = g.node(n.id);
    return {
      ...n,
      position: { x: p.x - width / 2, y: p.y - height / 2 },
      style: { ...n.style, width, height },
    };
  });
};

/* ------------------------------------------------------------------ */
/*  component                                                         */
/* ------------------------------------------------------------------ */

const DependencyGraph = ({ tasks = [] }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const rf = useReactFlow();

  const [nodes, setNodes] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [loading, setLoading] = useState(true);
  const [dir, setDir] = useState('LR');

  const [creating, setCreating] = useState(false);
  const [startId, setStartId] = useState(null);

  /* ------------------------------------------------------------------ */
  /*  build graph when tasks change                                     */
  /* ------------------------------------------------------------------ */
  useEffect(() => {
    setLoading(true);
    try {
      const active = tasks.filter((t) => !t.deleted);
      const top = convertLegacy(active);

      const ns = [];
      top.forEach((t) => {
        if (t.subtasks?.length) {
          const dims = calcContainerDims(t);
          ns.push({
            id: t._id,
            type: 'containerNode',
            position: { x: 0, y: 0 },
            style: dims,
            data: {
              task: t,
              onConnect: () => beginEdge(t._id),
              isCreatingEdge: creating && startId === t._id,
              onEdit: () => {},
            },
          });

          t.subtasks.forEach((st, i) => {
            const col = i % 2;
            const row = Math.floor(i / 2);
            const x = PAD + col * (SUB_W + PAD * 2);
            const y = HEADER_H + PAD + row * (SUB_H + PAD * 2);
            ns.push({
              id: st._id,
              type: 'subtaskNode',
              parentNode: t._id,
              extent: 'parent',
              position: { x, y },
              draggable: true,
              style: { width: SUB_W, height: SUB_H },
              data: {
                task: st,
                onConnect: () => beginEdge(st._id),
                isCreatingEdge: creating && startId === st._id,
                onEdit: () => {},
              },
            });
          });
        } else {
          ns.push({
            id: t._id,
            type: 'taskNode',
            position: { x: 0, y: 0 },
            style: { width: NODE_W, height: NODE_H },
            data: {
              task: t,
              onConnect: () => beginEdge(t._id),
              isCreatingEdge: creating && startId === t._id,
              onEdit: () => {},
            },
          });
        }
      });

      const es = [];
      active.forEach((obj) => {
        const add = (s, tg) => {
          const id = `${s}-${tg}`;
          if (!es.some((e) => e.id === id))
            es.push({
              id,
              source: s,
              target: tg,
              type: 'custom',
              animated: true,
              style: { stroke: theme.palette.primary.main },
              markerEnd: { type: MarkerType.ArrowClosed, color: theme.palette.primary.main },
              data: { curvature: 0.2, color: theme.palette.primary.main },
            });
        };
        const scan = (o) => (o.dependencies || []).forEach((d) => add(d, o._id));
        scan(obj);
        obj.subtasks?.forEach(scan);
      });

      const laid = layout(ns, es, dir);
      setNodes(ensureContainers(laid));
      setEdges(es);
    } finally {
      setLoading(false);
    }
  }, [tasks, dir, creating, startId, theme.palette.primary.main]);

  /* ------------------------------------------------------------------ */
  /*  edge creation callbacks                                           */
  /* ------------------------------------------------------------------ */
  const beginEdge = (id) => {
    setCreating(true);
    setStartId(id);
  };
  const cancelEdge = () => {
    setCreating(false);
    setStartId(null);
  };

  const onConnect = (p) => {
    let { source, target } = p;
    if (p.sourceHandle) {
      const m = p.sourceHandle.match(handleRx);
      if (m) source = m[1];
    }
    if (p.targetHandle) {
      const m = p.targetHandle.match(handleRx);
      if (m) target = m[1];
    }
    if (!source || !target || source === target) return;

    const id = `${source}-${target}`;
    setEdges((prev) =>
      prev.some((e) => e.id === id)
        ? prev
        : [
            ...prev,
            {
              id,
              source,
              target,
              type: 'custom',
              animated: true,
              style: { stroke: theme.palette.primary.main, strokeWidth: 2 },
              markerEnd: { type: MarkerType.ArrowClosed, color: theme.palette.primary.main },
              data: { curvature: 0.2, color: theme.palette.primary.main },
            },
          ]
    );
    cancelEdge();
  };

  /* ------------------------------------------------------------------ */
  /*  node change handler                                               */
  /* ------------------------------------------------------------------ */

  // Debounce function to prevent ResizeObserver errors
  const debounce = (func, delay) => {
    let timeoutId;
    return (...args) => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  // Use a ref to store the debounced function
  const debouncedEnsureContainers = useRef(
    debounce((nodes) => ensureContainers(nodes), 50)
  ).current;

  const handleNodesChange = useCallback(
    (changes) => {
      setNodes((nds) => {
        try {
          // First apply the changes
          const updatedNodes = applyNodeChanges(changes, nds);
          // Then ensure containers with debouncing to prevent ResizeObserver errors
          return debouncedEnsureContainers(updatedNodes) || updatedNodes;
        } catch (error) {
          console.error('Error applying node changes:', error);
          return nds; // Return unchanged nodes on error
        }
      });
    },
    [setNodes, debouncedEnsureContainers]
  );

  /* ------------------------------------------------------------------ */
  /*  render                                                            */
  /* ------------------------------------------------------------------ */
  if (loading)
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 6 }}>
        <CircularProgress />
      </Box>
    );

  return (
    <Paper sx={{ height: 600, position: 'relative' }}>
      {creating && (
        <Box sx={{ position: 'absolute', top: 10, left: 10, zIndex: 10, background: theme.palette.info.light, color: theme.palette.info.contrastText, p: 1, borderRadius: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2">Select a task to create dependency</Typography>
          <Button size="small" variant="contained" onClick={cancelEdge}>Cancel</Button>
        </Box>
      )}

      <Box sx={{ height: '100%' }}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={handleNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          onInit={() => rf.fitView()}
          minZoom={0.1}
          maxZoom={1.5}
          defaultZoom={0.8}
          connectionLineStyle={{ stroke: theme.palette.primary.main, strokeWidth: 2 }}
          connectionLineType="smoothstep"
          onNodeDragStop={() => {
            setNodes((p) => {
              try {
                // Use the debounced function to prevent ResizeObserver errors
                return debouncedEnsureContainers(p) || p;
              } catch (error) {
                console.error('Error in onNodeDragStop:', error);
                return p;
              }
            });
          }}
        >
          <Background variant="dots" gap={12} size={1} color="#f0f0f0" />
          <Controls />
          <MiniMap
            nodeStrokeWidth={3}
            nodeColor={(n) =>
              n.type === 'containerNode' ? '#e1f5fe' : n.type === 'subtaskNode' ? '#e8f5e9' : '#fff8e1'
            }
            maskColor="rgba(240,240,240,.5)"
            zoomable
            pannable
          />
          <Panel position="top-left">
            <Tooltip title={t('tasks.dependencyGraph.organize')}>
              <IconButton size="small" onClick={() => setDir((d) => (d === 'LR' ? 'TB' : 'LR'))}>
                <GridViewIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Zoom in">
              <IconButton size="small" onClick={() => rf.zoomIn()}><ZoomInIcon /></IconButton>
            </Tooltip>
            <Tooltip title="Zoom out">
              <IconButton size="small" onClick={() => rf.zoomOut()}><ZoomOutIcon /></IconButton>
            </Tooltip>
            <Tooltip title="Fit view">
              <IconButton size="small" onClick={() => rf.fitView()}><FitScreenIcon /></IconButton>
            </Tooltip>
          </Panel>
        </ReactFlow>
      </Box>
    </Paper>
  );
};

/* provider wrapper -------------------------------------------------- */
export default function DependencyGraphWithProvider(props) {
  return (
    <ReactFlowProvider>
      <DependencyGraph {...props} />
    </ReactFlowProvider>
  );
}