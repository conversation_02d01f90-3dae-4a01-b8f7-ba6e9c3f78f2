import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Typography,
  Chip,
  CircularProgress
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AddIcon from '@mui/icons-material/Add';
import { fetchEvents } from '../services/eventService';
import { AuthContext } from '../contexts/AuthContext';

const EventSelector = ({ selectedEventId, onEventChange }) => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);

  // Immediately check if we have events in the dropdown when component mounts
  useEffect(() => {
    console.log('EventSelector mounted, checking for events');
    if (user) {
      console.log('User is logged in:', user.name);
      if (events.length === 0) {
        console.log('No events loaded yet, will trigger load');
      } else {
        console.log('Events already loaded:', events.length, 'events');
      }
    }
  }, []);

  // Monitor selected event ID changes
  useEffect(() => {
    if (selectedEventId) {
      console.log('Selected event ID changed to:', selectedEventId);
    } else {
      console.log('No event currently selected');
    }
  }, [selectedEventId]);

  // Main effect for loading events
  useEffect(() => {
    const loadEvents = async () => {
      setLoading(true);
      try {
        // Fetch events from the API using the eventService
        console.log('EventSelector: Fetching events for user:', user?.name || 'Unknown user');
        const eventsData = await fetchEvents();
        console.log('EventSelector: Fetched events:', eventsData.length, 'events');

        // Update the events list
        setEvents(eventsData);

        // Get the saved event ID from localStorage
        const savedEventId = localStorage.getItem('selectedEventId');

        // If we have a saved event ID and it exists in the fetched events, use it
        if (savedEventId && eventsData.some(event => event._id === savedEventId)) {
          console.log('Using saved event ID from localStorage:', savedEventId);
          onEventChange(savedEventId);
        }
        // If we have events but no selection, select the first one
        else if (eventsData.length > 0 && !selectedEventId) {
          console.log('Auto-selecting first event:', eventsData[0].title);
          onEventChange(eventsData[0]._id);
        }
        // If the currently selected event is not in the fetched events, select the first available one
        else if (selectedEventId && !eventsData.some(event => event._id === selectedEventId)) {
          console.log('Selected event not found in user\'s events, selecting first available event');
          if (eventsData.length > 0) {
            console.log('Selecting first event:', eventsData[0].title);
            onEventChange(eventsData[0]._id);
          } else {
            console.log('No events available, clearing selection');
            onEventChange(''); // Reset the event selection
          }
        }
      } catch (err) {
        console.error('Error fetching events:', err);
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    // Only load events if we have a user
    if (user) {
      loadEvents();
    } else {
      console.log('No user, clearing events');
      setEvents([]);
      // Also clear the selected event when there's no user
      if (selectedEventId) {
        console.log('Clearing selected event because user is logged out');
        localStorage.removeItem('selectedEventId'); // Clear from localStorage too
        onEventChange('');
      }
      setLoading(false);
    }
  }, [user, selectedEventId, onEventChange]); // Re-fetch events when user or selected event changes

  const handleEventChange = (e) => {
    const eventId = e.target.value;
    onEventChange(eventId);
  };

  const handleCreateEvent = () => {
    navigate('/events');
  };

  // If no user is logged in, show disabled state
  if (!user) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <FormControl sx={{ minWidth: 250 }} disabled>
          <InputLabel>Select Event</InputLabel>
          <Select
            value=""
            label="Select Event"
          >
            <MenuItem value="">
              <Typography variant="body2" color="text.secondary">
                Please log in first
              </Typography>
            </MenuItem>
          </Select>
        </FormControl>

        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          size="small"
          disabled
        >
          Create Event
        </Button>
      </Box>
    );
  }

  if (loading) {
    return <CircularProgress size={24} />;
  }

  if (events.length === 0) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Typography variant="body1" color="text.secondary">
          No events found.
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          size="small"
          onClick={handleCreateEvent}
        >
          Create Event
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
      <FormControl sx={{ minWidth: 250 }}>
        <InputLabel>Select Event</InputLabel>
        <Select
          value={selectedEventId || ''}
          onChange={handleEventChange}
          label="Select Event"
          renderValue={(selected) => {
            const event = events.find(e => e._id === selected);
            if (!event) return "Select Event";

            const eventDate = new Date(event.date);
            return (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {event.title}
                <Chip
                  label={eventDate.toLocaleDateString()}
                  size="small"
                  color="primary"
                />
              </Box>
            );
          }}
        >
          {events.map((event) => (
            <MenuItem key={event._id} value={event._id}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                <Typography>{event.title}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {new Date(event.date).toLocaleDateString()}
                </Typography>
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        size="small"
        onClick={handleCreateEvent}
      >
        Create Event
      </Button>
    </Box>
  );
};

export default EventSelector;