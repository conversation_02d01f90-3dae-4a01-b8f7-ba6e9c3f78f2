import React, { useContext, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { EventContext } from '../contexts/EventContext';
import Calendar from '../pages/Calendar';

export default function CalendarWrapper() {
  const { eventId } = useParams();
  const { setSelectedEventId } = useContext(EventContext);
  const navigate = useNavigate();

  useEffect(() => {
    if (eventId) {
      setSelectedEventId(eventId);
    } else {
      navigate('/events');
    }
  }, [eventId, setSelectedEventId, navigate]);

  return <Calendar />;
}
