import { format, formatDistance } from 'date-fns';
import { enUS, zhTW } from 'date-fns/locale';
import i18n from '../i18n';

/**
 * Get the appropriate date-fns locale based on the current i18n language
 * @returns {Object} The date-fns locale object
 */
export const getDateLocale = () => {
  const language = i18n.language;
  
  switch (language) {
    case 'zh-TW':
      return zhTW;
    case 'en':
    default:
      return enUS;
  }
};

/**
 * Format a date using date-fns with the current locale
 * @param {Date|string|number} date - The date to format
 * @param {string} formatStr - The format string
 * @returns {string} The formatted date string
 */
export const formatDate = (date, formatStr = 'PPP') => {
  if (!date) return '';
  
  try {
    return format(new Date(date), formatStr, {
      locale: getDateLocale()
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Format the distance between two dates in words
 * @param {Date|string|number} date - The date to compare to now or baseDate
 * @param {Date|string|number} baseDate - The base date (defaults to now)
 * @returns {string} The formatted distance string
 */
export const formatDistanceToNow = (date, baseDate = new Date()) => {
  if (!date) return '';
  
  try {
    return formatDistance(new Date(date), new Date(baseDate), {
      locale: getDateLocale(),
      addSuffix: true
    });
  } catch (error) {
    console.error('Error formatting date distance:', error);
    return '';
  }
};
