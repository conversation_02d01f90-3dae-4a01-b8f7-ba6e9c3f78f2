import React, { useState, useEffect, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Divider,
  Alert,
  Snackbar,
  CircularProgress,
  InputAdornment,
  Tabs,
  Tab,
  Autocomplete,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AttachMoney as AttachMoneyIcon,
  Close as CloseIcon,
  FilterList as FilterListIcon,
  Sort as SortIcon,
  Search as SearchIcon,
  PieChart as PieChartIcon,
  <PERSON><PERSON><PERSON> as BarChartIcon,
  Table<PERSON>hart as TableChartIcon
} from '@mui/icons-material';
import { EventContext } from '../contexts/EventContext';
import { getEventBudget, addBudgetItem, updateBudgetItem, deleteBudgetItem, calculateBudgetSummary } from '../services/budgetService';
import { fetchTasks } from '../services/taskService';
import { fetchEventById } from '../services/eventService';
import config from '../config';

// Budget item categories
const BUDGET_CATEGORIES = [
  'Venue',
  'Catering',
  'Decoration',
  'Entertainment',
  'Photography',
  'Transportation',
  'Accommodation',
  'Marketing',
  'Gifts',
  'Printing',
  'Staff',
  'Equipment',
  'Other'
];

// Budget summary component
const BudgetSummary = ({ budgetData }) => {
  const { t } = useTranslation();
  const summary = calculateBudgetSummary(budgetData);

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('budget.summary', 'Budget Summary')}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 1 }}>
              <Typography variant="subtitle2" color="text.secondary">
                {t('budget.totalEstimated', 'Total Estimated')}
              </Typography>
              <Typography variant="h5" color="primary">
                ${summary.totalEstimated.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 1 }}>
              <Typography variant="subtitle2" color="text.secondary">
                {t('budget.totalActual', 'Total Actual')}
              </Typography>
              <Typography variant="h5" color={summary.totalActual > summary.totalEstimated ? 'error.main' : 'success.main'}>
                ${summary.totalActual.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 1 }}>
              <Typography variant="subtitle2" color="text.secondary">
                {t('budget.totalPaid', 'Total Paid')}
              </Typography>
              <Typography variant="h5" color="success.main">
                ${summary.totalPaid.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 1 }}>
              <Typography variant="subtitle2" color="text.secondary">
                {t('budget.totalUnpaid', 'Total Unpaid')}
              </Typography>
              <Typography variant="h5" color={summary.totalUnpaid > 0 ? 'warning.main' : 'text.primary'}>
                ${summary.totalUnpaid.toLocaleString()}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

// Budget item form component
const BudgetItemForm = ({ budgetItem, onSubmit, onCancel, taskId, tasks }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    description: '',
    estimatedAmount: 0,
    actualAmount: 0,
    currency: 'USD',
    isPaid: false,
    category: 'Other',
    notes: '',
    vendor: '',
    paidDate: null,
    taskId: taskId || ''
  });

  useEffect(() => {
    if (budgetItem) {
      setFormData({
        ...budgetItem,
        taskId: taskId || budgetItem.taskId || ''
      });
    } else {
      setFormData({
        description: '',
        estimatedAmount: 0,
        actualAmount: 0,
        currency: 'USD',
        isPaid: false,
        category: 'Other',
        notes: '',
        vendor: '',
        paidDate: null,
        taskId: taskId || ''
      });
    }
  }, [budgetItem, taskId]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleTaskChange = (event, newValue) => {
    setFormData({
      ...formData,
      taskId: newValue ? newValue._id : ''
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        {!taskId && (
          <Grid item xs={12}>
            <Autocomplete
              options={tasks || []}
              getOptionLabel={(option) => option.name}
              value={tasks?.find(task => task._id === formData.taskId) || null}
              onChange={handleTaskChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('budget.task', 'Task')}
                  fullWidth
                  required
                />
              )}
            />
          </Grid>
        )}

        <Grid item xs={12}>
          <TextField
            fullWidth
            label={t('budget.description', 'Description')}
            name="description"
            value={formData.description}
            onChange={handleChange}
            required
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label={t('budget.estimatedAmount', 'Estimated Amount')}
            name="estimatedAmount"
            type="number"
            value={formData.estimatedAmount}
            onChange={handleChange}
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label={t('budget.actualAmount', 'Actual Amount')}
            name="actualAmount"
            type="number"
            value={formData.actualAmount}
            onChange={handleChange}
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel>{t('budget.category', 'Category')}</InputLabel>
            <Select
              name="category"
              value={formData.category}
              onChange={handleChange}
              label={t('budget.category', 'Category')}
            >
              {BUDGET_CATEGORIES.map(category => (
                <MenuItem key={category} value={category}>
                  {t(`budget.categories.${category.toLowerCase()}`, category)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel>{t('budget.paymentStatus', 'Payment Status')}</InputLabel>
            <Select
              name="isPaid"
              value={formData.isPaid}
              onChange={handleChange}
              label={t('budget.paymentStatus', 'Payment Status')}
            >
              <MenuItem value={false}>{t('budget.notPaid', 'Not Paid')}</MenuItem>
              <MenuItem value={true}>{t('budget.paid', 'Paid')}</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label={t('budget.vendor', 'Vendor')}
            name="vendor"
            value={formData.vendor}
            onChange={handleChange}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel>{t('budget.currency', 'Currency')}</InputLabel>
            <Select
              name="currency"
              value={formData.currency}
              onChange={handleChange}
              label={t('budget.currency', 'Currency')}
            >
              {config.CURRENCIES.map(currency => (
                <MenuItem key={currency} value={currency}>{currency}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label={t('budget.notes', 'Notes')}
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            multiline
            rows={3}
          />
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button onClick={onCancel} sx={{ mr: 1 }}>
          {t('common.cancel', 'Cancel')}
        </Button>
        <Button type="submit" variant="contained" color="primary">
          {budgetItem ? t('common.update', 'Update') : t('common.add', 'Add')}
        </Button>
      </Box>
    </form>
  );
};

// Main budget management component
const BudgetManagement = () => {
  const { t } = useTranslation();
  const { eventId } = useParams();
  const navigate = useNavigate();
  const { selectedEventId, setSelectedEventId } = useContext(EventContext);

  const [loading, setLoading] = useState(true);
  const [event, setEvent] = useState(null);
  const [budgetData, setBudgetData] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedBudgetItem, setSelectedBudgetItem] = useState(null);
  const [selectedTaskId, setSelectedTaskId] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [hideTasksWithNoCost, setHideTasksWithNoCost] = useState(false);

  // Use the eventId from params or context
  const effectiveEventId = eventId || selectedEventId;

  // Load event and budget data
  useEffect(() => {
    if (!effectiveEventId) {
      navigate('/events');
      return;
    }

    setSelectedEventId(effectiveEventId);

    const loadData = async () => {
      setLoading(true);
      try {
        // Load event details
        const eventData = await fetchEventById(effectiveEventId);
        setEvent(eventData);

        // Load tasks
        const tasksData = await fetchTasks(effectiveEventId);
        setTasks(tasksData);

        // Load budget data
        const budgetData = await getEventBudget(effectiveEventId);
        setBudgetData(budgetData);
      } catch (error) {
        console.error('Error loading budget data:', error);
        setSnackbar({
          open: true,
          message: t('budget.errors.loadFailed', 'Failed to load budget data'),
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [effectiveEventId, navigate, setSelectedEventId, t]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle dialog open/close
  const handleOpenDialog = (budgetItem = null, taskId = null) => {
    setSelectedBudgetItem(budgetItem);
    setSelectedTaskId(taskId);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedBudgetItem(null);
    setSelectedTaskId(null);
  };

  // Handle budget item operations
  const handleAddBudgetItem = async (formData) => {
    try {
      const taskId = formData.taskId;
      // Remove taskId from the data to be sent to the API
      const { taskId: _, ...budgetItemData } = formData;

      await addBudgetItem(taskId, budgetItemData);

      // Refresh budget data
      const updatedBudgetData = await getEventBudget(effectiveEventId);
      setBudgetData(updatedBudgetData);

      setSnackbar({
        open: true,
        message: t('budget.success.added', 'Budget item added successfully'),
        severity: 'success'
      });

      handleCloseDialog();
    } catch (error) {
      console.error('Error adding budget item:', error);
      setSnackbar({
        open: true,
        message: t('budget.errors.addFailed', 'Failed to add budget item'),
        severity: 'error'
      });
    }
  };

  const handleUpdateBudgetItem = async (formData) => {
    try {
      const taskId = selectedTaskId || formData.taskId;
      const budgetItemId = selectedBudgetItem._id;

      // Remove taskId and _id from the data to be sent to the API
      const { taskId: _, _id: __, ...budgetItemData } = formData;

      await updateBudgetItem(taskId, budgetItemId, budgetItemData);

      // Refresh budget data
      const updatedBudgetData = await getEventBudget(effectiveEventId);
      setBudgetData(updatedBudgetData);

      setSnackbar({
        open: true,
        message: t('budget.success.updated', 'Budget item updated successfully'),
        severity: 'success'
      });

      handleCloseDialog();
    } catch (error) {
      console.error('Error updating budget item:', error);
      setSnackbar({
        open: true,
        message: t('budget.errors.updateFailed', 'Failed to update budget item'),
        severity: 'error'
      });
    }
  };

  const handleDeleteBudgetItem = async (taskId, budgetItemId) => {
    try {
      await deleteBudgetItem(taskId, budgetItemId);

      // Refresh budget data
      const updatedBudgetData = await getEventBudget(effectiveEventId);
      setBudgetData(updatedBudgetData);

      setSnackbar({
        open: true,
        message: t('budget.success.deleted', 'Budget item deleted successfully'),
        severity: 'success'
      });
    } catch (error) {
      console.error('Error deleting budget item:', error);
      setSnackbar({
        open: true,
        message: t('budget.errors.deleteFailed', 'Failed to delete budget item'),
        severity: 'error'
      });
    }
  };

  // Handle form submission
  const handleSubmit = (formData) => {
    if (selectedBudgetItem) {
      handleUpdateBudgetItem(formData);
    } else {
      handleAddBudgetItem(formData);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Filter budget data based on search term, category, and cost items
  const filteredBudgetData = budgetData.filter(task => {
    const matchesSearch = searchTerm === '' ||
      task.taskName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.budgetItems.some(item =>
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.vendor?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.notes?.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesCategory = filterCategory === '' ||
      task.budgetItems.some(item => item.category === filterCategory);

    const hasCostItems = !hideTasksWithNoCost || (task.budgetItems && task.budgetItems.length > 0);

    return matchesSearch && matchesCategory && hasCostItems;
  });

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          {t('budget.title', 'Budget Management')}
        </Typography>
        {event && (
          <Typography variant="subtitle1" color="text.secondary">
            {t('budget.forEvent', 'For event: {{eventTitle}}', { eventTitle: event.title })}
          </Typography>
        )}
      </Box>

      {/* Budget Summary */}
      <BudgetSummary budgetData={budgetData} />

      {/* Filters and Actions */}
      <Box sx={{ mb: 3, display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
        <TextField
          placeholder={t('budget.searchPlaceholder', 'Search budget items...')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          size="small"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ flexGrow: 1, maxWidth: 300 }}
        />

        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>{t('budget.filterByCategory', 'Filter by Category')}</InputLabel>
          <Select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            label={t('budget.filterByCategory', 'Filter by Category')}
          >
            <MenuItem value="">{t('common.all', 'All')}</MenuItem>
            {BUDGET_CATEGORIES.map(category => (
              <MenuItem key={category} value={category}>
                {t(`budget.categories.${category.toLowerCase()}`, category)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControlLabel
          control={
            <Switch
              checked={hideTasksWithNoCost}
              onChange={(e) => setHideTasksWithNoCost(e.target.checked)}
              size="small"
            />
          }
          label={t('budget.hideTasksWithNoCost', 'Hide tasks with no cost items')}
          sx={{ ml: 2 }}
        />

        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {t('budget.addBudgetItem', 'Add Budget Item')}
        </Button>
      </Box>

      {/* Tabs */}
      <Box sx={{ mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="budget view tabs">
          <Tab
            icon={<TableChartIcon />}
            label={t('budget.views.byTask', 'By Task')}
            id="budget-tab-0"
            aria-controls="budget-tabpanel-0"
          />
          <Tab
            icon={<BarChartIcon />}
            label={t('budget.views.byCategory', 'By Category')}
            id="budget-tab-1"
            aria-controls="budget-tabpanel-1"
          />
        </Tabs>
      </Box>

      {/* Tab Panels */}
      <Box role="tabpanel" hidden={tabValue !== 0} id="budget-tabpanel-0" aria-labelledby="budget-tab-0">
        {tabValue === 0 && (
          <>
            {filteredBudgetData.length === 0 ? (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  {t('budget.noBudgetItems', 'No budget items found')}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('budget.addBudgetItemsPrompt', 'Add budget items to track your expenses')}
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={() => handleOpenDialog()}
                  sx={{ mt: 2 }}
                >
                  {t('budget.addBudgetItem', 'Add Budget Item')}
                </Button>
              </Paper>
            ) : (
              <>
                {filteredBudgetData.map(task => (
                  <Paper key={task.taskId} sx={{ mb: 3, overflow: 'hidden' }}>
                    <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'primary.contrastText' }}>
                      <Typography variant="h6">
                        {task.taskName} ({task.taskType})
                      </Typography>
                    </Box>

                    <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {t('budget.estimatedTotal', 'Estimated Total')}:
                          <Typography component="span" fontWeight="bold" sx={{ ml: 1 }}>
                            ${task.budgetItems.reduce((sum, item) => sum + (item.estimatedAmount || 0), 0).toLocaleString()}
                          </Typography>
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {t('budget.actualTotal', 'Actual Total')}:
                          <Typography
                            component="span"
                            fontWeight="bold"
                            sx={{
                              ml: 1,
                              color: task.budgetItems.reduce((sum, item) => sum + (item.actualAmount || 0), 0) >
                                    task.budgetItems.reduce((sum, item) => sum + (item.estimatedAmount || 0), 0) ?
                                    'error.main' : 'success.main'
                            }}
                          >
                            ${task.budgetItems.reduce((sum, item) => sum + (item.actualAmount || 0), 0).toLocaleString()}
                          </Typography>
                        </Typography>
                      </Box>

                      <Button
                        variant="outlined"
                        startIcon={<AddIcon />}
                        onClick={() => handleOpenDialog(null, task.taskId)}
                      >
                        {t('budget.addItem', 'Add Item')}
                      </Button>
                    </Box>

                    <Divider />

                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>{t('budget.description', 'Description')}</TableCell>
                            <TableCell>{t('budget.category', 'Category')}</TableCell>
                            <TableCell align="right">{t('budget.estimated', 'Estimated')}</TableCell>
                            <TableCell align="right">{t('budget.actual', 'Actual')}</TableCell>
                            <TableCell>{t('budget.vendor', 'Vendor')}</TableCell>
                            <TableCell>{t('budget.status', 'Status')}</TableCell>
                            <TableCell align="right">{t('common.actions', 'Actions')}</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {task.budgetItems.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={7} align="center">
                                {t('budget.noItemsForTask', 'No budget items for this task')}
                              </TableCell>
                            </TableRow>
                          ) : (
                            task.budgetItems.map(item => (
                              <TableRow key={item._id}>
                                <TableCell>{item.description}</TableCell>
                                <TableCell>
                                  <Chip
                                    label={t(`budget.categories.${item.category.toLowerCase()}`, item.category)}
                                    size="small"
                                    color="primary"
                                    variant="outlined"
                                  />
                                </TableCell>
                                <TableCell align="right">${item.estimatedAmount?.toLocaleString() || 0}</TableCell>
                                <TableCell align="right">
                                  <Typography
                                    color={item.actualAmount > item.estimatedAmount ? 'error.main' : 'inherit'}
                                  >
                                    ${item.actualAmount?.toLocaleString() || 0}
                                  </Typography>
                                </TableCell>
                                <TableCell>{item.vendor || '-'}</TableCell>
                                <TableCell>
                                  <Chip
                                    label={item.isPaid ? t('budget.paid', 'Paid') : t('budget.notPaid', 'Not Paid')}
                                    size="small"
                                    color={item.isPaid ? 'success' : 'default'}
                                  />
                                </TableCell>
                                <TableCell align="right">
                                  <Tooltip title={t('common.edit', 'Edit')}>
                                    <IconButton
                                      size="small"
                                      onClick={() => handleOpenDialog(item, task.taskId)}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title={t('common.delete', 'Delete')}>
                                    <IconButton
                                      size="small"
                                      color="error"
                                      onClick={() => handleDeleteBudgetItem(task.taskId, item._id)}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Paper>
                ))}
              </>
            )}
          </>
        )}
      </Box>

      <Box role="tabpanel" hidden={tabValue !== 1} id="budget-tabpanel-1" aria-labelledby="budget-tab-1">
        {tabValue === 1 && (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('budget.categoryBreakdown', 'Category Breakdown')}
            </Typography>

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>{t('budget.category', 'Category')}</TableCell>
                    <TableCell align="right">{t('budget.estimated', 'Estimated')}</TableCell>
                    <TableCell align="right">{t('budget.actual', 'Actual')}</TableCell>
                    <TableCell align="right">{t('budget.variance', 'Variance')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {calculateBudgetSummary(budgetData).categoryBreakdown.map(category => (
                    <TableRow key={category.category}>
                      <TableCell>
                        <Chip
                          label={t(`budget.categories.${category.category.toLowerCase()}`, category.category)}
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell align="right">${category.estimated.toLocaleString()}</TableCell>
                      <TableCell align="right">${category.actual.toLocaleString()}</TableCell>
                      <TableCell align="right">
                        <Typography
                          color={category.variance < 0 ? 'error.main' : 'success.main'}
                        >
                          ${Math.abs(category.variance).toLocaleString()}
                          {category.variance >= 0 ? ' under' : ' over'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}
      </Box>

      {/* Budget Item Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {selectedBudgetItem ?
              t('budget.editBudgetItem', 'Edit Budget Item') :
              t('budget.addBudgetItem', 'Add Budget Item')
            }
            <IconButton onClick={handleCloseDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <BudgetItemForm
            budgetItem={selectedBudgetItem}
            onSubmit={handleSubmit}
            onCancel={handleCloseDialog}
            taskId={selectedTaskId}
            tasks={tasks}
          />
        </DialogContent>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BudgetManagement;
