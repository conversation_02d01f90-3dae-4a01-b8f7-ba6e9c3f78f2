import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Paper,
  Stack,
  Divider,
  useTheme
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import EventIcon from '@mui/icons-material/Event';
import TaskIcon from '@mui/icons-material/Task';
import PeopleIcon from '@mui/icons-material/People';
import PlaceIcon from '@mui/icons-material/Place';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const Landing = () => {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Box sx={{
      flexGrow: 1,
      padding: 0,
      marginTop: 0,
      width: '100%',
      minHeight: '100vh',
      bgcolor: 'background.default',
      mt: 0 // Override the margin-top from the main container
    }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #6200ea 0%, #03dac6 100%)',
          color: 'white',
          py: 10,
          textAlign: 'center'
        }}
      >
        <Container maxWidth="lg">
          <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
            {t('landing.hero.title')}
          </Typography>
          <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 4, maxWidth: '800px', mx: 'auto' }}>
            {t('landing.hero.subtitle')}
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
            <Button
              variant="contained"
              size="large"
              component={RouterLink}
              to="/register"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                fontWeight: 'bold',
                px: 4,
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.9)',
                }
              }}
            >
              {t('landing.hero.getStarted')}
            </Button>
            <Button
              variant="outlined"
              size="large"
              component={RouterLink}
              to="/pricing"
              sx={{
                color: 'white',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                }
              }}
            >
              {t('landing.hero.viewPricing')}
            </Button>
          </Stack>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom fontWeight="bold" color="primary">
          {t('landing.features.title')}
        </Typography>
        <Typography variant="h6" textAlign="center" color="text.secondary" paragraph sx={{ mb: 6, maxWidth: '800px', mx: 'auto' }}>
          {t('landing.features.subtitle')}
        </Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} md={6} lg={3}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', transition: 'transform 0.3s', '&:hover': { transform: 'translateY(-8px)' } }}>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
                <EventIcon sx={{ fontSize: 60, color: 'primary.main' }} />
              </Box>
              <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                <Typography gutterBottom variant="h5" component="h3" fontWeight="bold">
                  {t('landing.features.eventManagement.title')}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {t('landing.features.eventManagement.description')}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6} lg={3}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', transition: 'transform 0.3s', '&:hover': { transform: 'translateY(-8px)' } }}>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
                <TaskIcon sx={{ fontSize: 60, color: 'primary.main' }} />
              </Box>
              <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                <Typography gutterBottom variant="h5" component="h3" fontWeight="bold">
                  {t('landing.features.taskTracking.title')}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {t('landing.features.taskTracking.description')}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6} lg={3}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', transition: 'transform 0.3s', '&:hover': { transform: 'translateY(-8px)' } }}>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
                <PeopleIcon sx={{ fontSize: 60, color: 'primary.main' }} />
              </Box>
              <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                <Typography gutterBottom variant="h5" component="h3" fontWeight="bold">
                  {t('landing.features.guestManagement.title')}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {t('landing.features.guestManagement.description')}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6} lg={3}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', transition: 'transform 0.3s', '&:hover': { transform: 'translateY(-8px)' } }}>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
                <PlaceIcon sx={{ fontSize: 60, color: 'primary.main' }} />
              </Box>
              <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                <Typography gutterBottom variant="h5" component="h3" fontWeight="bold">
                  {t('landing.features.venuePlanning.title')}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {t('landing.features.venuePlanning.description')}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>

      {/* Testimonials Section */}
      <Box sx={{ bgcolor: 'grey.100', py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom fontWeight="bold" color="primary">
            {t('landing.testimonials.title')}
          </Typography>
          <Typography variant="h6" textAlign="center" color="text.secondary" paragraph sx={{ mb: 6 }}>
            {t('landing.testimonials.subtitle')}
          </Typography>

          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="body1" paragraph sx={{ fontStyle: 'italic' }}>
                  "{t('landing.testimonials.testimonial1.quote')}"
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: '50%',
                      bgcolor: 'primary.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                      fontSize: '1.2rem',
                      fontWeight: 'bold'
                    }}
                  >
                    JD
                  </Box>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {t('landing.testimonials.testimonial1.author')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('landing.testimonials.testimonial1.author').split(',')[1] || ''}
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="body1" paragraph sx={{ fontStyle: 'italic' }}>
                  "{t('landing.testimonials.testimonial2.quote')}"
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: '50%',
                      bgcolor: 'secondary.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                      fontSize: '1.2rem',
                      fontWeight: 'bold'
                    }}
                  >
                    MS
                  </Box>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {t('landing.testimonials.testimonial2.author')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('landing.testimonials.testimonial2.author').split(',')[1] || ''}
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="body1" paragraph sx={{ fontStyle: 'italic' }}>
                  "{t('landing.testimonials.testimonial3.quote')}"
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: '50%',
                      bgcolor: 'error.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                      fontSize: '1.2rem',
                      fontWeight: 'bold'
                    }}
                  >
                    AJ
                  </Box>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {t('landing.testimonials.testimonial3.author')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('landing.testimonials.testimonial3.author').split(',')[1] || ''}
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #03dac6 0%, #6200ea 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center'
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" component="h2" gutterBottom fontWeight="bold">
            {t('landing.cta.title')}
          </Typography>
          <Typography variant="h6" paragraph sx={{ mb: 4 }}>
            {t('landing.cta.subtitle')}
          </Typography>
          <Button
            variant="contained"
            size="large"
            component={RouterLink}
            to="/register"
            sx={{
              bgcolor: 'white',
              color: 'primary.main',
              fontWeight: 'bold',
              px: 4,
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.9)',
              }
            }}
          >
            {t('landing.cta.button')}
          </Button>
        </Container>
      </Box>

      {/* Features List Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom fontWeight="bold" color="primary">
          {t('landing.featuresList.title')}
        </Typography>
        <Typography variant="h6" textAlign="center" color="text.secondary" paragraph sx={{ mb: 6 }}>
          {t('landing.featuresList.subtitle')}
        </Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 4 }}>
              <Typography variant="h4" component="h3" gutterBottom color="primary" fontWeight="bold">
                {t('landing.featuresList.eventManagement.title')}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.eventManagement.feature1')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.eventManagement.feature2')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.eventManagement.feature3')}
                </Typography>
              </Box>
            </Box>

            <Box>
              <Typography variant="h4" component="h3" gutterBottom color="primary" fontWeight="bold">
                {t('landing.featuresList.taskManagement.title')}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.taskManagement.feature1')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.taskManagement.feature2')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.taskManagement.feature3')}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 4 }}>
              <Typography variant="h4" component="h3" gutterBottom color="primary" fontWeight="bold">
                {t('landing.featuresList.guestManagement.title')}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.guestManagement.feature1')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.guestManagement.feature2')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.guestManagement.feature3')}
                </Typography>
              </Box>
            </Box>

            <Box>
              <Typography variant="h4" component="h3" gutterBottom color="primary" fontWeight="bold">
                {t('landing.featuresList.venuePlanning.title')}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.venuePlanning.feature1')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.venuePlanning.feature2')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 2, mt: 0.5 }} />
                <Typography variant="body1">
                  {t('landing.featuresList.venuePlanning.feature3')}
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>

      {/* Final CTA */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 6, textAlign: 'center' }}>
        <Container maxWidth="md">
          <Typography variant="h4" component="h2" gutterBottom fontWeight="bold">
            {t('landing.finalCta.title')}
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center" sx={{ mt: 3 }}>
            <Button
              variant="contained"
              size="large"
              component={RouterLink}
              to="/register"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                fontWeight: 'bold',
                px: 4,
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.9)',
                }
              }}
            >
              {t('landing.finalCta.signUp')}
            </Button>
            <Button
              variant="outlined"
              size="large"
              component={RouterLink}
              to="/pricing"
              sx={{
                color: 'white',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                }
              }}
            >
              {t('landing.finalCta.viewPricing')}
            </Button>
          </Stack>
        </Container>
      </Box>
    </Box>
  );
};

export default Landing;
