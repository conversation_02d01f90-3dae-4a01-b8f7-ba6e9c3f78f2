import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Container,
  Typography,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Divider,
  useTheme
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import EventIcon from '@mui/icons-material/Event';
import TaskIcon from '@mui/icons-material/Task';
import PeopleIcon from '@mui/icons-material/People';
import PlaceIcon from '@mui/icons-material/Place';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AssignmentIcon from '@mui/icons-material/Assignment';
import GroupWorkIcon from '@mui/icons-material/GroupWork';
import BarChartIcon from '@mui/icons-material/BarChart';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import DevicesIcon from '@mui/icons-material/Devices';
import SecurityIcon from '@mui/icons-material/Security';

const FeatureSection = ({ title, description, icon, features }) => {
  return (
    <Paper elevation={2} sx={{ p: 4, height: '100%', borderRadius: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ color: 'primary.main', mr: 2 }}>
          {icon}
        </Box>
        <Typography variant="h5" component="h2" fontWeight="bold">
          {title}
        </Typography>
      </Box>

      <Typography variant="body1" paragraph color="text.secondary">
        {description}
      </Typography>

      <List>
        {features.map((feature, index) => (
          <ListItem key={index} sx={{ py: 0.5 }}>
            <ListItemIcon sx={{ minWidth: 36 }}>
              <CheckCircleIcon color="success" fontSize="small" />
            </ListItemIcon>
            <ListItemText primary={feature} />
          </ListItem>
        ))}
      </List>
    </Paper>
  );
};

const Features = () => {
  const theme = useTheme();
  const { t } = useTranslation();

  const featureSections = [
    {
      title: t('features.sections.eventManagement.title', 'Event Management'),
      description: t('features.sections.eventManagement.description', 'Create and manage multiple events with powerful organization tools.'),
      icon: <EventIcon fontSize="large" />,
      features: [
        t('features.sections.eventManagement.feature1', 'Create unlimited events with customizable details'),
        t('features.sections.eventManagement.feature2', 'Track event progress with visual indicators'),
        t('features.sections.eventManagement.feature3', 'Set event dates, locations, and budgets'),
        t('features.sections.eventManagement.feature4', 'Organize events by type and category')
      ]
    },
    {
      title: t('features.sections.taskManagement.title', 'Task Management'),
      description: t('features.sections.taskManagement.description', 'Keep track of all your event tasks with advanced task management.'),
      icon: <TaskIcon fontSize="large" />,
      features: [
        t('features.sections.taskManagement.feature1', 'Assign multiple team members to tasks'),
        t('features.sections.taskManagement.feature2', 'Set soft and hard deadlines with reminders'),
        t('features.sections.taskManagement.feature3', 'Track task status and progress'),
        t('features.sections.taskManagement.feature4', 'Organize tasks by type and priority')
      ]
    },
    {
      title: t('features.sections.guestManagement.title', 'Guest Management'),
      description: t('features.sections.guestManagement.description', 'Manage your guest list with comprehensive guest tracking features.'),
      icon: <PeopleIcon fontSize="large" />,
      features: [
        t('features.sections.guestManagement.feature1', 'Track RSVPs and meal preferences'),
        t('features.sections.guestManagement.feature2', 'Organize guests by groups or categories'),
        t('features.sections.guestManagement.feature3', 'Assign guests to tables and seats'),
        t('features.sections.guestManagement.feature4', 'Send automated guest communications')
      ]
    },
    {
      title: t('features.sections.venuePlanning.title', 'Venue Planning'),
      description: t('features.sections.venuePlanning.description', 'Design and visualize your venue with our interactive floor plan editor.'),
      icon: <PlaceIcon fontSize="large" />,
      features: [
        t('features.sections.venuePlanning.feature1', 'Drag-and-drop floor plan editor'),
        t('features.sections.venuePlanning.feature2', 'Create custom table layouts and seating arrangements'),
        t('features.sections.venuePlanning.feature3', 'Upload venue floor plans as background images'),
        t('features.sections.venuePlanning.feature4', 'Assign guests directly to tables and seats')
      ]
    },
    {
      title: t('features.sections.calendar.title', 'Calendar & Scheduling'),
      description: t('features.sections.calendar.description', 'Visualize your event timeline with our intuitive calendar views.'),
      icon: <CalendarMonthIcon fontSize="large" />,
      features: [
        t('features.sections.calendar.feature1', 'Monthly overview of all event tasks'),
        t('features.sections.calendar.feature2', 'Daily view with detailed task information'),
        t('features.sections.calendar.feature3', 'Assignee-based task organization'),
        t('features.sections.calendar.feature4', 'Automatic deadline warnings and notifications')
      ]
    },
    {
      title: t('features.sections.teamCollaboration.title', 'Team Collaboration'),
      description: t('features.sections.teamCollaboration.description', 'Work together seamlessly with your event planning team.'),
      icon: <GroupWorkIcon fontSize="large" />,
      features: [
        t('features.sections.teamCollaboration.feature1', 'Invite team members to collaborate on events'),
        t('features.sections.teamCollaboration.feature2', 'Assign tasks to multiple team members'),
        t('features.sections.teamCollaboration.feature3', 'Track individual contributions and progress'),
        t('features.sections.teamCollaboration.feature4', 'Real-time updates and notifications')
      ]
    },
    {
      title: t('features.sections.templates.title', 'Templates & Checklists'),
      description: t('features.sections.templates.description', 'Save time with pre-built templates and customizable checklists.'),
      icon: <AssignmentIcon fontSize="large" />,
      features: [
        t('features.sections.templates.feature1', 'Event templates for different event types'),
        t('features.sections.templates.feature2', 'Customizable task checklists'),
        t('features.sections.templates.feature3', 'Save your own templates for future use'),
        t('features.sections.templates.feature4', 'Import and export templates')
      ]
    },
    {
      title: t('features.sections.analytics.title', 'Analytics & Reporting'),
      description: t('features.sections.analytics.description', 'Gain insights into your event planning with detailed analytics.'),
      icon: <BarChartIcon fontSize="large" />,
      features: [
        t('features.sections.analytics.feature1', 'Track budget allocation and spending'),
        t('features.sections.analytics.feature2', 'Monitor task completion rates'),
        t('features.sections.analytics.feature3', 'Analyze team performance'),
        t('features.sections.analytics.feature4', 'Generate comprehensive event reports')
      ]
    },
    {
      title: t('features.sections.notifications.title', 'Notifications & Reminders'),
      description: t('features.sections.notifications.description', 'Stay on top of your event planning with timely notifications.'),
      icon: <NotificationsActiveIcon fontSize="large" />,
      features: [
        t('features.sections.notifications.feature1', 'Deadline reminders for tasks'),
        t('features.sections.notifications.feature2', 'Important milestone notifications'),
        t('features.sections.notifications.feature3', 'Customizable notification preferences'),
        t('features.sections.notifications.feature4', 'Email and in-app notifications')
      ]
    },
    {
      title: t('features.sections.crossPlatform.title', 'Cross-Platform Access'),
      description: t('features.sections.crossPlatform.description', 'Access your event planning from anywhere, on any device.'),
      icon: <DevicesIcon fontSize="large" />,
      features: [
        t('features.sections.crossPlatform.feature1', 'Responsive web application'),
        t('features.sections.crossPlatform.feature2', 'Mobile-friendly interface'),
        t('features.sections.crossPlatform.feature3', 'Consistent experience across devices'),
        t('features.sections.crossPlatform.feature4', 'Offline access to essential information')
      ]
    },
    {
      title: t('features.sections.security.title', 'Security & Privacy'),
      description: t('features.sections.security.description', 'Keep your event data secure with our robust security features.'),
      icon: <SecurityIcon fontSize="large" />,
      features: [
        t('features.sections.security.feature1', 'Secure user authentication'),
        t('features.sections.security.feature2', 'Data encryption for sensitive information'),
        t('features.sections.security.feature3', 'Granular permission controls'),
        t('features.sections.security.feature4', 'Regular security updates and monitoring')
      ]
    }
  ];

  return (
    <Box sx={{ flexGrow: 1, padding: 0, marginTop: 0, width: '100%', mt: 0 }}>
      {/* Header */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #6200ea 0%, #03dac6 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center'
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
            {t('features.header.title', 'Powerful Features for Successful Events')}
          </Typography>
          <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 4, maxWidth: '800px', mx: 'auto' }}>
            {t('features.header.subtitle', 'Discover all the tools you need to plan and execute flawless events')}
          </Typography>
        </Container>
      </Box>

      {/* Features Grid */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Grid container spacing={4}>
          {featureSections.map((section, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <FeatureSection
                title={section.title}
                description={section.description}
                icon={section.icon}
                features={section.features}
              />
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* CTA Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #03dac6 0%, #6200ea 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center'
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" component="h2" gutterBottom fontWeight="bold">
            {t('features.cta.title', 'Ready to Start Planning Your Events?')}
          </Typography>
          <Typography variant="h6" paragraph sx={{ mb: 4 }}>
            {t('features.cta.subtitle', 'Join thousands of event planners who trust our platform to deliver successful events.')}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              component={RouterLink}
              to="/register"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                fontWeight: 'bold',
                px: 4,
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.9)',
                }
              }}
            >
              {t('features.cta.signUp', 'Sign Up Free')}
            </Button>
            <Button
              variant="outlined"
              size="large"
              component={RouterLink}
              to="/pricing"
              sx={{
                color: 'white',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                }
              }}
            >
              {t('features.cta.viewPricing', 'View Pricing')}
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default Features;
