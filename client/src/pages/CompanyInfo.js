import React from 'react';
import { Container, Typography, Box, Paper, Divider, Grid } from '@mui/material';
import EmailIcon from '@mui/icons-material/Email';
import BusinessIcon from '@mui/icons-material/Business';
import SecurityIcon from '@mui/icons-material/Security';
import GavelIcon from '@mui/icons-material/Gavel';
import MoneyOffIcon from '@mui/icons-material/MoneyOff';
import { useTranslation } from 'react-i18next';

const CompanyInfo = () => {
  const { t } = useTranslation();
  const companyName = t('company.name');

  return (
    <Container maxWidth="lg" sx={{ py: 8 }}>
      <Typography variant="h3" component="h1" gutterBottom align="center" fontWeight="bold" color="primary">
        {t('company.about', { companyName })}
      </Typography>
      
      {/* Company Overview */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <BusinessIcon fontSize="large" color="primary" sx={{ mr: 2 }} />
          <Typography variant="h4" component="h2">
            {t('company.ourCompany')}
          </Typography>
        </Box>
        <Typography variant="body1" paragraph>
          {t('company.description', { companyName })}
        </Typography>
      </Paper>
      
      {/* Services */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h4" component="h2" gutterBottom color="primary">
          {t('company.services.title')}
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>{t('company.services.eventManagement.title')}</Typography>
              <Typography variant="body1">
                {t('company.services.eventManagement.description')}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>{t('company.services.taskTracking.title')}</Typography>
              <Typography variant="body1">
                {t('company.services.taskTracking.description')}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>{t('company.services.guestManagement.title')}</Typography>
              <Typography variant="body1">
                {t('company.services.guestManagement.description')}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>{t('company.services.venuePlanning.title')}</Typography>
              <Typography variant="body1">
                {t('company.services.venuePlanning.description')}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
      
      {/* Contact Information */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <EmailIcon fontSize="large" color="primary" sx={{ mr: 2 }} />
          <Typography variant="h4" component="h2">
            {t('company.contactUs.title')}
          </Typography>
        </Box>
        <Typography variant="body1" paragraph>
          {t('company.contactUs.description')}
        </Typography>
        <Box sx={{ pl: 2 }}>
          <Typography variant="body1" paragraph>
            {t('company.contactUs.email')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('company.contactUs.businessHours')}
          </Typography>
        </Box>
      </Paper>
      
      {/* Refund and Cancellation Policy */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <MoneyOffIcon fontSize="large" color="primary" sx={{ mr: 2 }} />
          <Typography variant="h4" component="h2">
            {t('company.refundPolicy.title')}
          </Typography>
        </Box>
        <Typography variant="body1" paragraph>
          <strong>{t('company.refundPolicy.subscriptions')}</strong> {t('company.refundPolicy.subscriptionsDetails')}
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>{t('company.refundPolicy.refunds')}</strong> {t('company.refundPolicy.refundsDetails')}
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>{t('company.refundPolicy.freeTier')}</strong> {t('company.refundPolicy.freeTierDetails')}
        </Typography>
      </Paper>
      
      {/* Terms and Conditions */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }} id="terms">
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <GavelIcon fontSize="large" color="primary" sx={{ mr: 2 }} />
          <Typography variant="h4" component="h2">
            {t('common.termsOfService')}
          </Typography>
        </Box>
        <Typography variant="body1" paragraph>
          {t('company.termsIntro', { companyName })}
        </Typography>
        <ul>
          <li>
            <Typography variant="body1" paragraph>
              {t('company.terms.autoRenew')}
            </Typography>
          </li>
          <li>
            <Typography variant="body1" paragraph>
              {t('company.termsReserveRight', { companyName })}
            </Typography>
          </li>
          <li>
            <Typography variant="body1" paragraph>
              {t('company.termsUnauthorized', { companyName })}
            </Typography>
          </li>
          <li>
            <Typography variant="body1" paragraph>
              {t('company.termsIntellectualProperty', { companyName })}
            </Typography>
          </li>
        </ul>
      </Paper>
      
      {/* Privacy Policy */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }} id="privacy">
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <SecurityIcon fontSize="large" color="primary" sx={{ mr: 2 }} />
          <Typography variant="h4" component="h2">
            {t('common.privacyPolicy')}
          </Typography>
        </Box>
        <Typography variant="body1" paragraph>
          {t('company.privacy.intro')}
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>{t('company.privacy.dataCollection')}</strong> {t('company.privacy.dataCollectionDetails')}
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>{t('company.privacy.useOfInfo')}</strong> {t('company.privacy.useOfInfoDetails')}
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>{t('company.privacy.dataSharing')}</strong> {t('company.privacy.dataSharingDetails')}
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>{t('company.privacy.security')}</strong> {t('company.privacy.securityDetails')}
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>{t('company.privacy.userRights')}</strong> {t('company.privacy.userRightsDetails')}
        </Typography>
      </Paper>
    </Container>
  );
};

export default CompanyInfo; 