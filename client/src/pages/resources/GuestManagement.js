import { useState, useEffect, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Container,
  Button,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TablePagination,
  InputAdornment
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import NoMealsIcon from '@mui/icons-material/NoMeals';
import { EventContext } from '../../contexts/EventContext';

// Sample data - In a real app, this would come from an API
const sampleGuests = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    rsvpStatus: 'Confirmed',
    attributes: [
      { type: 'Dietary', value: 'Vegetarian' },
      { type: 'Allergies', value: 'Nuts' }
    ]
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '************',
    rsvpStatus: 'Pending',
    attributes: [
      { type: 'Dietary', value: 'Vegan' },
      { type: 'Accessibility', value: 'Wheelchair' }
    ]
  },
  {
    id: 3,
    name: 'Michael Johnson',
    email: '<EMAIL>',
    phone: '************',
    rsvpStatus: 'Declined',
    attributes: [
      { type: 'Dietary', value: 'Gluten-Free' }
    ]
  }
];

// Predefined attribute types
const attributeTypes = [
  'Dietary',
  'Allergies',
  'Accessibility',
  'Seating Preference',
  'Special Needs',
  'Other'
];

// Translated attribute types function
const getTranslatedAttributeType = (t, type) => {
  switch(type) {
    case 'Dietary': return t('guests.attributeTypes.dietary', 'Dietary');
    case 'Allergies': return t('guests.attributeTypes.allergies', 'Allergies');
    case 'Accessibility': return t('guests.attributeTypes.accessibility', 'Accessibility');
    case 'Seating Preference': return t('guests.attributeTypes.seatingPreference', 'Seating Preference');
    case 'Special Needs': return t('guests.attributeTypes.specialNeeds', 'Special Needs');
    case 'Other': return t('guests.attributeTypes.other', 'Other');
    default: return type;
  }
};

// Predefined dietary options
const dietaryOptions = [
  'None',
  'Vegetarian',
  'Vegan',
  'Pescatarian',
  'Gluten-Free',
  'Dairy-Free',
  'Nut-Free',
  'Kosher',
  'Halal'
];

// Translated dietary options function
const getTranslatedDietaryOption = (t, option) => {
  switch(option) {
    case 'None': return t('guests.dietaryOptions.none', 'None');
    case 'Vegetarian': return t('guests.dietaryOptions.vegetarian', 'Vegetarian');
    case 'Vegan': return t('guests.dietaryOptions.vegan', 'Vegan');
    case 'Pescatarian': return t('guests.dietaryOptions.pescatarian', 'Pescatarian');
    case 'Gluten-Free': return t('guests.dietaryOptions.glutenFree', 'Gluten-Free');
    case 'Dairy-Free': return t('guests.dietaryOptions.dairyFree', 'Dairy-Free');
    case 'Nut-Free': return t('guests.dietaryOptions.nutFree', 'Nut-Free');
    case 'Kosher': return t('guests.dietaryOptions.kosher', 'Kosher');
    case 'Halal': return t('guests.dietaryOptions.halal', 'Halal');
    default: return option;
  }
};

const GuestManagement = () => {
  const { t } = useTranslation();
  const { currentEvent } = useContext(EventContext) || { currentEvent: null };
  const [guests, setGuests] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [guestToDelete, setGuestToDelete] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingGuest, setEditingGuest] = useState(null);
  const [newGuest, setNewGuest] = useState({
    name: '',
    email: '',
    phone: '',
    rsvpStatus: 'Pending',
    attributes: []
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [openAttributeDialog, setOpenAttributeDialog] = useState(false);
  const [newAttribute, setNewAttribute] = useState({ type: 'Dietary', value: '' });
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedGuestId, setSelectedGuestId] = useState(null);

  // Fetch guests from API when event changes
  useEffect(() => {
    const fetchGuests = async () => {
      if (!currentEvent) {
        setGuests(sampleGuests); // Use sample data when no event is selected
        return;
      }

      // Get auth token and user info from localStorage
      const token = localStorage.getItem('token');
      const userString = localStorage.getItem('user');
      let user = null;

      try {
        user = userString ? JSON.parse(userString) : null;
      } catch (e) {
        console.error('Error parsing user from localStorage:', e);
      }

      console.log('Fetching guests with user:', user);
      console.log('Current event:', currentEvent);
      console.log('Auth token available:', !!token);

      setIsLoading(true);
      try {
        // Use dev route if no authentication, otherwise use regular route
        const useDevRoute = !token || !user;
        let response;

        if (useDevRoute) {
          console.log('Using development route for guest fetching');
          // For development, we'll create some sample guests if none exist
          setGuests(sampleGuests.map(guest => ({
            ...guest,
            _id: guest.id.toString() // Ensure sample data has _id field
          })));
          setIsLoading(false);
          return;
        } else {
          response = await fetch(`/api/resources/guests?eventId=${currentEvent._id}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
        }

        console.log('Fetch response status:', response.status);

        if (!response.ok) {
          let errorMessage = `Failed to fetch guests: ${response.status}`;
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } catch (e) {
            try {
              const text = await response.text();
              if (text) errorMessage += ` - ${text}`;
            } catch (textError) {
              console.error('Could not parse error response:', textError);
            }
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log('Fetched guests:', data);
        setGuests(data);
      } catch (error) {
        console.error('Error fetching guests:', error);
        // Fallback to sample data if API fails
        setGuests(sampleGuests.map(guest => ({
          ...guest,
          _id: guest.id.toString() // Ensure sample data has _id field
        })));
      } finally {
        setIsLoading(false);
      }
    };

    fetchGuests();
  }, [currentEvent]);

  const handleOpenDialog = (guest = null) => {
    if (guest) {
      setEditingGuest(guest);
      setNewGuest({ ...guest });
    } else {
      setEditingGuest(null);
      setNewGuest({
        name: '',
        email: '',
        phone: '',
        rsvpStatus: 'Pending',
        attributes: []
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingGuest(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewGuest({ ...newGuest, [name]: value });
  };

  const handleSaveGuest = async () => {
    if (!currentEvent) {
      alert(t('guests.errors.selectEventFirst', 'Please select an event first'));
      return;
    }

    // Get auth token and user info from localStorage
    const token = localStorage.getItem('token');
    const userString = localStorage.getItem('user');
    let user = null;

    try {
      user = userString ? JSON.parse(userString) : null;
    } catch (e) {
      console.error('Error parsing user from localStorage:', e);
    }

    if (!token) {
      alert(t('errors.authTokenNotFound', 'Authentication token not found. Please log in again.'));
      return;
    }

    if (!user || !user._id) {
      alert(t('errors.userInfoNotFound', 'User information not found. Please log in again.'));
      return;
    }

    console.log('Current user:', user);

    setIsLoading(true);
    try {
      if (editingGuest) {
        // Update existing guest
        const guestId = editingGuest._id || editingGuest.id;

        // Use the development route for testing
        const useDevRoute = !token || !user; // Use dev route only if no auth
        const endpoint = useDevRoute
          ? `/api/resources/guests/dev-update/${guestId}`
          : `/api/resources/guests/${guestId}`;

        const response = await fetch(endpoint, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': useDevRoute ? '' : `Bearer ${token}`
          },
          body: JSON.stringify({
            ...newGuest,
            event: newGuest.event || editingGuest.event || currentEvent._id
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `Failed to update guest: ${response.status}`);
        }

        const updatedGuest = await response.json();
        setGuests(guests.map(g => (g._id || g.id) === guestId ? updatedGuest : g));
        setSelectedGuestId(updatedGuest._id || updatedGuest.id);
        console.log('Guest updated successfully:', updatedGuest);
      } else {
        // Add new guest
        const guestData = {
          ...newGuest,
          eventId: currentEvent._id,
          // Include these explicitly to ensure they're set
          createdBy: user._id,
          updatedBy: user._id
        };

        console.log('Creating new guest with data:', guestData);
        console.log('Current event:', currentEvent);
        console.log('Auth token available:', !!token);

        // Use the development route for testing
        const useDevRoute = !token || !user; // Use dev route only if no auth
        const endpoint = useDevRoute ? '/api/resources/guests/dev-create' : '/api/resources/guests';

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': useDevRoute ? '' : `Bearer ${token}`
          },
          body: JSON.stringify(guestData),
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);

        if (!response.ok) {
          let errorMessage = `Failed to create guest: ${response.status}`;
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } catch (e) {
            try {
              const text = await response.text();
              if (text) errorMessage += ` - ${text}`;
            } catch (textError) {
              console.error('Could not parse error response:', textError);
            }
          }
          throw new Error(errorMessage);
        }

        const createdGuest = await response.json();
        setGuests([...guests, createdGuest]);
        setSelectedGuestId(createdGuest._id || createdGuest.id);
        console.log('Guest created successfully:', createdGuest);
      }
      handleCloseDialog();
    } catch (error) {
      console.error('Error saving guest:', error);
      alert(t('guests.errors.saveFailed', 'Error saving guest: {{message}}', { message: error.message }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteConfirmation = (id) => {
    setGuestToDelete(id);
    setConfirmDialogOpen(true);
  };

  const handleCancelDelete = () => {
    setConfirmDialogOpen(false);
    setGuestToDelete(null);
  };

  const handleDeleteGuest = async () => {
    if (!guestToDelete) return;

    // Get auth token and user info from localStorage
    const token = localStorage.getItem('token');
    const userString = localStorage.getItem('user');
    let user = null;

    try {
      user = userString ? JSON.parse(userString) : null;
    } catch (e) {
      console.error('Error parsing user from localStorage:', e);
    }

    if (!token) {
      alert(t('errors.authTokenNotFound', 'Authentication token not found. Please log in again.'));
      return;
    }

    if (!user || !user._id) {
      alert(t('errors.userInfoNotFound', 'User information not found. Please log in again.'));
      return;
    }

    console.log('Current user for delete operation:', user);

    setIsLoading(true);
    try {
      // Use the development route for testing
      const useDevRoute = !token || !user; // Use dev route only if no auth
      const endpoint = useDevRoute
        ? `/api/resources/guests/dev-delete/${guestToDelete}`
        : `/api/resources/guests/${guestToDelete}`;

      const response = await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': useDevRoute ? '' : `Bearer ${token}`
        }
      });

      console.log('Delete response status:', response.status);

      if (!response.ok) {
        let errorMessage = `Failed to delete guest: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          try {
            const text = await response.text();
            if (text) errorMessage += ` - ${text}`;
          } catch (textError) {
            console.error('Could not parse error response:', textError);
          }
        }
        throw new Error(errorMessage);
      }

      setGuests(guests.filter(guest => (guest._id || guest.id) !== guestToDelete));
      // Clear selection if the deleted guest was selected
      if (selectedGuestId === guestToDelete) {
        setSelectedGuestId(null);
      }
      console.log('Guest deleted successfully');
    } catch (error) {
      console.error('Error deleting guest:', error);
      alert(t('guests.errors.deleteFailed', 'Error deleting guest: {{message}}', { message: error.message }));
    } finally {
      setIsLoading(false);
      setConfirmDialogOpen(false);
      setGuestToDelete(null);
    }
  };

  const handleOpenAttributeDialog = () => {
    setNewAttribute({ type: 'Dietary', value: '' });
    setOpenAttributeDialog(true);
  };

  const handleCloseAttributeDialog = () => {
    setOpenAttributeDialog(false);
  };

  const handleAttributeChange = (e) => {
    const { name, value } = e.target;
    setNewAttribute({ ...newAttribute, [name]: value });
  };

  const handleAddAttribute = () => {
    if (newAttribute.type && newAttribute.value) {
      setNewGuest({
        ...newGuest,
        attributes: [...newGuest.attributes, { ...newAttribute }]
      });
      handleCloseAttributeDialog();
    }
  };

  const handleRemoveAttribute = (index) => {
    const updatedAttributes = [...newGuest.attributes];
    updatedAttributes.splice(index, 1);
    setNewGuest({ ...newGuest, attributes: updatedAttributes });
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
    // Don't clear selection when changing pages as we want to keep track of the selected guest
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    // Don't clear selection when changing rows per page as we want to keep track of the selected guest
  };

  // Filter guests based on search term and status filter
  const filteredGuests = guests.filter(guest => {
    const matchesSearch = guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guest.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'All' || guest.rsvpStatus === filterStatus;
    return matchesSearch && matchesStatus;
  });

  // Effect to ensure selected guest is visible in the current page
  useEffect(() => {
    if (selectedGuestId) {
      const selectedGuestIndex = filteredGuests.findIndex(
        guest => (guest._id || guest.id) === selectedGuestId
      );

      if (selectedGuestIndex !== -1) {
        const targetPage = Math.floor(selectedGuestIndex / rowsPerPage);
        if (targetPage !== page) {
          setPage(targetPage);
        }
      }
    }
  }, [selectedGuestId, filteredGuests, rowsPerPage, page]);

  // Paginate guests
  const paginatedGuests = filteredGuests.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            {t('guests.title', 'Guest Management')}
          </Typography>
          {currentEvent && (
            <Typography variant="subtitle1" color="text.secondary">
              {t('guests.event', 'Event')}: {currentEvent.title}
            </Typography>
          )}
        </Box>

        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder={t('guests.searchPlaceholder', 'Search guests...')}
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setSelectedGuestId(null);
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth variant="outlined">
              <InputLabel id="status-filter-label">{t('guests.columns.rsvpStatus', 'RSVP Status')}</InputLabel>
              <Select
                labelId="status-filter-label"
                value={filterStatus}
                onChange={(e) => {
                  setFilterStatus(e.target.value);
                  setSelectedGuestId(null);
                }}
                label={t('guests.columns.rsvpStatus', 'RSVP Status')}
              >
                <MenuItem value="All">{t('common.all', 'All')}</MenuItem>
                <MenuItem value="Confirmed">{t('guests.rsvpStatus.confirmed', 'Confirmed')}</MenuItem>
                <MenuItem value="Pending">{t('guests.rsvpStatus.pending', 'Pending')}</MenuItem>
                <MenuItem value="Declined">{t('guests.rsvpStatus.declined', 'Declined')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
              fullWidth
              sx={{ height: '100%' }}
            >
              {t('guests.addGuest', 'Add Guest')}
            </Button>
          </Grid>
        </Grid>

        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('guests.columns.name', 'Name')}</TableCell>
                <TableCell>{t('guests.columns.email', 'Email')}</TableCell>
                <TableCell>{t('guests.columns.phone', 'Phone')}</TableCell>
                <TableCell>{t('guests.columns.rsvpStatus', 'RSVP Status')}</TableCell>
                <TableCell>{t('guests.columns.attributes', 'Attributes')}</TableCell>
                <TableCell align="right">{t('common.actions', 'Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body1" sx={{ py: 2 }}>
                      {t('guests.loadingGuests', 'Loading guests...')}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : paginatedGuests.length > 0 ? (
                paginatedGuests.map((guest) => {
                  const guestId = guest._id || guest.id;
                  const isSelected = guestId === selectedGuestId;
                  return (
                    <TableRow
                      key={guestId}
                      selected={isSelected}
                      sx={{
                        '&.Mui-selected': {
                          backgroundColor: 'rgba(25, 118, 210, 0.08)',
                        },
                        '&.Mui-selected:hover': {
                          backgroundColor: 'rgba(25, 118, 210, 0.12)',
                        },
                      }}
                      onClick={() => setSelectedGuestId(guestId)}
                    >
                      <TableCell>{guest.name}</TableCell>
                      <TableCell>{guest.email}</TableCell>
                      <TableCell>{guest.phone}</TableCell>
                      <TableCell>
                        <Chip
                          label={guest.rsvpStatus}
                          color={
                            guest.rsvpStatus === 'Confirmed' ? 'success' :
                            guest.rsvpStatus === 'Pending' ? 'warning' : 'error'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {guest.attributes.map((attr, index) => (
                            <Tooltip key={index} title={`${getTranslatedAttributeType(t, attr.type)}: ${attr.value}`}>
                              <Chip
                                icon={
                                  attr.type === 'Dietary' ?
                                    attr.value === 'Vegetarian' || attr.value === 'Vegan' ?
                                      <RestaurantIcon fontSize="small" /> :
                                      <NoMealsIcon fontSize="small" /> :
                                    null
                                }
                                label={attr.type === 'Dietary' ? getTranslatedDietaryOption(t, attr.value) : attr.value}
                                size="small"
                                variant="outlined"
                              />
                            </Tooltip>
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          <IconButton
                            color="primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOpenDialog(guest);
                            }}
                            size="small"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            color="error"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteConfirmation(guestId);
                            }}
                            size="small"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body1" sx={{ py: 2 }}>
                      {t('guests.noGuests', 'No guests found')}. {t('guests.addNewToStart', 'Add a new guest to get started.')}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredGuests.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </TableContainer>
      </Paper>

      {/* Guest Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingGuest ? t('guests.editGuest', 'Edit Guest') : t('guests.addNewGuest', 'Add New Guest')}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('guests.columns.name', 'Name')}
                name="name"
                value={newGuest.name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('guests.columns.email', 'Email')}
                name="email"
                type="email"
                value={newGuest.email}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('guests.columns.phone', 'Phone')}
                name="phone"
                value={newGuest.phone}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>{t('guests.columns.rsvpStatus', 'RSVP Status')}</InputLabel>
                <Select
                  name="rsvpStatus"
                  value={newGuest.rsvpStatus}
                  onChange={handleInputChange}
                  label={t('guests.columns.rsvpStatus', 'RSVP Status')}
                >
                  <MenuItem value="Confirmed">{t('guests.rsvpStatus.confirmed', 'Confirmed')}</MenuItem>
                  <MenuItem value="Pending">{t('guests.rsvpStatus.pending', 'Pending')}</MenuItem>
                  <MenuItem value="Declined">{t('guests.rsvpStatus.declined', 'Declined')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">{t('guests.guestAttributes', 'Guest Attributes')}</Typography>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleOpenAttributeDialog}
                  size="small"
                >
                  {t('guests.addAttribute', 'Add Attribute')}
                </Button>
              </Box>

              <Grid container spacing={1}>
                {newGuest.attributes.map((attr, index) => (
                  <Grid item key={index}>
                    <Chip
                      label={`${getTranslatedAttributeType(t, attr.type)}: ${attr.type === 'Dietary' ? getTranslatedDietaryOption(t, attr.value) : attr.value}`}
                      onDelete={() => handleRemoveAttribute(index)}
                      color="primary"
                      variant="outlined"
                    />
                  </Grid>
                ))}
                {newGuest.attributes.length === 0 && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      {t('guests.noAttributesYet', 'No attributes added yet. Click "Add Attribute" to add dietary preferences, allergies, or other special requirements.')}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t('common.cancel', 'Cancel')}</Button>
          <Button
            onClick={handleSaveGuest}
            variant="contained"
            color="primary"
            disabled={!newGuest.name}
          >
            {t('common.save', 'Save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Attribute Dialog */}
      <Dialog open={openAttributeDialog} onClose={handleCloseAttributeDialog}>
        <DialogTitle>{t('guests.addAttribute', 'Add Guest Attribute')}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 0.5 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>{t('guests.attributeType', 'Attribute Type')}</InputLabel>
                <Select
                  name="type"
                  value={newAttribute.type}
                  onChange={handleAttributeChange}
                  label={t('guests.attributeType', 'Attribute Type')}
                >
                  {attributeTypes.map((type) => (
                    <MenuItem key={type} value={type}>{getTranslatedAttributeType(t, type)}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              {newAttribute.type === 'Dietary' ? (
                <FormControl fullWidth>
                  <InputLabel>{t('guests.dietaryPreference', 'Dietary Preference')}</InputLabel>
                  <Select
                    name="value"
                    value={newAttribute.value}
                    onChange={handleAttributeChange}
                    label={t('guests.dietaryPreference', 'Dietary Preference')}
                  >
                    {dietaryOptions.map((option) => (
                      <MenuItem key={option} value={option}>{getTranslatedDietaryOption(t, option)}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              ) : (
                <TextField
                  fullWidth
                  label={t('guests.attributeValue', 'Attribute Value')}
                  name="value"
                  value={newAttribute.value}
                  onChange={handleAttributeChange}
                  placeholder={t('guests.enterTypeDetails', 'Enter {{type}} details', { type: getTranslatedAttributeType(t, newAttribute.type) })}
                />
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAttributeDialog}>{t('common.cancel', 'Cancel')}</Button>
          <Button
            onClick={handleAddAttribute}
            variant="contained"
            color="primary"
            disabled={!newAttribute.type || !newAttribute.value}
          >
            {t('common.add', 'Add')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onClose={handleCancelDelete}>
        <DialogTitle>{t('guests.confirmDelete', 'Confirm Delete')}</DialogTitle>
        <DialogContent>
          <Typography>{t('guests.deleteConfirmation', 'Are you sure you want to delete this guest?')}</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            {t('common.cannotUndo', 'This action cannot be undone.')}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>{t('common.cancel', 'Cancel')}</Button>
          <Button onClick={handleDeleteGuest} color="error" variant="contained">
            {t('common.delete', 'Delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default GuestManagement;
