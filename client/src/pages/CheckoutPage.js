import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import {
  Container,
  Box,
  Typography,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Paper
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import PaymentForm from '../components/PaymentForm';
import api from '../services/api';
import config from '../config';

// Initialize Stripe
const stripePromise = loadStripe(config.STRIPE_PUBLISHABLE_KEY || 'pk_test_your_stripe_test_key');

const CheckoutPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeStep, setActiveStep] = useState(0);
  const [planDetails, setPlanDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Get the plan ID from URL search params
  const searchParams = new URLSearchParams(location.search);
  const planId = searchParams.get('plan');
  const hasTrial = searchParams.get('trial') === 'true';
  const trialDays = hasTrial ? 14 : 0; // 14-day trial for Pro plan

  useEffect(() => {
    if (!planId) {
      setError('No plan selected');
      setLoading(false);
      return;
    }

    const fetchPlanDetails = async () => {
      try {
        const response = await api.get('/payments/plans');
        const selectedPlan = response.data.plans.find(plan => plan.id === planId);
        
        if (!selectedPlan) {
          setError('Invalid plan selected');
        } else {
          setPlanDetails(selectedPlan);
        }
      } catch (err) {
        console.error('Error fetching plan details:', err);
        setError('Failed to load plan details');
      } finally {
        setLoading(false);
      }
    };

    fetchPlanDetails();
  }, [planId]);

  const handlePaymentSuccess = () => {
    setActiveStep(2);
  };

  const handleCancel = () => {
    navigate('/pricing');
  };

  // Define steps for the checkout process
  const steps = [
    t('checkout.steps.details', 'Plan Details'),
    t('checkout.steps.payment', 'Payment'),
    t('checkout.steps.confirmation', 'Confirmation')
  ];

  // If session_id in URL, we've returned from Stripe with success
  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    if (sessionId) {
      setActiveStep(2); // Move to confirmation step
    }
  }, [searchParams]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h5" color="error" gutterBottom>
            {error}
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/pricing')}
            sx={{ mt: 2 }}
          >
            {t('checkout.returnToPricing', 'Return to Pricing')}
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 6 }}>
      <Box sx={{ mb: 4 }}>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>

      {activeStep === 0 && (
        <Box>
          <Typography variant="h4" gutterBottom>
            {t('checkout.reviewOrder', 'Review Your Order')}
          </Typography>
          
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                {planDetails.name} {t('checkout.plan', 'Plan')}
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                {planDetails.description}
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="body1">{t('checkout.price', 'Price')}:</Typography>
                <Typography variant="body1" fontWeight="bold">
                  ${planDetails.price}/{t('checkout.month', 'month')}
                </Typography>
              </Box>
              
              <Typography variant="subtitle1" gutterBottom>
                {t('checkout.features', 'Features')}:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                {planDetails.features.map((feature, index) => (
                  <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                    {feature}
                  </Typography>
                ))}
              </Box>
            </CardContent>
          </Card>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button variant="outlined" onClick={handleCancel}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button 
              variant="contained" 
              color="primary"
              onClick={() => setActiveStep(1)}
            >
              {t('checkout.proceedToPayment', 'Proceed to Payment')}
            </Button>
          </Box>
        </Box>
      )}

      {activeStep === 1 && (
        <Box>
          <Typography variant="h4" gutterBottom>
            {t('checkout.payment', 'Payment')}
          </Typography>
          
          <Elements stripe={stripePromise}>
            <PaymentForm 
              planId={planId} 
              trialDays={trialDays} 
              onSuccess={handlePaymentSuccess} 
              onCancel={handleCancel} 
            />
          </Elements>
        </Box>
      )}

      {activeStep === 2 && (
        <Box>
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h4" color="primary" gutterBottom>
              {t('checkout.thankYou', 'Thank You for Your Purchase!')}
            </Typography>
            
            <Typography variant="body1" paragraph>
              {t('checkout.confirmationMessage', 'Your subscription has been processed successfully. You now have access to all the features of the')} <strong>{planDetails?.name || 'Premium'}</strong> {t('checkout.plan', 'plan')}.
            </Typography>
            
            <Button 
              variant="contained" 
              color="primary"
              onClick={() => navigate('/events')}
              sx={{ mt: 2 }}
            >
              {t('checkout.getStarted', 'Get Started')}
            </Button>
          </Paper>
        </Box>
      )}
    </Container>
  );
};

export default CheckoutPage; 