import React, { useState, useContext } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Container,
  Button,
  Grid,
  Alert
} from '@mui/material';
import PeopleIcon from '@mui/icons-material/People';
import PlaceIcon from '@mui/icons-material/Place';
import BusinessIcon from '@mui/icons-material/Business';
import { Link, useNavigate } from 'react-router-dom';
import { EventContext } from '../contexts/EventContext';

// TabPanel component for tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`resources-tabpanel-${index}`}
      aria-labelledby={`resources-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `resources-tab-${index}`,
    'aria-controls': `resources-tabpanel-${index}`,
  };
}

const ResourceCard = ({ title, description, icon, linkTo }) => {
  return (
    <Grid item xs={12} md={6} lg={4}>
      <Paper
        elevation={3}
        sx={{
          p: 3,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          transition: 'transform 0.2s, box-shadow 0.2s',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)'
          }
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ mr: 2, color: 'primary.main' }}>
            {icon}
          </Box>
          <Typography variant="h6" component="h2">
            {title}
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flexGrow: 1 }}>
          {description}
        </Typography>
        <Button
          component={Link}
          to={linkTo}
          variant="contained"
          color="primary"
          fullWidth
        >
          Manage
        </Button>
      </Paper>
    </Grid>
  );
};

const Resources = () => {
  const [tabValue, setTabValue] = useState(0);
  const { selectedEventId, currentEvent } = useContext(EventContext);
  const navigate = useNavigate();

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleGoToEvents = () => {
    navigate('/events');
  };

  // If no event is selected, show a message
  if (!selectedEventId) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 3, mb: 4 }}>
          <Alert
            severity="info"
            sx={{ mb: 2 }}
            action={
              <Button color="inherit" size="small" onClick={handleGoToEvents}>
                GO TO EVENTS
              </Button>
            }
          >
            Please select an event to manage resources.
          </Alert>
          <Typography variant="h4" component="h1" gutterBottom>
            Resources Management
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Resources are specific to each event. Select an event from the dropdown in the navigation bar to manage resources for that event.
          </Typography>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Resources Management
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Manage all your event resources in one place - from guests to venues and more.
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mt: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="resources tabs"
            variant="fullWidth"
          >
            <Tab label="Overview" {...a11yProps(0)} />
            <Tab label="Guest Management" {...a11yProps(1)} />
            <Tab label="Venue Management" {...a11yProps(2)} />
            <Tab label="Stakeholder Management" {...a11yProps(3)} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <ResourceCard
              title="Guest Management"
              description="Manage your guest list, track RSVPs, and handle dietary preferences and other guest attributes."
              icon={<PeopleIcon fontSize="large" />}
              linkTo={selectedEventId ? `/resources/guests/${selectedEventId}` : "/events"}
            />
            <ResourceCard
              title="Venue Management"
              description="Create and manage venues, design floor plans, arrange tables and seats, and assign guests to specific positions."
              icon={<PlaceIcon fontSize="large" />}
              linkTo={selectedEventId ? `/resources/venues/${selectedEventId}` : "/events"}
            />
            <ResourceCard
              title="Stakeholder Management"
              description="Manage companies, organizations, and their contact points involved in your event."
              icon={<BusinessIcon fontSize="large" />}
              linkTo={selectedEventId ? `/resources/stakeholders/${selectedEventId}` : "/events"}
            />
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Guest Management
          </Typography>
          <Typography paragraph>
            Manage your guests, their RSVPs, and special attributes like dietary preferences.
          </Typography>
          <Button
            component={Link}
            to={selectedEventId ? `/resources/guests/${selectedEventId}` : "/events"}
            variant="contained"
            color="primary"
          >
            Go to Guest Management
          </Button>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Venue Management
          </Typography>
          <Typography paragraph>
            Create and manage venues, design floor plans, and assign guests to seats.
          </Typography>
          <Button
            component={Link}
            to={selectedEventId ? `/resources/venues/${selectedEventId}` : "/events"}
            variant="contained"
            color="primary"
          >
            Go to Venue Management
          </Button>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Stakeholder Management
          </Typography>
          <Typography paragraph>
            Manage companies, organizations, and their contact points involved in your event.
          </Typography>
          <Button
            component={Link}
            to={selectedEventId ? `/resources/stakeholders/${selectedEventId}` : "/events"}
            variant="contained"
            color="primary"
          >
            Go to Stakeholder Management
          </Button>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default Resources;
