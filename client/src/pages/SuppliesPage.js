import React, { useState, useEffect, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  FormControlLabel,
  Checkbox,
  Divider,
  CircularProgress,
  Alert,
  Tooltip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';
import LinkIcon from '@mui/icons-material/Link';
import { fetchSupplies, createSupply, updateSupply, deleteSupply } from '../services/supplyService';
import { fetchTasks } from '../services/taskService';
import { EventContext } from '../contexts/EventContext';

// Supply categories
const SUPPLY_CATEGORIES = [
  'Decoration',
  'Food',
  'Beverage',
  'Tableware',
  'Furniture',
  'Stationery',
  'Clothing',
  'Electronics',
  'Other'
];

// Supply units
const SUPPLY_UNITS = [
  'piece',
  'set',
  'box',
  'pack',
  'kg',
  'g',
  'l',
  'ml',
  'dozen',
  'pair',
  'meter',
  'cm'
];

const SupplyForm = ({ supply, onSubmit, onCancel, eventId, tasks }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: supply?.name || '',
    description: supply?.description || '',
    quantity: supply?.quantity || 1,
    unit: supply?.unit || 'piece',
    estimatedCost: supply?.estimatedCost || 0,
    actualCost: supply?.actualCost || 0,
    currency: supply?.currency || 'USD',
    isPurchased: supply?.isPurchased || false,
    purchaseDate: supply?.purchaseDate || null,
    category: supply?.category || 'Other',
    notes: supply?.notes || '',
    vendor: supply?.vendor || '',
    event: eventId,
    tasks: supply?.tasks || []
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleDateChange = (date) => {
    setFormData({
      ...formData,
      purchaseDate: date
    });
  };

  const handleTaskChange = (e) => {
    setFormData({
      ...formData,
      tasks: Array.from(e.target.selectedOptions, option => option.value)
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent event from bubbling up to parent forms

    // Format the data before submitting
    const formattedData = {
      ...formData,
      // Convert string values to appropriate types
      quantity: parseInt(formData.quantity) || 1,
      estimatedCost: parseFloat(formData.estimatedCost) || 0,
      actualCost: parseFloat(formData.actualCost) || 0,
      isPurchased: Boolean(formData.isPurchased),
      // Ensure required fields have values
      name: formData.name || 'Unnamed Supply',
      category: formData.category || 'Other',
      currency: formData.currency || 'USD',
      unit: formData.unit || 'piece'
    };

    onSubmit(formattedData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            name="name"
            label={t('supplies.name', 'Supply Name')}
            value={formData.name}
            onChange={handleChange}
            fullWidth
            required
            margin="normal"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            name="description"
            label={t('supplies.description', 'Description')}
            value={formData.description}
            onChange={handleChange}
            fullWidth
            multiline
            rows={2}
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <TextField
            name="quantity"
            label={t('supplies.quantity', 'Quantity')}
            type="number"
            value={formData.quantity}
            onChange={handleChange}
            fullWidth
            inputProps={{ min: 0 }}
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="unit-label">{t('supplies.unit', 'Unit')}</InputLabel>
            <Select
              labelId="unit-label"
              name="unit"
              value={formData.unit}
              onChange={handleChange}
              label={t('supplies.unit', 'Unit')}
            >
              {SUPPLY_UNITS.map((unit) => (
                <MenuItem key={unit} value={unit}>
                  {t(`supplies.units.${unit.toLowerCase()}`, unit)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={6}>
          <TextField
            name="estimatedCost"
            label={t('supplies.estimatedCost', 'Estimated Cost')}
            type="number"
            value={formData.estimatedCost}
            onChange={handleChange}
            fullWidth
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <TextField
            name="actualCost"
            label={t('supplies.actualCost', 'Actual Cost')}
            type="number"
            value={formData.actualCost}
            onChange={handleChange}
            fullWidth
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="category-label">{t('supplies.category', 'Category')}</InputLabel>
            <Select
              labelId="category-label"
              name="category"
              value={formData.category}
              onChange={handleChange}
              label={t('supplies.category', 'Category')}
            >
              {SUPPLY_CATEGORIES.map((category) => (
                <MenuItem key={category} value={category}>
                  {t(`supplies.categories.${category.toLowerCase()}`, category)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={6}>
          <TextField
            name="vendor"
            label={t('supplies.vendor', 'Vendor')}
            value={formData.vendor}
            onChange={handleChange}
            fullWidth
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <FormControlLabel
            control={
              <Checkbox
                name="isPurchased"
                checked={formData.isPurchased}
                onChange={handleChange}
              />
            }
            label={t('supplies.isPurchased', 'Purchased')}
            sx={{ mt: 2 }}
          />
        </Grid>

        <Grid item xs={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={t('supplies.purchaseDate', 'Purchase Date')}
              value={formData.purchaseDate}
              onChange={handleDateChange}
              renderInput={(params) => <TextField {...params} fullWidth margin="normal" />}
              disabled={!formData.isPurchased}
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={12}>
          <TextField
            name="notes"
            label={t('supplies.notes', 'Notes')}
            value={formData.notes}
            onChange={handleChange}
            fullWidth
            multiline
            rows={2}
            margin="normal"
          />
        </Grid>

        {tasks && tasks.length > 0 && (
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="tasks-label">{t('supplies.associatedTasks', 'Associated Tasks')}</InputLabel>
              <Select
                labelId="tasks-label"
                name="tasks"
                multiple
                value={formData.tasks}
                onChange={handleChange}
                label={t('supplies.associatedTasks', 'Associated Tasks')}
              >
                {tasks.map((task) => (
                  <MenuItem key={task._id} value={task._id}>
                    {task.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        <Grid item xs={12} sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button variant="outlined" onClick={onCancel}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button variant="contained" color="primary" type="submit">
            {supply ? t('common.update', 'Update') : t('common.add', 'Add')}
          </Button>
        </Grid>
      </Grid>
    </form>
  );
};

const SuppliesPage = () => {
  const { t } = useTranslation();
  const { selectedEventId, currentEvent } = useContext(EventContext);
  const [supplies, setSupplies] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedSupply, setSelectedSupply] = useState(null);
  const [confirmDeleteDialog, setConfirmDeleteDialog] = useState(false);
  const [supplyToDelete, setSupplyToDelete] = useState(null);

  // Fetch supplies and tasks when the selected event changes
  useEffect(() => {
    const fetchData = async () => {
      if (!currentEvent) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        console.log('Fetching supplies for event:', currentEvent._id);
        // Fetch supplies for the selected event
        const suppliesData = await fetchSupplies(currentEvent._id);
        setSupplies(suppliesData);

        // Fetch tasks for the selected event
        const tasksData = await fetchTasks(currentEvent._id);
        setTasks(tasksData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('supplies.fetchError', 'Failed to load supplies. Please try again.'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentEvent, t]);

  const handleOpenDialog = (supply = null) => {
    setSelectedSupply(supply);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedSupply(null);
  };

  const handleOpenDeleteDialog = (supply) => {
    setSupplyToDelete(supply);
    setConfirmDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setConfirmDeleteDialog(false);
    setSupplyToDelete(null);
  };

  const handleCreateSupply = async (formData) => {
    try {
      const newSupply = await createSupply(formData);
      setSupplies([...supplies, newSupply]);
      handleCloseDialog();
    } catch (err) {
      console.error('Error creating supply:', err);
      setError(t('supplies.createError', 'Failed to create supply. Please try again.'));
    }
  };

  const handleUpdateSupply = async (formData) => {
    try {
      const updatedSupply = await updateSupply(selectedSupply._id, formData);
      setSupplies(supplies.map(supply =>
        supply._id === updatedSupply._id ? updatedSupply : supply
      ));
      handleCloseDialog();
    } catch (err) {
      console.error('Error updating supply:', err);
      setError(t('supplies.updateError', 'Failed to update supply. Please try again.'));
    }
  };

  const handleDeleteSupply = async () => {
    if (!supplyToDelete) return;

    try {
      await deleteSupply(supplyToDelete._id);
      setSupplies(supplies.filter(supply => supply._id !== supplyToDelete._id));
      handleCloseDeleteDialog();
    } catch (err) {
      console.error('Error deleting supply:', err);
      setError(t('supplies.deleteError', 'Failed to delete supply. Please try again.'));
    }
  };

  const handleSubmit = (formData) => {
    if (selectedSupply) {
      handleUpdateSupply(formData);
    } else {
      handleCreateSupply(formData);
    }
  };

  // Get task names for a supply
  const getTaskNames = (supplyTasks) => {
    if (!supplyTasks || !supplyTasks.length) return '-';

    return supplyTasks.map(taskId => {
      const task = tasks.find(t => t._id === taskId);
      return task ? task.name : 'Unknown Task';
    }).join(', ');
  };

  // Calculate total costs
  const calculateTotals = () => {
    return supplies.reduce((totals, supply) => {
      totals.estimated += supply.estimatedCost || 0;
      totals.actual += supply.actualCost || 0;
      return totals;
    }, { estimated: 0, actual: 0 });
  };

  const totals = calculateTotals();

  if (!currentEvent) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          {t('supplies.noEventSelected', 'Please select an event to view supplies')}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          {t('supplies.title', 'Wedding Supplies')}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {t('supplies.addSupply', 'Add Supply')}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ mb: 3, p: 2 }}>
        <Typography variant="h6" gutterBottom>
          {t('supplies.summary', 'Summary')}
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <Typography variant="body1">
              {t('supplies.totalItems', 'Total Items')}: {supplies.length}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body1">
              {t('supplies.purchasedItems', 'Purchased Items')}: {supplies.filter(s => s.isPurchased).length}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body1">
              {t('supplies.totalEstimatedCost', 'Total Estimated Cost')}: ${totals.estimated.toLocaleString()}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography
              variant="body1"
              color={totals.actual > totals.estimated ? 'error' : 'inherit'}
            >
              {t('supplies.totalActualCost', 'Total Actual Cost')}: ${totals.actual.toLocaleString()}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : supplies.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            {t('supplies.noSupplies', 'No supplies added yet')}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            sx={{ mt: 2 }}
          >
            {t('supplies.addFirstSupply', 'Add Your First Supply')}
          </Button>
        </Paper>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('supplies.name', 'Name')}</TableCell>
                <TableCell>{t('supplies.category', 'Category')}</TableCell>
                <TableCell align="right">{t('supplies.quantity', 'Quantity')}</TableCell>
                <TableCell align="right">{t('supplies.estimatedCost', 'Estimated Cost')}</TableCell>
                <TableCell align="right">{t('supplies.actualCost', 'Actual Cost')}</TableCell>
                <TableCell>{t('supplies.vendor', 'Vendor')}</TableCell>
                <TableCell>{t('supplies.status', 'Status')}</TableCell>
                <TableCell>{t('supplies.associatedTasks', 'Associated Tasks')}</TableCell>
                <TableCell align="right">{t('common.actions', 'Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {supplies.map((supply) => (
                <TableRow key={supply._id}>
                  <TableCell>{supply.name}</TableCell>
                  <TableCell>
                    <Chip
                      label={t(`supplies.categories.${supply.category.toLowerCase()}`, supply.category)}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell align="right">{`${supply.quantity} ${supply.unit}`}</TableCell>
                  <TableCell align="right">${supply.estimatedCost?.toLocaleString() || 0}</TableCell>
                  <TableCell align="right">
                    <Typography
                      color={supply.actualCost > supply.estimatedCost ? 'error.main' : 'inherit'}
                    >
                      ${supply.actualCost?.toLocaleString() || 0}
                    </Typography>
                  </TableCell>
                  <TableCell>{supply.vendor || '-'}</TableCell>
                  <TableCell>
                    <Chip
                      label={supply.isPurchased ? t('supplies.purchased', 'Purchased') : t('supplies.notPurchased', 'Not Purchased')}
                      size="small"
                      color={supply.isPurchased ? 'success' : 'default'}
                    />
                  </TableCell>
                  <TableCell>
                    <Tooltip title={getTaskNames(supply.tasks)}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {supply.tasks && supply.tasks.length > 0 ? (
                          <>
                            <LinkIcon fontSize="small" sx={{ mr: 0.5 }} />
                            <Typography variant="body2" noWrap sx={{ maxWidth: 150 }}>
                              {getTaskNames(supply.tasks)}
                            </Typography>
                          </>
                        ) : (
                          '-'
                        )}
                      </Box>
                    </Tooltip>
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(supply)}
                      color="primary"
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDeleteDialog(supply)}
                      color="error"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Create/Edit Supply Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {selectedSupply
              ? t('supplies.editSupply', 'Edit Supply')
              : t('supplies.addSupply', 'Add Supply')}
            <IconButton onClick={handleCloseDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <SupplyForm
            supply={selectedSupply}
            onSubmit={handleSubmit}
            onCancel={handleCloseDialog}
            eventId={currentEvent._id}
            tasks={tasks}
          />
        </DialogContent>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog
        open={confirmDeleteDialog}
        onClose={handleCloseDeleteDialog}
      >
        <DialogTitle>
          {t('supplies.confirmDelete', 'Confirm Delete')}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {t('supplies.deleteConfirmation', 'Are you sure you want to delete this supply? This action cannot be undone.')}
          </Typography>
          {supplyToDelete && (
            <Typography variant="subtitle1" sx={{ mt: 2, fontWeight: 'bold' }}>
              {supplyToDelete.name}
            </Typography>
          )}
        </DialogContent>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 2, gap: 1 }}>
          <Button onClick={handleCloseDeleteDialog}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button onClick={handleDeleteSupply} variant="contained" color="error">
            {t('common.delete', 'Delete')}
          </Button>
        </Box>
      </Dialog>
    </Box>
  );
};

export default SuppliesPage;
