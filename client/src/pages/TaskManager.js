import React, { useState, useEffect, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Tabs,
  Tab,
  Button,
  Dialog,
  DialogContent,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  IconButton,
  DialogTitle,
  Tooltip,
  Divider,
  Snackbar
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import SortIcon from '@mui/icons-material/Sort';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import TaskList from '../components/TaskList';
import TaskForm from '../components/TaskForm';
import DefaultTaskSelector from '../components/DefaultTaskSelector';
import DependencyGraph from '../components/DependencyGraph';
import TaskExportImport from '../components/TaskExportImport';
import { fetchUsers } from '../services/userService';
import { EventContext } from '../contexts/EventContext';
import { fetchTasks, createTask, updateTask, deleteTask, batchDeleteTasks } from '../services/taskService';
import api from '../services/api';

const TaskManager = () => {
  const { id: urlEventId, view } = useParams();
  const { selectedEventId, currentEvent, setSelectedEventId } = useContext(EventContext);
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Use URL param if available, otherwise use context
  const eventId = urlEventId || selectedEventId;

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [event, setEvent] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [openTaskDialog, setOpenTaskDialog] = useState(false);
  const [openDefaultTasksDialog, setOpenDefaultTasksDialog] = useState(false);
  const [openFilterDialog, setOpenFilterDialog] = useState(false);
  const [currentTask, setCurrentTask] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  // Map URL view parameter to tab index
const getTabValueFromView = (viewParam) => {
    switch(viewParam) {
      case 'list': return 0;
      case 'graph': return 1;
      default: return 0; // Default to list view
    }
  };

  // Initialize tab value based on URL parameter if available
  const [tabValue, setTabValue] = useState(view ? getTabValueFromView(view) : 0);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter states
  const [filters, setFilters] = useState({
    status: [],
    taskType: [],
    assignee: [],
    dueDate: 'all', // 'all', 'today', 'week', 'month', 'overdue'
    sortBy: 'deadline' // 'deadline', 'name', 'status', 'type'
  });

  // Users state to store fetched users
  const [users, setUsers] = useState([]);

  // Load users from API
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const userData = await fetchUsers();
        setUsers(userData);
      } catch (error) {
        console.error('Error loading users:', error);
        setUsers([]);
      }
    };

    loadUsers();
  }, []);

  // Add hasJoined property to users based on event data
  const [usersWithJoinStatus, setUsersWithJoinStatus] = useState([]);

  useEffect(() => {
    if (users.length > 0 && event) {
      // Mark users who have joined the event (owner or accepted collaborators)
      const enhancedUsers = users.map(user => {
        // Check if user is the event owner (handle both string ID and object with _id)
        const isOwner =
          (typeof event.owner === 'string' && event.owner === user._id) ||
          (event.owner && event.owner._id && event.owner._id.toString() === user._id);

        // Check if user is a collaborator who has accepted the invitation
        const isCollaborator = event.collaborators?.some(collab => {
          // Handle both string ID and object with _id
          const collabUserId =
            (typeof collab.user === 'string') ? collab.user :
            (collab.user && collab.user._id) ? collab.user._id.toString() : null;

          return collabUserId === user._id;
        });

        // Add hasJoined property
        return {
          ...user,
          hasJoined: isOwner || isCollaborator
        };
      });

      setUsersWithJoinStatus(enhancedUsers);
    } else {
      setUsersWithJoinStatus(users);
    }
  }, [users, event]);

  // Load event and tasks
  useEffect(() => {
    if (!eventId) {
      navigate('/events');
      return;
    }

    // Update tab value if URL view parameter changes
    if (view) {
      setTabValue(getTabValueFromView(view));
    }

    // If we came from URL, update the context
    if (urlEventId && urlEventId !== selectedEventId) {
      setSelectedEventId(urlEventId);
    }

    const loadData = async () => {
      setLoading(true);
      setError('');

      try {
        // Get event data
        let eventData = currentEvent;

        if (!eventData) {
          // Get from API only - no localStorage fallback for events
          try {
            console.log('Fetching event data from API for event:', eventId);
            const response = await fetch(`${api.defaults.baseURL}/events/${eventId}`);
            if (response.ok) {
              eventData = await response.json();
              console.log('Successfully fetched event data from API:', eventData);
            } else {
              console.warn('API request failed, status:', response.status);
              throw new Error('Failed to fetch event data from API');
            }
          } catch (apiError) {
            console.error('Error fetching event data from API:', apiError);
            throw new Error('Failed to fetch event data from API');
          }

          if (!eventData) {
            throw new Error('Event not found');
          }
        }

        setEvent(eventData);

        // Get tasks for this event
        console.log('Fetching tasks for event:', eventId);
        try {
          const tasksData = await fetchTasks(eventId);
          console.log('Tasks fetched:', tasksData.length);
          setTasks(tasksData);
          setFilteredTasks(tasksData.filter(task => !task.deleted));
        } catch (taskError) {
          console.error('Error fetching tasks:', taskError);
          setError(t('tasks.errors.loadFailed', 'Failed to load tasks from the database. Please check your connection and try again.'));
          setTasks([]);
          setFilteredTasks([]);
        }
      } catch (err) {
        console.error('Error loading task data:', err);
        setError(err.message || t('tasks.errors.loadFailed', 'Failed to load tasks'));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [eventId, currentEvent, selectedEventId, urlEventId, navigate, setSelectedEventId]);

  // Apply filters and search whenever tasks, filters, or search query changes
  useEffect(() => {
    if (!tasks.length) return;

    let result = [...tasks];

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(task =>
        task.name.toLowerCase().includes(query) ||
        (task.details && task.details.toLowerCase().includes(query))
      );
    }

    // Apply status filter
    if (filters.status.length > 0) {
      result = result.filter(task => filters.status.includes(task.status));
    }

    // Apply task type filter
    if (filters.taskType.length > 0) {
      result = result.filter(task => filters.taskType.includes(task.taskType));
    }

    // Apply assignee filter
    if (filters.assignee.length > 0) {
      result = result.filter(task => {
        // Handle unassigned tasks
        if (filters.assignee.includes('unassigned') && (!task.assignees || task.assignees.length === 0)) {
          return true;
        }

        // For tasks with assignees, check if any of the task's assignees match the filter
        return task.assignees && task.assignees.some(assignee => {
          // Handle non-user assignees (they have isNonUserAssignee flag and no _id)
          if (assignee.isNonUserAssignee) {
            // If 'external' is in the filter, include all non-user assignees
            return filters.assignee.includes('external');
          }

          // For regular user assignees, check if their ID is in the filter
          return assignee._id && filters.assignee.includes(assignee._id);
        });
      });
    }

    // Apply due date filter
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekLater = new Date(today);
    weekLater.setDate(today.getDate() + 7);
    const monthLater = new Date(today);
    monthLater.setMonth(today.getMonth() + 1);

    if (filters.dueDate !== 'all') {
      result = result.filter(task => {
        const deadline = task.hardDeadline ? new Date(task.hardDeadline) :
                         task.softDeadline ? new Date(task.softDeadline) : null;

        if (!deadline) return false;

        switch (filters.dueDate) {
          case 'today':
            return deadline >= today && deadline < new Date(today.getTime() + 24 * 60 * 60 * 1000);
          case 'week':
            return deadline >= today && deadline <= weekLater;
          case 'month':
            return deadline >= today && deadline <= monthLater;
          case 'overdue':
            return deadline < today;
          default:
            return true;
        }
      });
    }

    // Apply sorting
    result.sort((a, b) => {
      switch (filters.sortBy) {
        case 'name':
          return (a.name || '').localeCompare(b.name || '');
        case 'status':
          return (a.status || '').localeCompare(b.status || '');
        case 'type':
          return (a.taskType || '').localeCompare(b.taskType || '');
        case 'deadline':
        default:
          const aDeadline = a.hardDeadline || a.softDeadline || new Date(9999, 11, 31);
          const bDeadline = b.hardDeadline || b.softDeadline || new Date(9999, 11, 31);
          return new Date(aDeadline) - new Date(bDeadline);
      }
    });

    setFilteredTasks(result);
  }, [tasks, filters, searchQuery]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);

    // Update URL when tab changes
    const viewParam = newValue === 0 ? 'list' : 'graph';
    navigate(`/tasks/${eventId}/${viewParam}`);
  };

  const handleOpenTaskDialog = async (task = null) => {
    if (task) {
      console.log('Opening task for editing:', task._id);

      try {
        // Import the fetchTask function
        const { fetchTask } = await import('../services/taskService');

        // Fetch the full task data with populated subtasks
        const fullTask = await fetchTask(task._id);
        console.log('Fetched full task data:', fullTask);

        // Check if the task has subtasks
        if (fullTask.subtasks && fullTask.subtasks.length > 0) {
          console.log('Task has subtasks:', fullTask.subtasks.length);

          // Create a copy of the task to avoid modifying the original
          const taskCopy = { ...fullTask };

          // Process each subtask to ensure it's a full object
          const processedSubtasks = [];

          for (const subtaskId of fullTask.subtasks) {
            // Find the full task object for this subtask ID
            const subtaskIdStr = typeof subtaskId === 'string' ? subtaskId :
              (subtaskId && subtaskId._id ? subtaskId._id.toString() : '');

            if (subtaskIdStr) {
              // Look for the subtask in the tasks array
              const subtaskObj = tasks.find(t => t._id.toString() === subtaskIdStr);

              if (subtaskObj) {
                console.log(`Found full subtask object for ID ${subtaskIdStr}:`, subtaskObj.name);
                processedSubtasks.push(subtaskObj);
              } else {
                console.log(`Could not find full subtask object for ID ${subtaskIdStr}, fetching from API`);
                try {
                  // Fetch the subtask data from the API
                  const subtaskData = await fetchTask(subtaskIdStr);
                  console.log(`Fetched subtask data for ID ${subtaskIdStr}:`, subtaskData.name);
                  processedSubtasks.push(subtaskData);
                } catch (subtaskError) {
                  console.error(`Error fetching subtask with ID ${subtaskIdStr}:`, subtaskError);
                  // If we can't fetch the subtask, use the ID as a fallback
                  processedSubtasks.push(subtaskIdStr);
                }
              }
            }
          }

          // Replace the subtasks array with the processed one
          taskCopy.subtasks = processedSubtasks;
          console.log('Processed subtasks:', taskCopy.subtasks.length);

          setCurrentTask(taskCopy);
        } else {
          setCurrentTask(fullTask);
        }
      } catch (error) {
        console.error('Error fetching full task data:', error);
        // If there's an error fetching the full task data, use the original task as a fallback
        setCurrentTask(task);
      }
    } else {
      setCurrentTask(null);
    }

    setOpenTaskDialog(true);
  };

  const handleCloseTaskDialog = () => {
    setOpenTaskDialog(false);
    setCurrentTask(null);
  };

  const handleOpenDefaultTasksDialog = () => {
    setOpenDefaultTasksDialog(true);
  };

  const handleCloseDefaultTasksDialog = () => {
    setOpenDefaultTasksDialog(false);
  };

  const handleOpenFilterDialog = () => {
    setOpenFilterDialog(true);
  };

  const handleCloseFilterDialog = () => {
    setOpenFilterDialog(false);
  };

  const handleTaskCreate = async (taskData) => {
    try {
      setLoading(true);
      const newTask = await createTask(eventId, taskData);

      setTasks(prevTasks => {
        const updatedTasks = [...prevTasks, newTask];
        setFilteredTasks(updatedTasks.filter(task => !task.deleted));
        return updatedTasks;
      });

      // Close the dialog on successful creation
      handleCloseTaskDialog();

      // Show success message
      setSnackbarMessage(t('tasks.success.created'));
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error creating task:', error);
      // Show error message
      setSnackbarMessage(t('tasks.errors.createFailed', { message: error.message || 'Unknown error' }));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const handleTaskUpdate = async (taskData) => {
    try {
      setLoading(true);

      // Log the task update request
      console.log('TaskManager: Updating task with ID:', taskData._id);
      console.log('TaskManager: Update payload:', JSON.stringify(taskData, null, 2));
      console.log('TaskManager: This should use the PUT HTTP method');

      // Call the updateTask function which should use PUT method
      const updatedTask = await updateTask(taskData._id, taskData);
      console.log('TaskManager: Task update successful:', updatedTask);

      // Update the local state with the updated task
      setTasks(prevTasks => {
        const updatedTasks = prevTasks.map(task =>
          task._id === updatedTask._id ? updatedTask : task
        );
        setFilteredTasks(updatedTasks.filter(task => !task.deleted));
        return updatedTasks;
      });

      // Close the dialog on successful update
      handleCloseTaskDialog();

      // Return the updated task for any component that needs it
      return updatedTask;
    } catch (error) {
      console.error('Error updating task:', error);
      // Don't close the dialog on error - let the user see the error
      // Re-throw the error so the calling component can handle it
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleDefaultTasksSelected = async (selectedTasks) => {
    try {
      // Create tasks directly in the database via API
      const createdTasks = [];

      // Process each task sequentially
      for (const taskTemplate of selectedTasks) {
        try {
          // Create a new task based on the template
          const newTask = await createTask(eventId, {
            ...taskTemplate,
            event: eventId
          });

          createdTasks.push(newTask);
        } catch (taskError) {
          console.error('Error creating individual task:', taskError);
        }
      }

      // Update local state with the newly created tasks
      setTasks(prevTasks => {
        const updatedTasks = [...prevTasks, ...createdTasks];
        setFilteredTasks(updatedTasks.filter(task => !task.deleted));
        return updatedTasks;
      });

      // Close dialog
      handleCloseDefaultTasksDialog();
    } catch (err) {
      console.error('Error adding default tasks:', err);
      setError(t('tasks.errors.addDefaultFailed'));
    }
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleFilterChange = (filterType, value) => {
    setFilters({
      ...filters,
      [filterType]: value
    });
  };

  const handleClearFilters = () => {
    setFilters({
      status: [],
      taskType: [],
      assignee: [],
      dueDate: 'all',
      sortBy: 'deadline'
    });
    setSearchQuery('');
  };

  // Get unique task types from tasks
  const getTaskTypes = () => {
    const types = new Set();
    tasks.forEach(task => {
      if (task.taskType) types.add(task.taskType);
    });
    return Array.from(types).sort();
  };

  // Get unique statuses from tasks
  const getTaskStatuses = () => {
    const statuses = new Set();
    tasks.forEach(task => {
      if (task.status) statuses.add(task.status);
    });
    return Array.from(statuses).sort();
  };

  // Get unique assignees from tasks
  const getAssignees = () => {
    const assigneeMap = new Map();
    const result = [{
      _id: 'unassigned',
      name: 'Unassigned',
      isSpecial: true
    }];

    tasks.forEach(task => {
      if (task.assignees && task.assignees.length > 0) {
        task.assignees.forEach(assignee => {
          if (assignee && !assigneeMap.has(assignee._id)) {
            assigneeMap.set(assignee._id, true);
            result.push(assignee);
          }
        });
      }
    });

    return result;
  };

  // Handle dependency creation
  const handleDependencyCreate = (sourceId, targetId, updatedTask) => {
    // Make sure we're not losing the event ID
    const taskWithEvent = {
      ...updatedTask,
      event: eventId
    };
    // Use handleTaskUpdate instead of handleTaskCreate since we're updating an existing task
    handleTaskUpdate(taskWithEvent);
  };

  // Handle dependency removal
  const handleDependencyRemove = (sourceId, targetId, updatedTask) => {
    // Make sure we're not losing the event ID
    const taskWithEvent = {
      ...updatedTask,
      event: eventId
    };
    // Use handleTaskUpdate instead of handleTaskCreate since we're updating an existing task
    handleTaskUpdate(taskWithEvent);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  const taskTypes = getTaskTypes();
  const taskStatuses = getTaskStatuses();
  const assigneeOptions = getAssignees();

  return (
    <Box sx={{ p: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          {t('tasks.titleForEvent', 'Tasks for {{eventTitle}}', { eventTitle: event?.title || t('events.generic', 'Event') })}
        </Typography>

        <Box>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={handleOpenDefaultTasksDialog}
            sx={{ mr: 1 }}
          >
            {t('tasks.addDefault', 'Add Default Tasks')}
          </Button>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenTaskDialog()}
          >
            {t('tasks.create', 'Create Task')}
          </Button>
        </Box>
      </Box>

      {/* Search and Filter Bar */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                onClick={handleOpenFilterDialog}
                sx={{ mr: 1 }}
              >
                Filter
              </Button>

              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  label="Sort By"
                  size="small"
                  startAdornment={<SortIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  <MenuItem value="deadline">{t('tasks.filters.sortOptions.deadline')}</MenuItem>
                  <MenuItem value="name">{t('tasks.filters.sortOptions.name')}</MenuItem>
                  <MenuItem value="status">{t('tasks.filters.sortOptions.status')}</MenuItem>
                  <MenuItem value="type">{t('tasks.filters.sortOptions.type')}</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>

          {/* Active Filters Display */}
          {(filters.status.length > 0 ||
            filters.taskType.length > 0 ||
            filters.assignee.length > 0 ||
            filters.dueDate !== 'all') && (
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  {t('tasks.filters.activeFilters')}:
                </Typography>

                {filters.status.map(status => (
                  <Chip
                    key={`status-${status}`}
                    label={`${t('tasks.filters.status')}: ${status}`}
                    onDelete={() => handleFilterChange('status', filters.status.filter(s => s !== status))}
                    size="small"
                  />
                ))}

                {filters.taskType.map(type => (
                  <Chip
                    key={`type-${type}`}
                    label={`${t('tasks.filters.taskType')}: ${type}`}
                    onDelete={() => handleFilterChange('taskType', filters.taskType.filter(t => t !== type))}
                    size="small"
                  />
                ))}

                {filters.assignee.map(id => {
                  // Special handling for 'unassigned' and 'external' filters
                  if (id === 'unassigned') {
                    return (
                      <Chip
                        key="assignee-unassigned"
                        label={`${t('tasks.filters.assignee')}: ${t('tasks.filters.unassigned', 'Unassigned')}`}
                        onDelete={() => handleFilterChange('assignee', filters.assignee.filter(a => a !== 'unassigned'))}
                        size="small"
                      />
                    );
                  }

                  if (id === 'external') {
                    return (
                      <Chip
                        key="assignee-external"
                        label={`${t('tasks.filters.assignee')}: ${t('tasks.filters.external', 'External')}`}
                        onDelete={() => handleFilterChange('assignee', filters.assignee.filter(a => a !== 'external'))}
                        size="small"
                        color="secondary"
                      />
                    );
                  }

                  // Regular user assignees
                  const assignee = assigneeOptions.find(a => a._id === id);
                  return (
                    <Chip
                      key={`assignee-${id}`}
                      label={`${t('tasks.filters.assignee')}: ${assignee ? assignee.name : id}`}
                      onDelete={() => handleFilterChange('assignee', filters.assignee.filter(a => a !== id))}
                      size="small"
                    />
                  );
                })}

                {filters.dueDate !== 'all' && (
                  <Chip
                    label={`${t('tasks.filters.dueDate')}: ${t(`tasks.filters.dueDates.${filters.dueDate}`)}`}
                    onDelete={() => handleFilterChange('dueDate', 'all')}
                    size="small"
                  />
                )}

                <Button
                  variant="text"
                  size="small"
                  onClick={handleClearFilters}
                >
                  {t('tasks.filters.clearAll')}
                </Button>
              </Box>
            </Grid>
          )}
        </Grid>
      </Paper>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label={t('tasks.views.list', 'List View')} />
          <Tab label={t('tasks.views.dependencyGraph', 'Dependency Graph')} />
        </Tabs>
      </Paper>

      {/* Task Export/Import Component */}
      {tabValue === 0 && (
        <TaskExportImport
          eventId={eventId}
          onTasksImported={(importedTasks) => {
            // If we have imported tasks directly from the server, use them
            if (importedTasks && importedTasks.length > 0) {
              console.log('Using imported tasks directly:', importedTasks.length);

              // Merge the imported tasks with existing tasks
              setTasks(prevTasks => {
                // Create a map of existing task IDs
                const existingTaskIds = new Set(prevTasks.map(task => task._id));

                // Filter out imported tasks that already exist
                const newTasks = importedTasks.filter(task => !existingTaskIds.has(task._id));

                // Combine existing tasks with new tasks
                const updatedTasks = [...prevTasks, ...newTasks];
                setFilteredTasks(updatedTasks.filter(task => !task.deleted));
                return updatedTasks;
              });
            } else {
              // Otherwise, refresh tasks from the server
              console.log('Refreshing tasks from server after import');
              setLoading(true);
              fetchTasks(eventId)
                .then(tasksData => {
                  setTasks(tasksData);
                  setFilteredTasks(tasksData.filter(task => !task.deleted));
                })
                .catch(error => {
                  console.error('Error refreshing tasks after import:', error);
                  setError(t('tasks.errors.loadFailed'));
                })
                .finally(() => {
                  setLoading(false);
                });
            }
          }}
        />
      )}

      {tabValue === 0 && (
        <TaskList
          tasks={filteredTasks}
          onTaskClick={handleOpenTaskDialog}
          onTaskUpdate={handleTaskUpdate}
          onBatchDelete={async (taskIds) => {
            try {
              setLoading(true);

              // Call the batch delete function
              const result = await batchDeleteTasks(taskIds);

              // Update the local state by filtering out the deleted tasks
              setTasks(prevTasks => {
                const updatedTasks = prevTasks.filter(task => !taskIds.includes(task._id));
                setFilteredTasks(updatedTasks.filter(task => !task.deleted));
                return updatedTasks;
              });

              // Show success message
              setSnackbarMessage(t('tasks.success.batchDeleted', { count: result.deletedCount }));
              setSnackbarSeverity('success');
              setSnackbarOpen(true);

              return result;
            } catch (error) {
              console.error('Error batch deleting tasks:', error);

              // Show error message
              setSnackbarMessage(t('tasks.errors.batchDeleteFailed', { message: error.message || 'Unknown error' }));
              setSnackbarSeverity('error');
              setSnackbarOpen(true);

              throw error;
            } finally {
              setLoading(false);
            }
          }}
        />
      )}

      {tabValue === 1 && (
        <DependencyGraph
          tasks={filteredTasks}
          onTaskCreate={handleTaskCreate}
          onTaskUpdate={handleTaskUpdate}
          onDependencyCreate={handleDependencyCreate}
          onDependencyRemove={handleDependencyRemove}
          eventId={eventId}
          assignees={usersWithJoinStatus}
        />
      )}

      {/* Task Dialog */}
      <Dialog
        open={openTaskDialog}
        onClose={handleCloseTaskDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {currentTask ? t('tasks.edit', 'Edit Task') : t('tasks.createNew', 'Create New Task')}
            <IconButton onClick={handleCloseTaskDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <TaskForm
            task={currentTask}
            onSubmit={currentTask ? handleTaskUpdate : handleTaskCreate}
            allTasks={tasks}
            assignees={usersWithJoinStatus}
            eventId={eventId}
          />
        </DialogContent>
      </Dialog>

      {/* Default Tasks Dialog */}
      <Dialog
        open={openDefaultTasksDialog}
        onClose={handleCloseDefaultTasksDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {t('tasks.addDefault')}
            <IconButton onClick={handleCloseDefaultTasksDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <DefaultTaskSelector
            eventType={event?.eventType}
            eventDate={event?.date}
            onTasksSelected={handleDefaultTasksSelected}
          />
        </DialogContent>
      </Dialog>

      {/* Filter Dialog */}
      <Dialog
        open={openFilterDialog}
        onClose={handleCloseFilterDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {t('tasks.filters.title')}
            <IconButton onClick={handleCloseFilterDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3}>
            {/* Status Filter */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>{t('tasks.filters.status')}</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {taskStatuses.map(status => (
                  <Chip
                    key={status}
                    label={status}
                    onClick={() => {
                      const newStatuses = filters.status.includes(status)
                        ? filters.status.filter(s => s !== status)
                        : [...filters.status, status];
                      handleFilterChange('status', newStatuses);
                    }}
                    color={filters.status.includes(status) ? "primary" : "default"}
                  />
                ))}
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            {/* Task Type Filter */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>{t('tasks.filters.taskType')}</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {taskTypes.map(type => (
                  <Chip
                    key={type}
                    label={type}
                    onClick={() => {
                      const newTypes = filters.taskType.includes(type)
                        ? filters.taskType.filter(t => t !== type)
                        : [...filters.taskType, type];
                      handleFilterChange('taskType', newTypes);
                    }}
                    color={filters.taskType.includes(type) ? "primary" : "default"}
                  />
                ))}
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            {/* Assignee Filter */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>{t('tasks.filters.assignee')}</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {/* Special options for unassigned and external assignees */}
                <Chip
                  key="unassigned"
                  label={t('tasks.filters.unassigned', 'Unassigned')}
                  onClick={() => {
                    const newAssignees = filters.assignee.includes('unassigned')
                      ? filters.assignee.filter(a => a !== 'unassigned')
                      : [...filters.assignee, 'unassigned'];
                    handleFilterChange('assignee', newAssignees);
                  }}
                  color={filters.assignee.includes('unassigned') ? "primary" : "default"}
                />
                <Chip
                  key="external"
                  label={t('tasks.filters.external', 'External (Non-User)')}
                  onClick={() => {
                    const newAssignees = filters.assignee.includes('external')
                      ? filters.assignee.filter(a => a !== 'external')
                      : [...filters.assignee, 'external'];
                    handleFilterChange('assignee', newAssignees);
                  }}
                  color={filters.assignee.includes('external') ? "secondary" : "default"}
                />

                {/* Regular user assignees */}
                {assigneeOptions.map(assignee => (
                  <Chip
                    key={assignee._id}
                    label={assignee.name}
                    onClick={() => {
                      const newAssignees = filters.assignee.includes(assignee._id)
                        ? filters.assignee.filter(a => a !== assignee._id)
                        : [...filters.assignee, assignee._id];
                      handleFilterChange('assignee', newAssignees);
                    }}
                    color={filters.assignee.includes(assignee._id) ? "primary" : "default"}
                  />
                ))}
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            {/* Due Date Filter */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>{t('tasks.filters.dueDate')}</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {[
                  { value: 'all', label: t('tasks.filters.dueDates.all') },
                  { value: 'today', label: t('tasks.filters.dueDates.today') },
                  { value: 'week', label: t('tasks.filters.dueDates.week') },
                  { value: 'month', label: t('tasks.filters.dueDates.month') },
                  { value: 'overdue', label: t('tasks.filters.dueDates.overdue') }
                ].map(option => (
                  <Chip
                    key={option.value}
                    label={option.label}
                    onClick={() => handleFilterChange('dueDate', option.value)}
                    color={filters.dueDate === option.value ? "primary" : "default"}
                  />
                ))}
              </Box>
            </Grid>

            <Grid item xs={12} sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button onClick={handleClearFilters} sx={{ mr: 1 }}>
                  {t('tasks.filters.clearAll')}
                </Button>
                <Button
                  variant="contained"
                  onClick={handleCloseFilterDialog}
                >
                  {t('tasks.filters.apply')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TaskManager;