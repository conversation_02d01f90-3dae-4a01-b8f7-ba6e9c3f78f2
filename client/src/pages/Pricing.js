import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Stack,
  useTheme
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
// import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline'; // Unused import
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { AuthContext } from '../contexts/AuthContext';

const PricingTier = ({
  title,
  price,
  description,
  features,
  buttonText,
  buttonVariant = 'contained',
  highlighted = false,
  buttonLink = '/register',
  planId,
  trialDays = 0,
  showTrialButton = false
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { user } = useContext(AuthContext);
  
  // Determine the button link based on plan and authentication status
  const getButtonLink = () => {
    if (planId === 'free') {
      return user ? '/' : '/register';
    }
    
    // For paid plans, go to checkout if logged in, otherwise register
    return user ? `/checkout?plan=${planId}` : '/register';
  };

  // Determine the trial button link
  const getTrialButtonLink = () => {
    return user ? `/checkout?plan=${planId}&trial=true` : '/register';
  };

  return (
    <Card
      elevation={highlighted ? 8 : 2}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        position: 'relative',
        overflow: 'visible',
        border: highlighted ? `2px solid ${theme.palette.primary.main}` : 'none',
        transition: 'transform 0.3s',
        '&:hover': {
          transform: 'translateY(-8px)'
        }
      }}
    >
      {highlighted && (
        <Box
          sx={{
            position: 'absolute',
            top: -15,
            left: '50%',
            transform: 'translateX(-50%)',
            bgcolor: 'primary.main',
            color: 'white',
            py: 0.5,
            px: 2,
            borderRadius: 1,
            fontWeight: 'bold',
            fontSize: '0.875rem',
            zIndex: 1
          }}
        >
          {t('pricing.mostPopular', 'MOST POPULAR')}
        </Box>
      )}
      <CardHeader
        title={title}
        titleTypographyProps={{ align: 'center', variant: 'h5', fontWeight: 'bold' }}
        sx={{
          bgcolor: highlighted ? 'primary.light' : 'grey.100',
          color: highlighted ? 'primary.contrastText' : 'text.primary',
          pb: 1
        }}
      />
      <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Typography component="h2" variant="h3" color="text.primary">
            ${price}
          </Typography>
          <Typography variant="h6" color="text.secondary">
            {t('pricing.perMonth', '/month')}
          </Typography>
        </Box>
        <Typography variant="subtitle1" align="center" sx={{ fontStyle: 'italic', mb: 2 }}>
          {description}
        </Typography>
        <Divider sx={{ my: 2 }} />
        <List sx={{ flexGrow: 1, mb: 2 }}>
          {features.map((feature, index) => (
            <ListItem key={index} sx={{ py: 0.5 }}>
              <ListItemIcon sx={{ minWidth: 36 }}>
                {feature.included ? (
                  <CheckCircleIcon color={feature.highlighted ? "primary" : "success"} />
                ) : (
                  <RemoveCircleOutlineIcon color="disabled" />
                )}
              </ListItemIcon>
              <ListItemText
                primary={feature.text}
                primaryTypographyProps={{
                  variant: 'body2',
                  fontWeight: feature.highlighted ? 'bold' : 'regular',
                  color: feature.highlighted ? 'primary.main' : 'text.primary'
                }}
              />
            </ListItem>
          ))}
        </List>
        <Box sx={{ mt: 'auto', pt: 2 }}>
          {showTrialButton ? (
            <Stack spacing={1}>
              <Button
                component={RouterLink}
                to={getTrialButtonLink()}
                fullWidth
                variant={buttonVariant}
                color={highlighted ? "primary" : "primary"}
                sx={{
                  py: 1.5,
                  fontWeight: 'bold',
                  ...(highlighted && {
                    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.25)',
                  })
                }}
              >
                {t('pricing.startTrial', 'Start {{days}}-Day Free Trial', { days: trialDays })}
              </Button>
              <Button
                component={RouterLink}
                to={getButtonLink()}
                fullWidth
                variant="outlined"
                color={highlighted ? "primary" : "primary"}
                sx={{
                  py: 1.5,
                }}
              >
                {buttonText}
              </Button>
            </Stack>
          ) : (
            <Button
              component={RouterLink}
              to={getButtonLink()}
              fullWidth
              variant={buttonVariant}
              color={highlighted ? "primary" : "primary"}
              sx={{
                py: 1.5,
                fontWeight: 'bold',
                ...(highlighted && {
                  boxShadow: '0 4px 10px rgba(0, 0, 0, 0.25)',
                })
              }}
            >
              {buttonText}
            </Button>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

const Pricing = () => {
  // const theme = useTheme(); // Unused variable
  const { t } = useTranslation();

  const tiers = [
    {
      title: t('pricing.tiers.free.title'),
      price: t('pricing.tiers.free.price'),
      description: t('pricing.tiers.free.description'),
      buttonText: t('pricing.tiers.free.buttonText'),
      buttonVariant: 'outlined',
      planId: 'free',
      features: [
        { text: t('pricing.features.free.events', 'Up to 3 events'), included: true },
        { text: t('pricing.features.free.guests', 'Up to 50 guests per event'), included: true },
        { text: t('pricing.features.free.taskManagement', 'Basic task management'), included: true },
        { text: t('pricing.features.free.venuePlanning', 'Simple venue planning'), included: true },
        { text: t('pricing.features.free.support', 'Email support'), included: true },
        { text: t('pricing.features.free.taskDependencies', 'Advanced task dependencies'), included: false },
        { text: t('pricing.features.free.teamCollaboration', 'Team collaboration'), included: false },
        { text: t('pricing.features.free.templates', 'Custom event templates'), included: false },
        { text: t('pricing.features.free.prioritySupport', 'Priority support'), included: false },
      ],
    },
    {
      title: t('pricing.tiers.pro.title'),
      price: t('pricing.tiers.pro.price'),
      description: t('pricing.tiers.pro.description'),
      buttonText: t('pricing.tiers.pro.buttonText'),
      buttonVariant: 'contained',
      highlighted: true,
      planId: 'pro',
      trialDays: 14,
      showTrialButton: true,
      features: [
        { text: t('pricing.features.pro.events', 'Unlimited events'), included: true, highlighted: true },
        { text: t('pricing.features.pro.guests', 'Up to 200 guests per event'), included: true },
        { text: t('pricing.features.pro.taskManagement', 'Advanced task management'), included: true },
        { text: t('pricing.features.pro.venuePlanning', 'Detailed venue planning'), included: true },
        { text: t('pricing.features.pro.support', 'Email & chat support'), included: true },
        { text: t('pricing.features.pro.taskDependencies', 'Task dependencies & timelines'), included: true, highlighted: true },
        { text: t('pricing.features.pro.teamCollaboration', 'Team collaboration (up to 5 users)'), included: true, highlighted: true },
        { text: t('pricing.features.pro.templates', 'Custom event templates'), included: true },
        { text: t('pricing.features.pro.prioritySupport', 'Priority support'), included: false },
      ],
    },
    {
      title: t('pricing.tiers.enterprise.title'),
      price: t('pricing.tiers.enterprise.price'),
      description: t('pricing.tiers.enterprise.description'),
      buttonText: t('pricing.tiers.enterprise.buttonText'),
      buttonVariant: 'contained',
      planId: 'enterprise',
      features: [
        { text: t('pricing.features.enterprise.events', 'Unlimited events'), included: true },
        { text: t('pricing.features.enterprise.guests', 'Unlimited guests'), included: true, highlighted: true },
        { text: t('pricing.features.enterprise.taskManagement', 'Premium task management'), included: true },
        { text: t('pricing.features.enterprise.venuePlanning', 'Advanced venue planning'), included: true },
        { text: t('pricing.features.enterprise.support', 'Priority support'), included: true, highlighted: true },
        { text: t('pricing.features.enterprise.taskDependencies', 'Advanced task dependencies'), included: true },
        { text: t('pricing.features.enterprise.teamCollaboration', 'Unlimited team collaboration'), included: true, highlighted: true },
        { text: t('pricing.features.enterprise.branding', 'Custom branding'), included: true, highlighted: true },
        { text: t('pricing.features.enterprise.api', 'API access'), included: true, highlighted: true },
      ],
    },
  ];

  return (
    <Box sx={{ flexGrow: 1, padding: 0, marginTop: 0, width: '100%', mt: 0 }}>
      {/* Header */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #6200ea 0%, #03dac6 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center'
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
            {t('pricing.title')}
          </Typography>
          <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 2, maxWidth: '800px', mx: 'auto' }}>
            {t('pricing.subtitle')}
          </Typography>
        </Container>
      </Box>

      {/* Pricing Tiers */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Grid container spacing={4} alignItems="flex-start">
          {tiers.map((tier) => (
            <Grid
              item
              key={tier.title}
              xs={12}
              sm={tier.title === 'Enterprise' ? 12 : 6}
              md={4}
            >
              <PricingTier
                title={tier.title}
                price={tier.price}
                description={tier.description}
                features={tier.features}
                buttonText={tier.buttonText}
                buttonVariant={tier.buttonVariant}
                highlighted={tier.highlighted}
                planId={tier.planId}
                trialDays={tier.trialDays}
                showTrialButton={tier.showTrialButton}
              />
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Feature Comparison */}
      <Box sx={{ bgcolor: 'grey.100', py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom fontWeight="bold" color="primary">
            {t('pricing.features.title', 'Compare Features')}
          </Typography>
          <Typography variant="h6" textAlign="center" color="text.secondary" paragraph sx={{ mb: 6 }}>
            {t('pricing.features.subtitle', 'Find the plan that includes all the features you need')}
          </Typography>

          <Paper sx={{ overflow: 'hidden', borderRadius: 2 }}>
            <Box sx={{ minWidth: 700, overflowX: 'auto' }}>
              <Box sx={{ width: '100%', display: 'table', borderCollapse: 'collapse' }}>
                {/* Header Row */}
                <Box sx={{ display: 'table-row', bgcolor: 'grey.100' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', width: '40%' }}>
                    <Typography variant="subtitle1" fontWeight="bold">{t('pricing.features.feature', 'Feature')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="subtitle1" fontWeight="bold">{t('pricing.tiers.free.title')}</Typography>
                  </Box>
                  <Box sx={{
                    display: 'table-cell',
                    p: 2,
                    borderBottom: '1px solid',
                    borderColor: 'divider',
                    textAlign: 'center',
                    bgcolor: 'primary.light',
                    color: 'primary.contrastText'
                  }}>
                    <Typography variant="subtitle1" fontWeight="bold">{t('pricing.tiers.pro.title')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="subtitle1" fontWeight="bold">{t('pricing.tiers.enterprise.title')}</Typography>
                  </Box>
                </Box>

                {/* Event Management */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.features.events')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.free.events', 'Up to 3')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.unlimited', 'Unlimited')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.unlimited', 'Unlimited')}</Typography>
                  </Box>
                </Box>

                {/* Guest Management */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.comparison.guestsPerEvent', 'Guests per Event')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.free.guests', 'Up to 50')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.pro.guests', 'Up to 200')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.unlimited', 'Unlimited')}</Typography>
                  </Box>
                </Box>

                {/* Task Management */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.comparison.taskManagement', 'Task Management')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.basic', 'Basic')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.advanced', 'Advanced')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.premium', 'Premium')}</Typography>
                  </Box>
                </Box>

                {/* Task Dependencies */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.comparison.taskDependencies', 'Task Dependencies')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <RemoveCircleOutlineIcon color="disabled" fontSize="small" />
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <CheckCircleIcon color="success" fontSize="small" />
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <CheckCircleIcon color="success" fontSize="small" />
                  </Box>
                </Box>

                {/* Team Collaboration */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.comparison.teamCollaboration', 'Team Collaboration')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <RemoveCircleOutlineIcon color="disabled" fontSize="small" />
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.pro.teamMembers', 'Up to 5 users')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.unlimited', 'Unlimited')}</Typography>
                  </Box>
                </Box>

                {/* Venue Planning */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.comparison.venuePlanning', 'Venue Planning')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.basic', 'Basic')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.advanced', 'Advanced')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.premium', 'Premium')}</Typography>
                  </Box>
                </Box>

                {/* Custom Templates */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.comparison.customTemplates', 'Custom Templates')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <RemoveCircleOutlineIcon color="disabled" fontSize="small" />
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <CheckCircleIcon color="success" fontSize="small" />
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <CheckCircleIcon color="success" fontSize="small" />
                  </Box>
                </Box>

                {/* Support */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.comparison.support', 'Support')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.free.support', 'Email')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.pro.support', 'Email & Chat')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <Typography variant="body2">{t('pricing.comparison.enterprise.support', 'Priority')}</Typography>
                  </Box>
                </Box>

                {/* API Access */}
                <Box sx={{ display: 'table-row' }}>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">{t('pricing.comparison.apiAccess', 'API Access')}</Typography>
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <RemoveCircleOutlineIcon color="disabled" fontSize="small" />
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <RemoveCircleOutlineIcon color="disabled" fontSize="small" />
                  </Box>
                  <Box sx={{ display: 'table-cell', p: 2, borderBottom: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
                    <CheckCircleIcon color="success" fontSize="small" />
                  </Box>
                </Box>
              </Box>
            </Box>
          </Paper>
        </Container>
      </Box>

      {/* FAQ Section */}
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom fontWeight="bold" color="primary">
          {t('pricing.faq.title', 'Frequently Asked Questions')}
        </Typography>
        <Typography variant="h6" textAlign="center" color="text.secondary" paragraph sx={{ mb: 6 }}>
          {t('pricing.faq.subtitle', 'Have questions about our pricing? Find answers below')}
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom color="primary">
                {t('pricing.faq.questions.q1.question', 'Can I upgrade or downgrade my plan?')}
              </Typography>
              <Typography variant="body1">
                {t('pricing.faq.questions.q1.answer', 'Yes, you can change your plan at any time. When upgrading, you\'ll be charged the prorated difference for the remainder of your billing cycle. When downgrading, the new rate will apply at the start of your next billing cycle.')}
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom color="primary">
                {t('pricing.faq.questions.q2.question', 'Is there a free trial?')}
              </Typography>
              <Typography variant="body1">
                {t('pricing.faq.questions.q2.answer', 'Yes! You can use the Free plan indefinitely with its limitations, or try the Pro plan free for 14 days. No credit card required for the trial.')}
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom color="primary">
                {t('pricing.faq.questions.q3.question', 'What payment methods do you accept?')}
              </Typography>
              <Typography variant="body1">
                {t('pricing.faq.questions.q3.answer', 'We accept all major credit cards, including Visa, Mastercard, American Express, and Discover. For Enterprise plans, we also offer invoicing options.')}
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom color="primary">
                {t('pricing.faq.questions.q4.question', 'Do you offer discounts for non-profits?')}
              </Typography>
              <Typography variant="body1">
                {t('pricing.faq.questions.q4.answer', 'Yes, we offer special pricing for non-profit organizations. Please contact our sales team for more information and to verify your non-profit status.')}
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      {/* Final CTA */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #03dac6 0%, #6200ea 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center'
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" component="h2" gutterBottom fontWeight="bold">
            {t('pricing.finalCta.title', 'Ready to Get Started?')}
          </Typography>
          <Typography variant="h6" paragraph sx={{ mb: 4 }}>
            {t('pricing.finalCta.subtitle', 'Choose the plan that\'s right for you and start planning amazing events today.')}
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
            <Button
              variant="contained"
              size="large"
              component={RouterLink}
              to="/register"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                fontWeight: 'bold',
                px: 4,
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.9)',
                }
              }}
            >
              {t('pricing.finalCta.signUp', 'Sign Up Now')}
            </Button>
            <Button
              variant="outlined"
              size="large"
              component={RouterLink}
              to="/contact"
              sx={{
                color: 'white',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                }
              }}
            >
              {t('pricing.finalCta.contactSales', 'Contact Sales')}
            </Button>
          </Stack>
        </Container>
      </Box>
    </Box>
  );
};

export default Pricing;
