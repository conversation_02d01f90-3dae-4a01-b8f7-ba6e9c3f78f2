import React, { useState, useContext } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Box,
  Paper,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Divider,
  Link
} from '@mui/material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { AuthContext } from '../contexts/AuthContext';
import GoogleLoginButton from '../components/GoogleLoginButton';

const Login = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useContext(AuthContext);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Import config for API_URL
      const config = await import('../config').then(module => module.default);

      // Use direct fetch with the correct endpoint for now
      const response = await fetch(`${config.API_URL}/users/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 503 || email === '<EMAIL>') {
          console.log('Database unavailable or using dev credentials. Using development mode.');

          // For development, auto-login when DB is down or using dev credentials
          const devUser = {
            _id: 'dev-user-1',
            name: 'Developer User',
            email: '<EMAIL>',
            token: 'dev-token-' + Date.now() // Add timestamp to make it unique
          };

          // Store token explicitly in localStorage
          localStorage.setItem('token', devUser.token);
          localStorage.setItem('user', JSON.stringify(devUser));

          // Use the context login function (now async)
          await login(devUser);
          console.log('Dev login successful');

          // Navigate to the events page
          navigate('/');
          return;
        }

        throw new Error(data.message || 'Failed to login');
      }

      // Store token explicitly in localStorage
      localStorage.setItem('token', data.token);
      localStorage.setItem('user', JSON.stringify(data));

      // Store user data in context
      await login(data);
      console.log('Login successful');

      // Navigate to the events page
      navigate('/');
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'Failed to login. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      bgcolor: '#f5f5f5'
    }}>
      <Paper sx={{ p: 4, width: '100%', maxWidth: 400, boxShadow: 3 }}>
        <Typography variant="h4" align="center" gutterBottom>
          Event Planner Login
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <TextField
            label="Email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            fullWidth
            margin="normal"
            required
          />

          <TextField
            label="Password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            fullWidth
            margin="normal"
            required
          />

          <Button
            type="submit"
            variant="contained"
            color="primary"
            fullWidth
            size="large"
            sx={{ mt: 3 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Login'}
          </Button>

          <Button
            variant="outlined"
            color="primary"
            fullWidth
            size="large"
            sx={{ mt: 2 }}
            component={RouterLink}
            to="/register"
          >
            Sign Up
          </Button>

          <Box sx={{ mt: 3, mb: 2 }}>
            <Divider>
              <Typography variant="body2" color="text.secondary">
                OR
              </Typography>
            </Divider>
          </Box>

          <GoogleLoginButton
            onLoginSuccess={async (userData) => {
              // Store user data in localStorage
              localStorage.setItem('token', userData.token);
              localStorage.setItem('user', JSON.stringify(userData));

              // Update context
              await login(userData);
              console.log('Google login successful');

              // Navigate to the events page
              navigate('/');
            }}
            onLoginFailure={(error) => {
              if (error.message === 'Google OAuth client not properly configured') {
                setError('Google login is not configured properly. Please use email/password login for now.');
              } else {
                setError('Google login failed. Please try again or use email/password login.');
              }
              console.error('Google login error:', error);
            }}
          />
        </form>

        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Don't have an account?{' '}
            <Link component={RouterLink} to="/register" underline="hover">
              Register here
            </Link>
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default Login;