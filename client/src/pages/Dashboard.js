import React, { useState, useEffect, useContext } from 'react';
import { Typography, Box, Divider, Paper } from '@mui/material';
import PendingInvitations from '../components/PendingInvitations';
import { fetchEvents } from '../services/eventService';
import { AuthContext } from '../contexts/AuthContext';

const Dashboard = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    if (user) {
      loadEvents();
    } else {
      setEvents([]);
    }
  }, [user]); // Re-fetch events when user changes

  const loadEvents = async () => {
    try {
      setLoading(true);
      const data = await fetchEvents();
      setEvents(data);
    } catch (error) {
      console.error('Error loading events:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInvitationResponse = () => {
    // Reload events after responding to an invitation
    loadEvents();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      {/* Pending Invitations Section */}
      <PendingInvitations onInvitationResponse={handleInvitationResponse} />
      
      <Divider sx={{ my: 3 }} />
      
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          Your Events Overview
        </Typography>
        
        <Typography variant="body1">
          {events.length > 0 
            ? `You have ${events.length} events scheduled.` 
            : 'You don\'t have any events scheduled yet.'}
        </Typography>
        
        {/* More dashboard content can be added here */}
      </Paper>
    </Box>
  );
};

export default Dashboard; 