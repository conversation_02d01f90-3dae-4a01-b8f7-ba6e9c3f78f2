{"name": "event-planner-client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.12.0", "@mui/x-date-pickers": "^6.2.0", "@react-oauth/google": "^0.12.1", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.1.0", "axios": "^1.3.5", "date-fns": "^2.29.3", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "moment": "^2.29.4", "react": "^18.2.0", "react-big-calendar": "^1.6.9", "react-dom": "^18.2.0", "react-i18next": "^15.4.1", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "reactflow": "^11.7.0", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:ui-only": "playwright test task-list/task-list-ui-only.spec.js", "test:e2e:simple": "playwright test task-list/simple-test.spec.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@playwright/test": "^1.52.0"}}