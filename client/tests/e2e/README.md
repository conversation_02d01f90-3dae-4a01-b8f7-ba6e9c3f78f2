# End-to-End Tests with <PERSON><PERSON>

This directory contains end-to-end tests for the Event Planner application using Playwright.

## Test Structure

- `task-list/`: Tests for the task list view
  - `simple-test.spec.js`: Basic test to verify the login page loads
  - `minimal-test.spec.js`: Minimal test to check login page and attempt login
  - `login-debug.spec.js`: Debug test to investigate login issues
  - `task-list-ui.spec.js`: Test for basic UI components without requiring login
  - `task-list-final.spec.js`: Comprehensive UI tests that work without API access
  - `task-list-mock.spec.js`: Tests with API mocking (requires configuration)
  - `task-list-basic.spec.js`: Full tests (requires working API)
  - `task-list-filtering.spec.js`: Tests for filtering and sorting tasks (requires working API)
  - `task-list-subtasks.spec.js`: Tests for subtask functionality (requires working API)
  - `task-list-actions.spec.js`: Tests for various actions and edge cases (requires working API)

- `utils/`: Utility functions and helpers
  - `auth.js`: Authentication utilities
  - `task-list-page.js`: Page Object Model for the task list page
  - `test-data.js`: Test data generation and management

## Running Tests

To run all tests:

```bash
npm run test:e2e
```

To run tests with UI mode:

```bash
npm run test:e2e:ui
```

To run tests in debug mode:

```bash
npm run test:e2e:debug
```

To view the test report:

```bash
npm run test:e2e:report
```

To run a specific test file:

```bash
npx playwright test task-list/task-list-ui.spec.js
```

## Current Status

The tests are currently configured to work without requiring API access. This is because the API server at `http://iehha01.zapto.org:5000` is not accessible, resulting in login failures.

### Working Tests

The following tests work without requiring API access:

- `simple-test.spec.js`: Verifies that the login page loads correctly
- `task-list-ui.spec.js`: Tests basic UI components without requiring login
- `task-list-final.spec.js`: Tests task list UI components without requiring login

### Tests Requiring Configuration

The following tests require configuration to work:

- `task-list-mock.spec.js`: Requires proper API mocking configuration
- `task-list-basic.spec.js`: Requires a working API server
- `task-list-filtering.spec.js`: Requires a working API server
- `task-list-subtasks.spec.js`: Requires a working API server
- `task-list-actions.spec.js`: Requires a working API server

## Fixing the Tests

To make all tests work, you need to:

1. **Update API URL**: Change the API URL in the application to point to a working server
2. **Configure API Mocking**: Update the mock responses in `task-list-mock.spec.js`
3. **Update Authentication**: Ensure the login credentials match those in your development environment

## Test Coverage

The task list tests cover the following functionality:

1. **Basic Functionality**
   - Displaying the task list
   - Creating new tasks
   - Editing existing tasks
   - Deleting tasks
   - Pagination

2. **Filtering and Sorting**
   - Filtering by task type
   - Filtering by task status
   - Filtering by multiple criteria
   - Clearing filters
   - Sorting tasks

3. **Subtask Functionality**
   - Displaying parent tasks with subtask indicators
   - Displaying subtasks when parent task is expanded
   - Editing subtasks
   - Adding new subtasks
   - Removing subtasks

4. **Actions and Edge Cases**
   - Handling tasks with long names
   - Handling tasks with long descriptions
   - Handling tasks with special characters
   - Canceling task creation
   - Canceling task deletion
   - Validating required fields
   - Showing empty state when no tasks match filters
   - Updating task status directly from the list

## Adding New Tests

To add new tests:

1. Create a new test file in the appropriate directory
2. Import the necessary utilities and page objects
3. Use the Page Object Model pattern to interact with the UI
4. Follow the existing test structure for consistency

## Best Practices

- Use the Page Object Model pattern to encapsulate UI interactions
- Create test data programmatically using the API when possible
- Clean up test data after tests complete
- Use descriptive test names
- Keep tests independent and isolated
- Avoid hardcoding selectors in test files
