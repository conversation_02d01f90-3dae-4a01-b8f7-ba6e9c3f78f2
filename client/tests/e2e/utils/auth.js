/**
 * Authentication utilities for Playwright tests
 */

/**
 * Login to the application
 * @param {import('@playwright/test').Page} page - Playwright page
 * @param {string} email - User email
 * @param {string} password - User password
 */
async function login(page, email = '<EMAIL>', password = 'password123') {
  await page.goto('/login');
  await page.waitForLoadState('networkidle');
  
  await page.fill('input[type="email"]', email);
  await page.fill('input[type="password"]', password);
  await page.click('button[type="submit"]');
  
  // Wait for navigation to complete after login
  await page.waitForURL('**/events');
}

/**
 * Create a test account if needed and login
 * @param {import('@playwright/test').Page} page - Playwright page
 * @param {string} email - User email
 * @param {string} password - User password
 */
async function setupTestAccount(page, email = '<EMAIL>', password = 'password123') {
  await page.goto('/signup');
  
  try {
    // Try to create an account (might already exist)
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[type="email"]', email);
    await page.fill('input[type="password"]', password);
    await page.fill('input[name="confirmPassword"]', password);
    await page.click('button[type="submit"]');
    
    // Wait for either navigation to events page or error message
    await Promise.race([
      page.waitForURL('**/events', { timeout: 5000 }),
      page.waitForSelector('div[role="alert"]', { timeout: 5000 })
    ]);
  } catch (error) {
    // If account creation fails, try to login
    console.log('Account might already exist, trying to login');
  }
  
  // If we're not on the events page, try to login
  if (!page.url().includes('/events')) {
    await login(page, email, password);
  }
}

/**
 * Select an event from the dropdown
 * @param {import('@playwright/test').Page} page - Playwright page
 * @param {string} eventName - Name of the event to select
 */
async function selectEvent(page, eventName) {
  // Click on the event selector dropdown
  await page.click('button[aria-label="Select Event"]');
  
  // Wait for dropdown to appear and select the event
  await page.waitForSelector('ul[role="listbox"]');
  await page.click(`li:has-text("${eventName}")`);
  
  // Wait for the event to be loaded
  await page.waitForLoadState('networkidle');
}

/**
 * Navigate to the task list view
 * @param {import('@playwright/test').Page} page - Playwright page
 */
async function navigateToTaskList(page) {
  // If we're not already on the tasks page, navigate to it
  if (!page.url().includes('/tasks')) {
    await page.click('a[href*="/tasks"]');
    await page.waitForURL('**/tasks');
  }
  
  // Make sure we're on the list view tab
  await page.click('button:has-text("List")');
  await page.waitForSelector('table');
}

module.exports = {
  login,
  setupTestAccount,
  selectEvent,
  navigateToTaskList
};
