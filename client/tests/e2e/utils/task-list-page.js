/**
 * Page Object Model for the Task List page
 */
class TaskListPage {
  /**
   * @param {import('@playwright/test').Page} page - Playwright page
   */
  constructor(page) {
    this.page = page;
    
    // Selectors
    this.taskTable = 'table';
    this.taskRows = 'table tbody tr:nth-child(odd)'; // Only the main rows, not the expanded rows
    this.taskNameCell = (taskName) => `table tbody tr td:has-text("${taskName}")`;
    this.expandTaskButton = (taskName) => `table tbody tr:has-text("${taskName}") td:first-child button`;
    this.editTaskButton = (taskName) => `table tbody tr:has-text("${taskName}") td:last-child button:has(svg[data-testid="EditIcon"])`;
    this.deleteTaskButton = (taskName) => `table tbody tr:has-text("${taskName}") td:last-child button:has(svg[data-testid="DeleteIcon"])`;
    this.expandedTaskDetails = (taskName) => `table tbody tr:has-text("${taskName}") + tr div`;
    this.paginationControls = 'div[class*="MuiTablePagination"]';
    this.rowsPerPageSelect = 'div[class*="MuiTablePagination"] select';
    this.nextPageButton = 'button[aria-label="Go to next page"]';
    this.previousPageButton = 'button[aria-label="Go to previous page"]';
    this.emptyStateMessage = 'div[class*="MuiPaper"] h6:has-text("No tasks")';
    this.taskTypeFilter = 'input[name="taskType"]';
    this.taskStatusFilter = 'input[name="status"]';
    this.createTaskButton = 'button:has-text("Create Task")';
    this.taskDialog = 'div[role="dialog"]';
    this.taskNameInput = 'input[name="name"]';
    this.taskTypeSelect = 'input[name="taskType"]';
    this.taskStatusSelect = 'input[name="status"]';
    this.taskDetailsInput = 'textarea[name="details"]';
    this.saveTaskButton = 'div[role="dialog"] button:has-text("Save")';
    this.cancelTaskButton = 'div[role="dialog"] button:has-text("Cancel")';
    this.deleteConfirmDialog = 'div[role="dialog"]:has-text("Delete Task")';
    this.confirmDeleteButton = 'div[role="dialog"] button:has-text("Delete")';
    this.cancelDeleteButton = 'div[role="dialog"] button:has-text("Cancel")';
    this.snackbar = 'div[role="alert"]';
  }

  /**
   * Navigate to the task list page
   */
  async navigate() {
    await this.page.goto('/tasks');
    await this.page.waitForSelector(this.taskTable);
  }

  /**
   * Get all task rows
   * @returns {Promise<Array<import('@playwright/test').ElementHandle>>} Task rows
   */
  async getTaskRows() {
    return await this.page.$$(this.taskRows);
  }

  /**
   * Get the number of tasks in the list
   * @returns {Promise<number>} Number of tasks
   */
  async getTaskCount() {
    const rows = await this.getTaskRows();
    return rows.length;
  }

  /**
   * Check if a task exists in the list
   * @param {string} taskName - Name of the task to check
   * @returns {Promise<boolean>} Whether the task exists
   */
  async taskExists(taskName) {
    return await this.page.isVisible(this.taskNameCell(taskName));
  }

  /**
   * Expand a task to show details
   * @param {string} taskName - Name of the task to expand
   */
  async expandTask(taskName) {
    await this.page.click(this.expandTaskButton(taskName));
    await this.page.waitForSelector(this.expandedTaskDetails(taskName));
  }

  /**
   * Get task details
   * @param {string} taskName - Name of the task to get details for
   * @returns {Promise<string>} Task details text
   */
  async getTaskDetails(taskName) {
    await this.expandTask(taskName);
    const detailsElement = await this.page.$(this.expandedTaskDetails(taskName));
    return await detailsElement.textContent();
  }

  /**
   * Click the edit button for a task
   * @param {string} taskName - Name of the task to edit
   */
  async clickEditTask(taskName) {
    await this.page.click(this.editTaskButton(taskName));
    await this.page.waitForSelector(this.taskDialog);
  }

  /**
   * Click the delete button for a task
   * @param {string} taskName - Name of the task to delete
   */
  async clickDeleteTask(taskName) {
    await this.page.click(this.deleteTaskButton(taskName));
    await this.page.waitForSelector(this.deleteConfirmDialog);
  }

  /**
   * Confirm task deletion
   */
  async confirmDeleteTask() {
    await this.page.click(this.confirmDeleteButton);
    await this.page.waitForSelector(this.snackbar);
  }

  /**
   * Cancel task deletion
   */
  async cancelDeleteTask() {
    await this.page.click(this.cancelDeleteButton);
    await this.page.waitForSelector(this.taskTable);
  }

  /**
   * Click the create task button
   */
  async clickCreateTask() {
    await this.page.click(this.createTaskButton);
    await this.page.waitForSelector(this.taskDialog);
  }

  /**
   * Fill the task form
   * @param {Object} taskData - Task data
   * @param {string} taskData.name - Task name
   * @param {string} taskData.taskType - Task type
   * @param {string} taskData.status - Task status
   * @param {string} taskData.details - Task details
   */
  async fillTaskForm(taskData) {
    if (taskData.name) {
      await this.page.fill(this.taskNameInput, taskData.name);
    }
    
    if (taskData.taskType) {
      await this.page.click(this.taskTypeSelect);
      await this.page.click(`li:has-text("${taskData.taskType}")`);
    }
    
    if (taskData.status) {
      await this.page.click(this.taskStatusSelect);
      await this.page.click(`li:has-text("${taskData.status}")`);
    }
    
    if (taskData.details) {
      await this.page.fill(this.taskDetailsInput, taskData.details);
    }
  }

  /**
   * Save the task form
   */
  async saveTaskForm() {
    await this.page.click(this.saveTaskButton);
    await this.page.waitForSelector(this.snackbar);
  }

  /**
   * Cancel the task form
   */
  async cancelTaskForm() {
    await this.page.click(this.cancelTaskButton);
    await this.page.waitForSelector(this.taskTable);
  }

  /**
   * Create a new task
   * @param {Object} taskData - Task data
   * @param {string} taskData.name - Task name
   * @param {string} taskData.taskType - Task type
   * @param {string} taskData.status - Task status
   * @param {string} taskData.details - Task details
   */
  async createTask(taskData) {
    await this.clickCreateTask();
    await this.fillTaskForm(taskData);
    await this.saveTaskForm();
  }

  /**
   * Edit a task
   * @param {string} taskName - Name of the task to edit
   * @param {Object} taskData - Task data
   * @param {string} taskData.name - Task name
   * @param {string} taskData.taskType - Task type
   * @param {string} taskData.status - Task status
   * @param {string} taskData.details - Task details
   */
  async editTask(taskName, taskData) {
    await this.clickEditTask(taskName);
    await this.fillTaskForm(taskData);
    await this.saveTaskForm();
  }

  /**
   * Delete a task
   * @param {string} taskName - Name of the task to delete
   */
  async deleteTask(taskName) {
    await this.clickDeleteTask(taskName);
    await this.confirmDeleteTask();
  }

  /**
   * Change rows per page
   * @param {number} rowsPerPage - Number of rows per page
   */
  async changeRowsPerPage(rowsPerPage) {
    await this.page.selectOption(`${this.rowsPerPageSelect}`, rowsPerPage.toString());
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Go to next page
   */
  async goToNextPage() {
    await this.page.click(this.nextPageButton);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Go to previous page
   */
  async goToPreviousPage() {
    await this.page.click(this.previousPageButton);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Check if the task list is empty
   * @returns {Promise<boolean>} Whether the task list is empty
   */
  async isTaskListEmpty() {
    return await this.page.isVisible(this.emptyStateMessage);
  }
}

module.exports = { TaskListPage };
