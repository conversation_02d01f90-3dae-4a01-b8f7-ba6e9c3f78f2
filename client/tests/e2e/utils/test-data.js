/**
 * Test data utilities for Playwright tests
 */

/**
 * Generate a random task name
 * @returns {string} Random task name
 */
function generateTaskName() {
  const prefix = 'Test Task';
  const randomId = Math.floor(Math.random() * 10000);
  return `${prefix} ${randomId}`;
}

/**
 * Generate a random event name
 * @returns {string} Random event name
 */
function generateEventName() {
  const prefix = 'Test Event';
  const randomId = Math.floor(Math.random() * 10000);
  return `${prefix} ${randomId}`;
}

/**
 * Create a test event via API
 * @param {import('@playwright/test').APIRequestContext} request - Playwright API request context
 * @param {string} token - Authentication token
 * @param {Object} eventData - Event data (optional)
 * @returns {Promise<Object>} Created event
 */
async function createTestEvent(request, token, eventData = {}) {
  const defaultEventData = {
    title: generateEventName(),
    date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
    location: 'Test Location',
    description: 'Test event created by <PERSON>wright',
    eventType: 'Wedding',
    budget: 10000,
    currency: 'USD',
    isPublic: false
  };
  
  const response = await request.post('/api/events', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    data: { ...defaultEventData, ...eventData }
  });
  
  return await response.json();
}

/**
 * Create a test task via API
 * @param {import('@playwright/test').APIRequestContext} request - Playwright API request context
 * @param {string} token - Authentication token
 * @param {string} eventId - Event ID
 * @param {Object} taskData - Task data (optional)
 * @returns {Promise<Object>} Created task
 */
async function createTestTask(request, token, eventId, taskData = {}) {
  const defaultTaskData = {
    name: generateTaskName(),
    taskType: 'Other',
    details: 'Test task created by Playwright',
    status: 'Not Started',
    event: eventId,
    cost: {
      amount: 100,
      currency: 'USD',
      isPaid: false
    },
    assignees: []
  };
  
  const response = await request.post('/api/tasks', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    data: { ...defaultTaskData, ...taskData }
  });
  
  return await response.json();
}

/**
 * Create multiple test tasks via API
 * @param {import('@playwright/test').APIRequestContext} request - Playwright API request context
 * @param {string} token - Authentication token
 * @param {string} eventId - Event ID
 * @param {number} count - Number of tasks to create
 * @returns {Promise<Array<Object>>} Created tasks
 */
async function createMultipleTestTasks(request, token, eventId, count = 5) {
  const tasks = [];
  
  for (let i = 0; i < count; i++) {
    const task = await createTestTask(request, token, eventId, {
      name: `Test Task ${i + 1}`,
      status: i % 5 === 0 ? 'Completed' : 
              i % 5 === 1 ? 'In Progress' : 
              i % 5 === 2 ? 'Delayed' : 
              i % 5 === 3 ? 'Cancelled' : 'Not Started',
      taskType: i % 7 === 0 ? 'Venue' :
                i % 7 === 1 ? 'Catering' :
                i % 7 === 2 ? 'Photography' :
                i % 7 === 3 ? 'Decoration' :
                i % 7 === 4 ? 'Entertainment' :
                i % 7 === 5 ? 'Logistics' : 'Other'
    });
    tasks.push(task);
  }
  
  return tasks;
}

/**
 * Clean up test data
 * @param {import('@playwright/test').APIRequestContext} request - Playwright API request context
 * @param {string} token - Authentication token
 * @param {Array<string>} eventIds - Event IDs to delete
 * @param {Array<string>} taskIds - Task IDs to delete
 */
async function cleanupTestData(request, token, eventIds = [], taskIds = []) {
  // Delete tasks first
  for (const taskId of taskIds) {
    try {
      await request.delete(`/api/tasks/${taskId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
    } catch (error) {
      console.warn(`Failed to delete task ${taskId}:`, error);
    }
  }
  
  // Then delete events
  for (const eventId of eventIds) {
    try {
      await request.delete(`/api/events/${eventId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
    } catch (error) {
      console.warn(`Failed to delete event ${eventId}:`, error);
    }
  }
}

module.exports = {
  generateTaskName,
  generateEventName,
  createTestEvent,
  createTestTask,
  createMultipleTestTasks,
  cleanupTestData
};
