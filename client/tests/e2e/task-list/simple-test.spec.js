const { test, expect } = require('@playwright/test');

test.describe('Simple Task List Test', () => {
  test('should load the login page', async ({ page }) => {
    // Navigate to the login page
    await page.goto('/login');
    
    // Check that the login form is displayed
    await expect(page.locator('form')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });
});
