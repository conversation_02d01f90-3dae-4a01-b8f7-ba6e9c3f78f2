const { test, expect } = require('@playwright/test');
const { login, setupTestAccount, selectEvent, navigateToTaskList } = require('../utils/auth');
const { createTestEvent, createTestTask, cleanupTestData, generateTaskName } = require('../utils/test-data');
const { TaskListPage } = require('../utils/task-list-page');

test.describe('Task List Actions and Edge Cases', () => {
  let page;
  let taskListPage;
  let testEvent;
  let testTasks = [];
  let authToken;

  test.beforeAll(async ({ browser, request }) => {
    // Create a context for API requests
    const context = await browser.newContext();
    page = await context.newPage();
    
    // Setup test account and login
    await setupTestAccount(page);
    
    // Get auth token from localStorage
    authToken = await page.evaluate(() => localStorage.getItem('token'));
    
    // Create test event
    testEvent = await createTestEvent(request, authToken);
    
    // Create a few test tasks
    testTasks.push(await createTestTask(request, authToken, testEvent._id, {
      name: 'Task with Long Name That Should Be Truncated in the UI',
      taskType: 'Other',
      details: 'This task has a very long name that should be truncated in the UI'
    }));
    
    testTasks.push(await createTestTask(request, authToken, testEvent._id, {
      name: 'Task with Long Description',
      taskType: 'Other',
      details: 'This task has a very long description that should be truncated in the UI. '.repeat(10)
    }));
    
    testTasks.push(await createTestTask(request, authToken, testEvent._id, {
      name: 'Task with Special Characters',
      taskType: 'Other',
      details: 'This task has special characters: !@#$%^&*()_+{}|:"<>?~`-=[]\\;\',./€£¥©®™℠'
    }));
  });

  test.beforeEach(async () => {
    // Navigate to task list for the test event
    await page.goto('/events');
    await selectEvent(page, testEvent.title);
    await navigateToTaskList(page);
    
    // Initialize page object
    taskListPage = new TaskListPage(page);
  });

  test.afterAll(async ({ request }) => {
    // Clean up test data
    const taskIds = testTasks.map(task => task._id);
    await cleanupTestData(request, authToken, [testEvent._id], taskIds);
  });

  test('should handle tasks with long names', async () => {
    // Check that the task with long name is displayed
    const longNameTask = testTasks[0];
    await expect(page.locator(`table tbody tr:has-text("${longNameTask.name.substring(0, 20)}")`)).toBeVisible();
    
    // Expand the task to see the full name
    await taskListPage.expandTask(longNameTask.name);
    
    // Check that the full name is displayed in the details
    const detailsText = await taskListPage.getTaskDetails(longNameTask.name);
    expect(detailsText).toContain(longNameTask.name);
  });

  test('should handle tasks with long descriptions', async () => {
    // Check that the task with long description is displayed
    const longDescTask = testTasks[1];
    await expect(page.locator(`table tbody tr:has-text("${longDescTask.name}")`)).toBeVisible();
    
    // Expand the task to see the full description
    await taskListPage.expandTask(longDescTask.name);
    
    // Check that the full description is displayed in the details
    const detailsText = await taskListPage.getTaskDetails(longDescTask.name);
    expect(detailsText).toContain(longDescTask.details);
  });

  test('should handle tasks with special characters', async () => {
    // Check that the task with special characters is displayed
    const specialCharsTask = testTasks[2];
    await expect(page.locator(`table tbody tr:has-text("${specialCharsTask.name}")`)).toBeVisible();
    
    // Expand the task to see the details with special characters
    await taskListPage.expandTask(specialCharsTask.name);
    
    // Check that the details with special characters are displayed correctly
    const detailsText = await taskListPage.getTaskDetails(specialCharsTask.name);
    expect(detailsText).toContain(specialCharsTask.details);
  });

  test('should cancel task creation', async () => {
    // Click the create task button
    await taskListPage.clickCreateTask();
    
    // Fill in the task name
    const newTaskName = generateTaskName();
    await page.fill('input[name="name"]', newTaskName);
    
    // Cancel the task creation
    await taskListPage.cancelTaskForm();
    
    // Check that the task was not created
    await expect(page.locator(`table tbody tr:has-text("${newTaskName}")`)).not.toBeVisible();
  });

  test('should cancel task deletion', async () => {
    // Get the first task
    const taskToNotDelete = testTasks[0];
    
    // Click the delete button
    await taskListPage.clickDeleteTask(taskToNotDelete.name);
    
    // Cancel the deletion
    await taskListPage.cancelDeleteTask();
    
    // Check that the task was not deleted
    await expect(page.locator(`table tbody tr:has-text("${taskToNotDelete.name}")`)).toBeVisible();
  });

  test('should validate required fields when creating a task', async () => {
    // Click the create task button
    await taskListPage.clickCreateTask();
    
    // Try to save without filling in the name
    await page.click('div[role="dialog"] button:has-text("Save")');
    
    // Check that an error message is displayed
    await expect(page.locator('div[role="dialog"] p:has-text("required")')).toBeVisible();
    
    // Fill in the name and try again
    const newTaskName = generateTaskName();
    await page.fill('input[name="name"]', newTaskName);
    
    // Save the task
    await page.click('div[role="dialog"] button:has-text("Save")');
    
    // Check that the task was created
    await expect(page.locator(`table tbody tr:has-text("${newTaskName}")`)).toBeVisible();
    
    // Add the new task to our list for cleanup
    const newTaskElement = await page.locator(`table tbody tr:has-text("${newTaskName}")`);
    const newTaskId = await newTaskElement.getAttribute('data-task-id');
    if (newTaskId) {
      testTasks.push({ _id: newTaskId, name: newTaskName });
    }
  });

  test('should show empty state when no tasks match filters', async () => {
    // Open the filter dialog
    await page.click('button:has-text("Filter")');
    await page.waitForSelector('div[role="presentation"]');
    
    // Set a filter that won't match any tasks
    await page.click('input[name="taskType"]');
    await page.click('li:has-text("Venue")');
    
    await page.click('input[name="status"]');
    await page.click('li:has-text("Delayed")');
    
    // Apply the filter
    await page.click('button:has-text("Apply")');
    
    // Wait for the filter to be applied
    await page.waitForLoadState('networkidle');
    
    // Check if the empty state message is displayed
    const isEmptyState = await taskListPage.isTaskListEmpty();
    if (!isEmptyState) {
      // If not empty, check that the tasks match the filter
      const taskRows = await page.$$('table tbody tr:nth-child(odd)');
      for (const row of taskRows) {
        const taskTypeCell = await row.$('td:nth-child(3)');
        const statusCell = await row.$('td:nth-child(4)');
        
        const taskType = await taskTypeCell.textContent();
        const status = await statusCell.textContent();
        
        expect(taskType.trim()).toBe('Venue');
        expect(status.trim()).toBe('Delayed');
      }
    }
    
    // Clear the filters
    await page.click('button:has-text("Filter")');
    await page.click('button:has-text("Clear")');
  });

  test('should update task status directly from the list', async () => {
    // Get the first task
    const taskToUpdate = testTasks[0];
    
    // Click on the status cell to open the status dropdown
    await page.click(`table tbody tr:has-text("${taskToUpdate.name}") td:nth-child(4)`);
    
    // Select a different status
    await page.click('li:has-text("Completed")');
    
    // Wait for the update to be applied
    await page.waitForLoadState('networkidle');
    
    // Check that the status was updated
    const statusCell = await page.locator(`table tbody tr:has-text("${taskToUpdate.name}") td:nth-child(4)`);
    await expect(statusCell).toHaveText('Completed');
  });
});
