const { test, expect } = require('@playwright/test');

test.describe('Task List Filtering and Sorting with Mock API', () => {
  // Mock the API responses
  test.beforeEach(async ({ page }) => {
    // Mock the login API
    await page.route('**/api/users/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          _id: 'mock-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'mock-token'
        })
      });
    });
    
    // Mock the events API
    await page.route('**/api/events', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-event-id',
            title: 'Mock Event',
            date: new Date().toISOString(),
            location: 'Mock Location',
            description: 'Mock event for testing',
            owner: 'mock-user-id'
          }
        ])
      });
    });
    
    // Mock the tasks API with a variety of tasks for filtering and sorting
    await page.route('**/api/tasks**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-task-id-1',
            name: 'A - First Task',
            taskType: 'Venue',
            details: 'This is the first task alphabetically',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [{
              _id: 'mock-user-id',
              name: 'Test User',
              email: '<EMAIL>'
            }],
            startTime: new Date().toISOString(),
            duration: '01:30:00',
            softDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            _id: 'mock-task-id-2',
            name: 'B - Second Task',
            taskType: 'Catering',
            details: 'This is the second task alphabetically',
            status: 'In Progress',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '02:00:00',
            softDeadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            _id: 'mock-task-id-3',
            name: 'C - Third Task',
            taskType: 'Entertainment',
            details: 'This is the third task alphabetically',
            status: 'Completed',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '03:00:00',
            softDeadline: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            _id: 'mock-task-id-4',
            name: 'D - Fourth Task',
            taskType: 'Other',
            details: 'This is the fourth task alphabetically',
            status: 'Delayed',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '04:00:00',
            softDeadline: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            _id: 'mock-task-id-5',
            name: 'E - Fifth Task',
            taskType: 'Venue',
            details: 'This is the fifth task alphabetically',
            status: 'Cancelled',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '05:00:00',
            softDeadline: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000).toISOString()
          }
        ])
      });
    });
  });
  
  // Helper function to login and navigate to tasks page
  const loginAndNavigateToTasks = async (page) => {
    // Go to login page
    await page.goto('/login');
    
    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for navigation to events page
    await page.waitForURL('**/events');
    
    // Navigate to tasks page
    await page.goto('/tasks');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Click on the List view tab if it exists
    try {
      await page.click('button:has-text("List")');
    } catch (error) {
      console.log('List tab not found or not clickable');
    }
  };
  
  test('should display all tasks initially', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Check that all tasks are displayed
    await expect(page.locator('text=A - First Task')).toBeVisible();
    await expect(page.locator('text=B - Second Task')).toBeVisible();
    await expect(page.locator('text=C - Third Task')).toBeVisible();
    await expect(page.locator('text=D - Fourth Task')).toBeVisible();
    await expect(page.locator('text=E - Fifth Task')).toBeVisible();
    
    // Take a screenshot
    await page.screenshot({ path: 'task-list-all-mock.png' });
  });
  
  test('should change rows per page', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Open the rows per page dropdown
    await page.click('div[class*="MuiTablePagination"] select');
    
    // Select 5 rows per page
    await page.selectOption('div[class*="MuiTablePagination"] select', '5');
    
    // Check that all tasks are still displayed (since we have exactly 5 tasks)
    await expect(page.locator('text=A - First Task')).toBeVisible();
    await expect(page.locator('text=E - Fifth Task')).toBeVisible();
    
    // Take a screenshot
    await page.screenshot({ path: 'task-list-pagination-mock.png' });
  });
  
  test('should sort tasks by name', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Click on the Name column header to sort
    await page.click('table thead tr th:nth-child(2)');
    
    // Check that tasks are sorted alphabetically
    const taskNames = await page.$$eval('table tbody tr:nth-child(odd) td:nth-child(2)', 
      elements => elements.map(el => el.textContent.trim()));
    
    // Verify that the tasks are sorted alphabetically
    const sortedNames = [...taskNames].sort();
    expect(taskNames).toEqual(sortedNames);
    
    // Take a screenshot
    await page.screenshot({ path: 'task-list-sorted-mock.png' });
  });
  
  test('should filter tasks by status', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // If there's a filter button, click it
    try {
      await page.click('button:has-text("Filter")');
      
      // Wait for filter dialog to appear
      await page.waitForSelector('div[role="dialog"]');
      
      // Select 'Completed' status
      await page.selectOption('select[name="status"]', 'Completed');
      
      // Apply filter
      await page.click('div[role="dialog"] button:has-text("Apply")');
      
      // Wait for the table to update
      await page.waitForLoadState('networkidle');
      
      // Check that only completed tasks are displayed
      await expect(page.locator('text=C - Third Task')).toBeVisible();
      
      // Take a screenshot
      await page.screenshot({ path: 'task-list-filtered-mock.png' });
    } catch (error) {
      console.log('Filter button not found or not implemented yet');
      test.skip();
    }
  });
});
