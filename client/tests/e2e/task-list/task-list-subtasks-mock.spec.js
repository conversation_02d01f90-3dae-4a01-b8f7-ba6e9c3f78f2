const { test, expect } = require('@playwright/test');

test.describe('Task List Subtask Functionality with Mock API', () => {
  // Mock the API responses
  test.beforeEach(async ({ page }) => {
    // Mock the login API
    await page.route('**/api/users/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          _id: 'mock-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'mock-token'
        })
      });
    });
    
    // Mock the events API
    await page.route('**/api/events', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-event-id',
            title: 'Mock Event',
            date: new Date().toISOString(),
            location: 'Mock Location',
            description: 'Mock event for testing',
            owner: 'mock-user-id'
          }
        ])
      });
    });
    
    // Mock the tasks API with parent tasks and subtasks
    await page.route('**/api/tasks**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-parent-task-1',
            name: 'Parent Task 1',
            taskType: 'Planning',
            details: 'This is a parent task with subtasks',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [],
            subtasks: ['mock-subtask-1', 'mock-subtask-2']
          },
          {
            _id: 'mock-subtask-1',
            name: 'Subtask 1',
            taskType: 'Other',
            details: 'This is the first subtask',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [],
            parentTask: 'mock-parent-task-1'
          },
          {
            _id: 'mock-subtask-2',
            name: 'Subtask 2',
            taskType: 'Other',
            details: 'This is the second subtask',
            status: 'Completed',
            event: 'mock-event-id',
            assignees: [],
            parentTask: 'mock-parent-task-1'
          },
          {
            _id: 'mock-parent-task-2',
            name: 'Parent Task 2',
            taskType: 'Venue',
            details: 'This is another parent task with subtasks',
            status: 'In Progress',
            event: 'mock-event-id',
            assignees: [],
            subtasks: ['mock-subtask-3']
          },
          {
            _id: 'mock-subtask-3',
            name: 'Subtask 3',
            taskType: 'Other',
            details: 'This is a subtask of the second parent',
            status: 'In Progress',
            event: 'mock-event-id',
            assignees: [],
            parentTask: 'mock-parent-task-2'
          }
        ])
      });
    });
    
    // Mock the task creation API for subtasks
    await page.route('**/api/tasks', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            _id: 'mock-new-subtask',
            name: 'New Subtask',
            taskType: 'Other',
            details: 'This is a new subtask created via the form',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [],
            parentTask: 'mock-parent-task-1'
          })
        });
      }
    });
    
    // Mock the task update API
    await page.route('**/api/tasks/mock-subtask-1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            _id: 'mock-subtask-1',
            name: 'Updated Subtask 1',
            taskType: 'Other',
            details: 'This subtask has been updated',
            status: 'In Progress',
            event: 'mock-event-id',
            assignees: [],
            parentTask: 'mock-parent-task-1'
          })
        });
      }
    });
  });
  
  // Helper function to login and navigate to tasks page
  const loginAndNavigateToTasks = async (page) => {
    // Go to login page
    await page.goto('/login');
    
    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for navigation to events page
    await page.waitForURL('**/events');
    
    // Navigate to tasks page
    await page.goto('/tasks');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Click on the List view tab if it exists
    try {
      await page.click('button:has-text("List")');
    } catch (error) {
      console.log('List tab not found or not clickable');
    }
  };
  
  test('should display parent tasks in the list', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Check that parent tasks are displayed
    await expect(page.locator('text=Parent Task 1')).toBeVisible();
    await expect(page.locator('text=Parent Task 2')).toBeVisible();
    
    // Take a screenshot
    await page.screenshot({ path: 'task-list-parents-mock.png' });
  });
  
  test('should expand parent task to show subtasks', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Click the expand button for the first parent task
    await page.click('table tbody tr:has-text("Parent Task 1") td:first-child button');
    
    // Check that the subtasks section is displayed
    await expect(page.locator('text=Subtasks')).toBeVisible();
    
    // Check that the subtasks are displayed
    await expect(page.locator('text=Subtask 1')).toBeVisible();
    await expect(page.locator('text=Subtask 2')).toBeVisible();
    
    // Take a screenshot
    await page.screenshot({ path: 'task-subtasks-expanded-mock.png' });
  });
  
  test('should edit a subtask from the parent task details', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Click the expand button for the first parent task
    await page.click('table tbody tr:has-text("Parent Task 1") td:first-child button');
    
    // Wait for the subtasks to be displayed
    await page.waitForSelector('text=Subtask 1');
    
    // Click the edit button for the first subtask
    await page.click('div:has-text("Subtask 1") button:has(svg[data-testid="EditIcon"])');
    
    // Wait for the task dialog to appear
    await page.waitForSelector('div[role="dialog"]');
    
    // Update the subtask name and status
    await page.fill('input[name="name"]', 'Updated Subtask 1');
    await page.fill('textarea[name="details"]', 'This subtask has been updated');
    await page.selectOption('select[name="status"]', 'In Progress');
    
    // Save the subtask
    await page.click('div[role="dialog"] button:has-text("Save")');
    
    // Wait for the snackbar to appear
    await page.waitForSelector('div[role="alert"]');
    
    // Take a screenshot
    await page.screenshot({ path: 'task-subtask-edit-mock.png' });
  });
  
  test('should create a new subtask from the parent task details', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Click the expand button for the first parent task
    await page.click('table tbody tr:has-text("Parent Task 1") td:first-child button');
    
    // Wait for the subtasks to be displayed
    await page.waitForSelector('text=Subtasks');
    
    // Click the create task button
    await page.click('button:has-text("Create Task")');
    
    // Wait for the task dialog to appear
    await page.waitForSelector('div[role="dialog"]');
    
    // Fill in the subtask form
    await page.fill('input[name="name"]', 'New Subtask');
    await page.selectOption('select[name="taskType"]', 'Other');
    await page.fill('textarea[name="details"]', 'This is a new subtask created via the form');
    
    // Select the parent task
    await page.selectOption('select[name="parentTask"]', 'mock-parent-task-1');
    
    // Save the subtask
    await page.click('div[role="dialog"] button:has-text("Save")');
    
    // Wait for the snackbar to appear
    await page.waitForSelector('div[role="alert"]');
    
    // Take a screenshot
    await page.screenshot({ path: 'task-subtask-create-mock.png' });
  });
});
