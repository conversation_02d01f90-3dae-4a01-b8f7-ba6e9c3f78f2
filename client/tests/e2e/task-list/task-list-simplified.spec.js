const { test, expect } = require('@playwright/test');

test.describe('Task List Simplified Test', () => {
  test('should navigate to tasks page after login', async ({ page }) => {
    // Go to login page
    await page.goto('/login');
    
    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for navigation to events page
    await page.waitForURL('**/events');
    
    // Check that we're on the events page
    expect(page.url()).toContain('/events');
    
    // Navigate to tasks page
    await page.click('a[href*="/tasks"]');
    
    // Wait for navigation to tasks page
    await page.waitForURL('**/tasks');
    
    // Check that we're on the tasks page
    expect(page.url()).toContain('/tasks');
    
    // Check for the task list view
    await page.click('button:has-text("List")');
    
    // Wait for the task list to be visible
    await expect(page.locator('table')).toBeVisible();
  });
});
