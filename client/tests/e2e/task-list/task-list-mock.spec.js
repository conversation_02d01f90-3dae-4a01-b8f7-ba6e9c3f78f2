const { test, expect } = require('@playwright/test');

test.describe('Task List Mock Tests', () => {
  // Mock the API responses
  test.beforeEach(async ({ page }) => {
    // Mock the login API
    await page.route('**/api/users/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          _id: 'mock-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'mock-token'
        })
      });
    });

    // Mock the events API
    await page.route('**/api/events', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-event-id',
            title: 'Mock Event',
            date: new Date().toISOString(),
            location: 'Mock Location',
            description: 'Mock event for testing',
            owner: 'mock-user-id'
          }
        ])
      });
    });

    // Mock the tasks API
    await page.route('**/api/tasks**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-task-id-1',
            name: 'Mock Task 1',
            taskType: 'Other',
            details: 'This is a mock task for testing',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '01:30:00',
            softDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            cost: {
              amount: 100,
              currency: 'USD',
              isPaid: false
            }
          },
          {
            _id: 'mock-task-id-2',
            name: 'Mock Task 2',
            taskType: 'Venue',
            details: 'This is another mock task for testing',
            status: 'In Progress',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '02:00:00',
            softDeadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            _id: 'mock-task-id-3',
            name: 'Mock Task with Subtasks',
            taskType: 'Planning',
            details: 'This is a parent task with subtasks',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [],
            subtasks: ['mock-subtask-id-1', 'mock-subtask-id-2']
          },
          {
            _id: 'mock-subtask-id-1',
            name: 'Mock Subtask 1',
            taskType: 'Other',
            details: 'This is a subtask',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [],
            parentTask: 'mock-task-id-3'
          },
          {
            _id: 'mock-subtask-id-2',
            name: 'Mock Subtask 2',
            taskType: 'Other',
            details: 'This is another subtask',
            status: 'Completed',
            event: 'mock-event-id',
            assignees: [],
            parentTask: 'mock-task-id-3'
          }
        ])
      });
    });
  });

  // Helper function to login and navigate to tasks page
  const loginAndNavigateToTasks = async (page) => {
    // Go to login page
    await page.goto('/login');

    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for navigation to events page
    await page.waitForURL('**/events');

    // Navigate to tasks page
    await page.goto('/tasks');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Click on the List view tab if it exists
    try {
      await page.click('button:has-text("List")');
    } catch (error) {
      console.log('List tab not found or not clickable');
    }
  };

  test('should login and display task list', async ({ page }) => {
    await loginAndNavigateToTasks(page);

    // Check if the task table is visible
    const isTableVisible = await page.isVisible('table');
    expect(isTableVisible).toBeTruthy();

    // Check that the mock tasks are displayed
    await expect(page.locator('text=Mock Task 1')).toBeVisible();
    await expect(page.locator('text=Mock Task 2')).toBeVisible();

    // Take a screenshot
    await page.screenshot({ path: 'task-list-mock.png' });
  });

  test('should expand task to show details', async ({ page }) => {
    await loginAndNavigateToTasks(page);

    // Click the expand button for the first task
    await page.click('table tbody tr:has-text("Mock Task 1") td:first-child button');

    // Check that the task details are displayed
    await expect(page.locator('text=This is a mock task for testing')).toBeVisible();

    // Take a screenshot
    await page.screenshot({ path: 'task-expanded-mock.png' });
  });

  test('should show task with subtasks', async ({ page }) => {
    await loginAndNavigateToTasks(page);

    // Check that the parent task is displayed
    await expect(page.locator('text=Mock Task with Subtasks')).toBeVisible();

    // Click the expand button for the parent task
    await page.click('table tbody tr:has-text("Mock Task with Subtasks") td:first-child button');

    // Check that the subtasks section is displayed
    await expect(page.locator('text=Subtasks')).toBeVisible();

    // Check that the subtasks are displayed
    await expect(page.locator('text=Mock Subtask 1')).toBeVisible();
    await expect(page.locator('text=Mock Subtask 2')).toBeVisible();

    // Take a screenshot
    await page.screenshot({ path: 'task-subtasks-mock.png' });
  });

  test('should have pagination controls', async ({ page }) => {
    await loginAndNavigateToTasks(page);

    // Check that pagination controls are displayed
    await expect(page.locator('div[class*="MuiTablePagination"]')).toBeVisible();

    // Take a screenshot
    await page.screenshot({ path: 'task-pagination-mock.png' });
  });
});
