const { test, expect } = require('@playwright/test');

test.describe('Task List Tests', () => {
  // Test the task list page UI components
  test('should render task list page components', async ({ page }) => {
    // Go directly to the tasks page
    await page.goto('/tasks');
    
    // Check that the page has loaded
    await page.waitForLoadState('domcontentloaded');
    
    // Check for basic UI elements
    await expect(page.locator('header')).toBeVisible();
    await expect(page.locator('#root')).toBeVisible();
    
    // Check for task-related UI elements if they exist
    const hasTaskUI = await page.isVisible('button:has-text("Create Task")') || 
                      await page.isVisible('button:has-text("List")') ||
                      await page.isVisible('table');
    
    console.log('Has task UI elements:', hasTaskUI);
    
    // Take a screenshot for visual verification
    await page.screenshot({ path: 'task-list-page.png' });
  });
  
  // Test the task creation form UI
  test('should render task creation form', async ({ page }) => {
    // Go to the tasks page
    await page.goto('/tasks');
    
    // Check if the create task button exists
    const hasCreateButton = await page.isVisible('button:has-text("Create Task")');
    
    if (hasCreateButton) {
      // Click the create task button
      await page.click('button:has-text("Create Task")');
      
      // Check if the task form dialog appears
      const hasTaskForm = await page.isVisible('div[role="dialog"]');
      
      if (hasTaskForm) {
        // Check for form elements
        await expect(page.locator('input[name="name"]')).toBeVisible();
        
        // Take a screenshot of the form
        await page.screenshot({ path: 'task-creation-form.png' });
        
        // Close the form
        await page.click('button:has-text("Cancel")');
      } else {
        console.log('Task form dialog not found');
      }
    } else {
      console.log('Create Task button not found');
    }
  });
  
  // Test the task list view UI
  test('should render task list view', async ({ page }) => {
    // Go to the tasks page
    await page.goto('/tasks');
    
    // Check if the list view tab exists
    const hasListTab = await page.isVisible('button:has-text("List")');
    
    if (hasListTab) {
      // Click the list view tab
      await page.click('button:has-text("List")');
      
      // Check if the task table is visible
      const hasTaskTable = await page.isVisible('table');
      
      if (hasTaskTable) {
        // Check for table headers
        const headers = await page.$$eval('table thead th', 
          elements => elements.map(el => el.textContent));
        
        console.log('Table headers:', headers);
        
        // Take a screenshot of the table
        await page.screenshot({ path: 'task-list-table.png' });
      } else {
        // Check for empty state message
        const hasEmptyState = await page.isVisible('div:has-text("No tasks")');
        
        if (hasEmptyState) {
          console.log('Empty state message found');
          await page.screenshot({ path: 'task-list-empty.png' });
        } else {
          console.log('Neither task table nor empty state message found');
        }
      }
    } else {
      console.log('List view tab not found');
    }
  });
});
