const { test, expect } = require('@playwright/test');

test.describe('Login Debug', () => {
  test('should debug login process', async ({ page }) => {
    // Navigate to the login page
    await page.goto('/login');
    
    // Check that the login form is displayed
    await expect(page.locator('form')).toBeVisible();
    
    // Enable console logging
    page.on('console', msg => console.log(`BROWSER CONSOLE: ${msg.type()}: ${msg.text()}`));
    
    // Enable request logging
    page.on('request', request => console.log(`REQUEST: ${request.method()} ${request.url()}`));
    
    // Enable response logging
    page.on('response', response => console.log(`RESPONSE: ${response.status()} ${response.url()}`));
    
    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait a bit to see what happens
    await page.waitForTimeout(5000);
    
    // Log the current URL
    console.log('Current URL after login attempt:', page.url());
    
    // Check if there's an error message
    const errorMessage = await page.locator('div[role="alert"]').textContent().catch(() => null);
    if (errorMessage) {
      console.log('Error message:', errorMessage);
    }
    
    // Try to create a test account
    console.log('Attempting to create a test account...');
    await page.goto('/signup');
    
    // Fill in signup form
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.fill('input[name="confirmPassword"]', 'password123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait a bit to see what happens
    await page.waitForTimeout(5000);
    
    // Log the current URL
    console.log('Current URL after signup attempt:', page.url());
    
    // Check if there's an error message
    const signupErrorMessage = await page.locator('div[role="alert"]').textContent().catch(() => null);
    if (signupErrorMessage) {
      console.log('Signup error message:', signupErrorMessage);
    }
  });
});
