const { test, expect } = require('@playwright/test');

test.describe('Task List UI Tests', () => {
  test('should render task list UI components', async ({ page }) => {
    // Go directly to the tasks page
    await page.goto('/tasks');
    
    // Check that the page has loaded
    await page.waitForLoadState('domcontentloaded');
    
    // Check for basic UI elements that should be present regardless of login state
    await expect(page.locator('header')).toBeVisible();
    
    // Check for the presence of the app container
    await expect(page.locator('#root')).toBeVisible();
    
    // Log the page content for debugging
    const pageContent = await page.content();
    console.log('Page content length:', pageContent.length);
    
    // Take a screenshot for visual verification
    await page.screenshot({ path: 'task-list-ui.png' });
    
    console.log('Test completed successfully');
  });
});
