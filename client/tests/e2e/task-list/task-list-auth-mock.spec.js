const { test, expect } = require('@playwright/test');

test.describe('Task List Auth Mock Tests', () => {
  // Set up authentication before each test
  test.beforeEach(async ({ page }) => {
    // Go to the login page
    await page.goto('/login');

    // Inject a token directly into localStorage to simulate being logged in
    await page.evaluate(() => {
      localStorage.setItem('token', 'mock-token');
      localStorage.setItem('user', JSON.stringify({
        _id: 'mock-user-id',
        name: 'Test User',
        email: '<EMAIL>'
      }));
    });

    // Mock the events API
    await page.route('**/api/events', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-event-id',
            title: 'Mock Event',
            date: new Date().toISOString(),
            location: 'Mock Location',
            description: 'Mock event for testing',
            owner: 'mock-user-id'
          }
        ])
      });
    });

    // Mock the tasks API
    await page.route('**/api/tasks**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-task-id-1',
            name: 'Mock Task 1',
            taskType: 'Other',
            details: 'This is a mock task for testing',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: []
          },
          {
            _id: 'mock-task-id-2',
            name: 'Mock Task 2',
            taskType: 'Venue',
            details: 'This is another mock task for testing',
            status: 'In Progress',
            event: 'mock-event-id',
            assignees: []
          }
        ])
      });
    });
  });

  test('should redirect to login page when accessing tasks with invalid auth', async ({ page }) => {
    // Navigate to tasks page
    await page.goto('/tasks');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Check that we're redirected to login
    await expect(page.url()).toContain('/login');

    // Take a screenshot
    await page.screenshot({ path: 'tasks-redirect-to-login.png' });
  });

  test('should redirect to login page when accessing events with invalid auth', async ({ page }) => {
    // Navigate to events page
    await page.goto('/events');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Check that we're redirected to login
    await expect(page.url()).toContain('/login');

    // Take a screenshot
    await page.screenshot({ path: 'events-redirect-to-login.png' });
  });
});
