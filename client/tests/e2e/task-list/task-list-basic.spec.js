const { test, expect } = require('@playwright/test');
const { login, setupTestAccount, selectEvent, navigateToTaskList } = require('../utils/auth');
const { createTestEvent, createTestTask, createMultipleTestTasks, cleanupTestData, generateTaskName } = require('../utils/test-data');
const { TaskListPage } = require('../utils/task-list-page');

test.describe('Task List Basic Functionality', () => {
  let page;
  let taskListPage;
  let testEvent;
  let testTasks = [];
  let authToken;

  test.beforeAll(async ({ browser, request }) => {
    // Create a context for API requests
    const context = await browser.newContext();
    page = await context.newPage();
    
    // Setup test account and login
    await setupTestAccount(page);
    
    // Get auth token from localStorage
    authToken = await page.evaluate(() => localStorage.getItem('token'));
    
    // Create test event
    testEvent = await createTestEvent(request, authToken);
    
    // Create test tasks
    testTasks = await createMultipleTestTasks(request, authToken, testEvent._id, 10);
  });

  test.beforeEach(async () => {
    // Navigate to task list for the test event
    await page.goto('/events');
    await selectEvent(page, testEvent.title);
    await navigateToTaskList(page);
    
    // Initialize page object
    taskListPage = new TaskListPage(page);
  });

  test.afterAll(async ({ request }) => {
    // Clean up test data
    const taskIds = testTasks.map(task => task._id);
    await cleanupTestData(request, authToken, [testEvent._id], taskIds);
  });

  test('should display task list with correct number of tasks', async () => {
    // Check that the task list is displayed
    await expect(page.locator('table')).toBeVisible();
    
    // Check that the correct number of tasks are displayed
    const taskCount = await taskListPage.getTaskCount();
    expect(taskCount).toBeGreaterThanOrEqual(Math.min(10, testTasks.length));
  });

  test('should display task details when expanded', async () => {
    // Get the first task
    const firstTask = testTasks[0];
    
    // Expand the task
    await taskListPage.expandTask(firstTask.name);
    
    // Check that the task details are displayed
    const detailsText = await taskListPage.getTaskDetails(firstTask.name);
    expect(detailsText).toContain(firstTask.details);
  });

  test('should create a new task', async () => {
    // Generate a unique task name
    const newTaskName = generateTaskName();
    
    // Create a new task
    await taskListPage.createTask({
      name: newTaskName,
      taskType: 'Other',
      status: 'Not Started',
      details: 'This is a test task created by Playwright'
    });
    
    // Check that the task was created
    await expect(page.locator(`table tbody tr:has-text("${newTaskName}")`)).toBeVisible();
    
    // Add the new task to the list for cleanup
    const newTaskElement = await page.locator(`table tbody tr:has-text("${newTaskName}")`);
    const newTaskId = await newTaskElement.getAttribute('data-task-id');
    if (newTaskId) {
      testTasks.push({ _id: newTaskId, name: newTaskName });
    }
  });

  test('should edit an existing task', async () => {
    // Get the first task
    const taskToEdit = testTasks[0];
    const updatedTaskName = `${taskToEdit.name} (Edited)`;
    
    // Edit the task
    await taskListPage.editTask(taskToEdit.name, {
      name: updatedTaskName,
      details: 'This task has been edited by Playwright'
    });
    
    // Check that the task was updated
    await expect(page.locator(`table tbody tr:has-text("${updatedTaskName}")`)).toBeVisible();
    
    // Update the task in our list
    taskToEdit.name = updatedTaskName;
  });

  test('should delete a task', async () => {
    // Get the last task
    const taskToDelete = testTasks[testTasks.length - 1];
    
    // Delete the task
    await taskListPage.deleteTask(taskToDelete.name);
    
    // Check that the task was deleted
    await expect(page.locator(`table tbody tr:has-text("${taskToDelete.name}")`)).not.toBeVisible();
    
    // Remove the task from our list
    testTasks.pop();
  });

  test('should paginate tasks correctly', async () => {
    // Change rows per page to 5
    await taskListPage.changeRowsPerPage(5);
    
    // Check that the correct number of tasks are displayed
    const taskCount = await taskListPage.getTaskCount();
    expect(taskCount).toBeLessThanOrEqual(5);
    
    // Go to the next page
    await taskListPage.goToNextPage();
    
    // Check that different tasks are displayed
    const firstPageTasks = await page.$$eval('table tbody tr:nth-child(odd) td:nth-child(2)', 
      elements => elements.map(el => el.textContent));
    
    // Go back to the first page
    await taskListPage.goToPreviousPage();
    
    // Check that the original tasks are displayed
    const returnedToFirstPageTasks = await page.$$eval('table tbody tr:nth-child(odd) td:nth-child(2)', 
      elements => elements.map(el => el.textContent));
    
    expect(firstPageTasks).toEqual(returnedToFirstPageTasks);
  });
});
