const { test, expect } = require('@playwright/test');
const { login, setupTestAccount, selectEvent, navigateToTaskList } = require('../utils/auth');
const { createTestEvent, createTestTask, createMultipleTestTasks, cleanupTestData } = require('../utils/test-data');
const { TaskListPage } = require('../utils/task-list-page');

test.describe('Task List Filtering and Sorting', () => {
  let page;
  let taskListPage;
  let testEvent;
  let testTasks = [];
  let authToken;

  test.beforeAll(async ({ browser, request }) => {
    // Create a context for API requests
    const context = await browser.newContext();
    page = await context.newPage();
    
    // Setup test account and login
    await setupTestAccount(page);
    
    // Get auth token from localStorage
    authToken = await page.evaluate(() => localStorage.getItem('token'));
    
    // Create test event
    testEvent = await createTestEvent(request, authToken);
    
    // Create test tasks with different statuses and types
    const taskTypes = ['Venue', 'Catering', 'Photography', 'Decoration', 'Entertainment', 'Logistics', 'Other'];
    const statuses = ['Not Started', 'In Progress', 'Completed', 'Delayed', 'Cancelled'];
    
    for (let i = 0; i < taskTypes.length; i++) {
      for (let j = 0; j < statuses.length; j++) {
        const task = await createTestTask(request, authToken, testEvent._id, {
          name: `${taskTypes[i]} Task - ${statuses[j]}`,
          taskType: taskTypes[i],
          status: statuses[j]
        });
        testTasks.push(task);
      }
    }
  });

  test.beforeEach(async () => {
    // Navigate to task list for the test event
    await page.goto('/events');
    await selectEvent(page, testEvent.title);
    await navigateToTaskList(page);
    
    // Initialize page object
    taskListPage = new TaskListPage(page);
  });

  test.afterAll(async ({ request }) => {
    // Clean up test data
    const taskIds = testTasks.map(task => task._id);
    await cleanupTestData(request, authToken, [testEvent._id], taskIds);
  });

  test('should filter tasks by type', async () => {
    // Open the task type filter
    await page.click('button:has-text("Filter")');
    await page.waitForSelector('div[role="presentation"]');
    
    // Select the Venue task type
    await page.click('input[name="taskType"]');
    await page.click('li:has-text("Venue")');
    
    // Apply the filter
    await page.click('button:has-text("Apply")');
    
    // Wait for the filter to be applied
    await page.waitForLoadState('networkidle');
    
    // Check that only Venue tasks are displayed
    const taskRows = await page.$$('table tbody tr:nth-child(odd)');
    for (const row of taskRows) {
      const taskTypeCell = await row.$('td:nth-child(3)');
      const taskType = await taskTypeCell.textContent();
      expect(taskType.trim()).toBe('Venue');
    }
  });

  test('should filter tasks by status', async () => {
    // Open the task status filter
    await page.click('button:has-text("Filter")');
    await page.waitForSelector('div[role="presentation"]');
    
    // Select the Completed status
    await page.click('input[name="status"]');
    await page.click('li:has-text("Completed")');
    
    // Apply the filter
    await page.click('button:has-text("Apply")');
    
    // Wait for the filter to be applied
    await page.waitForLoadState('networkidle');
    
    // Check that only Completed tasks are displayed
    const taskRows = await page.$$('table tbody tr:nth-child(odd)');
    for (const row of taskRows) {
      const statusCell = await row.$('td:nth-child(4)');
      const status = await statusCell.textContent();
      expect(status.trim()).toBe('Completed');
    }
  });

  test('should filter tasks by both type and status', async () => {
    // Open the filter dialog
    await page.click('button:has-text("Filter")');
    await page.waitForSelector('div[role="presentation"]');
    
    // Select the Venue task type
    await page.click('input[name="taskType"]');
    await page.click('li:has-text("Venue")');
    
    // Select the Completed status
    await page.click('input[name="status"]');
    await page.click('li:has-text("Completed")');
    
    // Apply the filter
    await page.click('button:has-text("Apply")');
    
    // Wait for the filter to be applied
    await page.waitForLoadState('networkidle');
    
    // Check that only Venue tasks with Completed status are displayed
    const taskRows = await page.$$('table tbody tr:nth-child(odd)');
    for (const row of taskRows) {
      const taskTypeCell = await row.$('td:nth-child(3)');
      const statusCell = await row.$('td:nth-child(4)');
      
      const taskType = await taskTypeCell.textContent();
      const status = await statusCell.textContent();
      
      expect(taskType.trim()).toBe('Venue');
      expect(status.trim()).toBe('Completed');
    }
  });

  test('should clear filters', async () => {
    // Open the filter dialog
    await page.click('button:has-text("Filter")');
    await page.waitForSelector('div[role="presentation"]');
    
    // Select the Venue task type
    await page.click('input[name="taskType"]');
    await page.click('li:has-text("Venue")');
    
    // Apply the filter
    await page.click('button:has-text("Apply")');
    
    // Wait for the filter to be applied
    await page.waitForLoadState('networkidle');
    
    // Open the filter dialog again
    await page.click('button:has-text("Filter")');
    
    // Clear the filters
    await page.click('button:has-text("Clear")');
    
    // Wait for the filters to be cleared
    await page.waitForLoadState('networkidle');
    
    // Check that all task types are displayed
    const taskTypes = await page.$$eval('table tbody tr:nth-child(odd) td:nth-child(3)', 
      elements => elements.map(el => el.textContent.trim()));
    
    // Verify that we have more than one task type
    const uniqueTaskTypes = new Set(taskTypes);
    expect(uniqueTaskTypes.size).toBeGreaterThan(1);
  });

  test('should sort tasks by name', async () => {
    // Click on the Name column header to sort
    await page.click('table thead tr th:nth-child(2)');
    
    // Wait for the sort to be applied
    await page.waitForLoadState('networkidle');
    
    // Get the task names
    const taskNames = await page.$$eval('table tbody tr:nth-child(odd) td:nth-child(2)', 
      elements => elements.map(el => el.textContent.trim()));
    
    // Check that the tasks are sorted alphabetically
    const sortedTaskNames = [...taskNames].sort();
    expect(taskNames).toEqual(sortedTaskNames);
    
    // Click again to sort in reverse
    await page.click('table thead tr th:nth-child(2)');
    
    // Wait for the sort to be applied
    await page.waitForLoadState('networkidle');
    
    // Get the task names again
    const reversedTaskNames = await page.$$eval('table tbody tr:nth-child(odd) td:nth-child(2)', 
      elements => elements.map(el => el.textContent.trim()));
    
    // Check that the tasks are sorted in reverse alphabetical order
    const reverseSortedTaskNames = [...taskNames].sort().reverse();
    expect(reversedTaskNames).toEqual(reverseSortedTaskNames);
  });
});
