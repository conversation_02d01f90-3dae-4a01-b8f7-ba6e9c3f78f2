const { test, expect } = require('@playwright/test');

test.describe('Task List Login Mock Test', () => {
  // Mock the API responses
  test.beforeEach(async ({ page }) => {
    // Mock the login API
    await page.route('**/api/users/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          _id: 'mock-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'mock-token'
        })
      });
    });
    
    // Mock the events API
    await page.route('**/api/events', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-event-id',
            title: 'Mock Event',
            date: new Date().toISOString(),
            location: 'Mock Location',
            description: 'Mock event for testing',
            owner: 'mock-user-id'
          }
        ])
      });
    });
  });
  
  test('should login successfully', async ({ page }) => {
    // Go to login page
    await page.goto('/login');
    
    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for navigation to events page
    await page.waitForURL('**/events');
    
    // Check that we're on the events page
    await expect(page.url()).toContain('/events');
    
    // Take a screenshot
    await page.screenshot({ path: 'login-success-mock.png' });
  });
});
