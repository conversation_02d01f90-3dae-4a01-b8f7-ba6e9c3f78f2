const { test, expect } = require('@playwright/test');

test.describe('Minimal Test', () => {
  test('should load the login page', async ({ page }) => {
    // Navigate to the login page
    await page.goto('/login');
    
    // Check that the login form is displayed
    await expect(page.locator('form')).toBeVisible();
  });
  
  test('should attempt login', async ({ page }) => {
    // Navigate to the login page
    await page.goto('/login');
    
    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait a bit to see what happens
    await page.waitForTimeout(5000);
    
    // Log the current URL
    console.log('Current URL after login attempt:', page.url());
  });
});
