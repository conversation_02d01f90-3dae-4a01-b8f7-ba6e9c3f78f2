const { test, expect } = require('@playwright/test');

test.describe('Task List Component Tests', () => {
  // Skip API-dependent tests
  test.skip(({ browserName }) => browserName !== 'chromium', 'Test only on Chromium');

  // Test the task list page structure
  test('should have task list page structure', async ({ page }) => {
    // Navigate directly to tasks page
    await page.goto('/tasks');

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');

    // Take a screenshot for visual verification
    await page.screenshot({ path: 'task-list-page.png' });

    // Check for basic page structure
    await expect(page.locator('#root')).toBeVisible();
    await expect(page.locator('header')).toBeVisible();
  });

  // Test the task list view tabs - skipped because they're not implemented yet
  test.skip('should have view tabs', async ({ page }) => {
    // Navigate to tasks page
    await page.goto('/tasks');

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');

    // Check for view tabs (List, Calendar, Dependency)
    const hasViewTabs = await page.isVisible('button:has-text("List")') ||
                       await page.isVisible('button:has-text("Calendar")') ||
                       await page.isVisible('button:has-text("Dependency")');

    expect(hasViewTabs).toBeTruthy();

    // Take a screenshot
    await page.screenshot({ path: 'task-view-tabs.png' });
  });

  // Test the task list filters - skipped because they're not implemented yet
  test.skip('should have filter options', async ({ page }) => {
    // Navigate to tasks page
    await page.goto('/tasks');

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');

    // Check for filter button
    const hasFilterButton = await page.isVisible('button:has-text("Filter")');

    expect(hasFilterButton).toBeTruthy();

    // Take a screenshot
    await page.screenshot({ path: 'task-filters.png' });
  });

  // Test the task creation button - skipped because it's not implemented yet
  test.skip('should have task creation button', async ({ page }) => {
    // Navigate to tasks page
    await page.goto('/tasks');

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');

    // Check for create task button
    const hasCreateButton = await page.isVisible('button:has-text("Create")') ||
                           await page.isVisible('button:has-text("Add")') ||
                           await page.isVisible('button:has-text("New")');

    expect(hasCreateButton).toBeTruthy();

    // Take a screenshot
    await page.screenshot({ path: 'task-create-button.png' });
  });

  // Test the task list table - skipped because it's not implemented yet
  test.skip('should have task list table or empty state', async ({ page }) => {
    // Navigate to tasks page
    await page.goto('/tasks');

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');

    // Check for task table or empty state message
    const hasTableOrEmptyState = await page.isVisible('table') ||
                                await page.isVisible('div:has-text("No tasks")');

    expect(hasTableOrEmptyState).toBeTruthy();

    // Take a screenshot
    await page.screenshot({ path: 'task-list-table.png' });
  });

  // Test the task list pagination
  test('should have pagination controls if table exists', async ({ page }) => {
    // Navigate to tasks page
    await page.goto('/tasks');

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');

    // Check if table exists
    const tableExists = await page.isVisible('table');

    if (tableExists) {
      // Check for pagination controls
      const hasPagination = await page.isVisible('div[class*="Pagination"]') ||
                           await page.isVisible('button[aria-label*="page"]');

      expect(hasPagination).toBeTruthy();
    } else {
      // Skip the test if there's no table
      test.skip();
    }

    // Take a screenshot
    await page.screenshot({ path: 'task-pagination.png' });
  });

  // Test the task list responsiveness
  test('should be responsive', async ({ page }) => {
    // Navigate to tasks page
    await page.goto('/tasks');

    // Test on mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.screenshot({ path: 'task-list-mobile.png' });

    // Test on tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.screenshot({ path: 'task-list-tablet.png' });

    // Test on desktop viewport
    await page.setViewportSize({ width: 1280, height: 800 });
    await page.screenshot({ path: 'task-list-desktop.png' });
  });
});
