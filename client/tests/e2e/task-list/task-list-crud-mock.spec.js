const { test, expect } = require('@playwright/test');

test.describe('Task List CRUD Operations with Mock API', () => {
  // Mock the API responses
  test.beforeEach(async ({ page }) => {
    // Mock the login API
    await page.route('**/api/users/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          _id: 'mock-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'mock-token'
        })
      });
    });
    
    // Mock the events API
    await page.route('**/api/events', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-event-id',
            title: 'Mock Event',
            date: new Date().toISOString(),
            location: 'Mock Location',
            description: 'Mock event for testing',
            owner: 'mock-user-id'
          }
        ])
      });
    });
    
    // Mock the tasks API
    await page.route('**/api/tasks**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-task-id-1',
            name: 'Mock Task 1',
            taskType: 'Other',
            details: 'This is a mock task for testing',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '01:30:00',
            softDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          }
        ])
      });
    });
    
    // Mock the task creation API
    await page.route('**/api/tasks', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            _id: 'mock-new-task-id',
            name: 'New Task',
            taskType: 'Other',
            details: 'This is a new task created via the form',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '01:00:00',
            softDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          })
        });
      }
    });
    
    // Mock the task update API
    await page.route('**/api/tasks/mock-task-id-1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            _id: 'mock-task-id-1',
            name: 'Updated Task 1',
            taskType: 'Other',
            details: 'This task has been updated',
            status: 'In Progress',
            event: 'mock-event-id',
            assignees: [],
            startTime: new Date().toISOString(),
            duration: '01:30:00',
            softDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          })
        });
      }
    });
    
    // Mock the task deletion API
    await page.route('**/api/tasks/mock-task-id-1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            message: 'Task deleted successfully'
          })
        });
      }
    });
  });
  
  // Helper function to login and navigate to tasks page
  const loginAndNavigateToTasks = async (page) => {
    // Go to login page
    await page.goto('/login');
    
    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for navigation to events page
    await page.waitForURL('**/events');
    
    // Navigate to tasks page
    await page.goto('/tasks');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Click on the List view tab if it exists
    try {
      await page.click('button:has-text("List")');
    } catch (error) {
      console.log('List tab not found or not clickable');
    }
  };
  
  test('should create a new task', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Click the create task button
    await page.click('button:has-text("Create Task")');
    
    // Wait for the task dialog to appear
    await page.waitForSelector('div[role="dialog"]');
    
    // Fill in the task form
    await page.fill('input[name="name"]', 'New Task');
    await page.selectOption('select[name="taskType"]', 'Other');
    await page.fill('textarea[name="details"]', 'This is a new task created via the form');
    
    // Save the task
    await page.click('div[role="dialog"] button:has-text("Save")');
    
    // Wait for the snackbar to appear
    await page.waitForSelector('div[role="alert"]');
    
    // Take a screenshot
    await page.screenshot({ path: 'task-create-mock.png' });
  });
  
  test('should edit an existing task', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Click the edit button for the first task
    await page.click('table tbody tr:has-text("Mock Task 1") td:last-child button:has(svg[data-testid="EditIcon"])');
    
    // Wait for the task dialog to appear
    await page.waitForSelector('div[role="dialog"]');
    
    // Update the task name and status
    await page.fill('input[name="name"]', 'Updated Task 1');
    await page.fill('textarea[name="details"]', 'This task has been updated');
    await page.selectOption('select[name="status"]', 'In Progress');
    
    // Save the task
    await page.click('div[role="dialog"] button:has-text("Save")');
    
    // Wait for the snackbar to appear
    await page.waitForSelector('div[role="alert"]');
    
    // Take a screenshot
    await page.screenshot({ path: 'task-edit-mock.png' });
  });
  
  test('should delete a task', async ({ page }) => {
    await loginAndNavigateToTasks(page);
    
    // Click the delete button for the first task
    await page.click('table tbody tr:has-text("Mock Task 1") td:last-child button:has(svg[data-testid="DeleteIcon"])');
    
    // Wait for the confirmation dialog to appear
    await page.waitForSelector('div[role="dialog"]:has-text("Delete Task")');
    
    // Confirm deletion
    await page.click('div[role="dialog"] button:has-text("Delete")');
    
    // Wait for the snackbar to appear
    await page.waitForSelector('div[role="alert"]');
    
    // Take a screenshot
    await page.screenshot({ path: 'task-delete-mock.png' });
  });
});
