const { test, expect } = require('@playwright/test');
const { login, setupTestAccount, selectEvent, navigateToTaskList } = require('../utils/auth');
const { createTestEvent, createTestTask, cleanupTestData, generateTaskName } = require('../utils/test-data');
const { TaskListPage } = require('../utils/task-list-page');

test.describe('Task List Subtask Functionality', () => {
  let page;
  let taskListPage;
  let testEvent;
  let parentTask;
  let subtasks = [];
  let authToken;

  test.beforeAll(async ({ browser, request }) => {
    // Create a context for API requests
    const context = await browser.newContext();
    page = await context.newPage();
    
    // Setup test account and login
    await setupTestAccount(page);
    
    // Get auth token from localStorage
    authToken = await page.evaluate(() => localStorage.getItem('token'));
    
    // Create test event
    testEvent = await createTestEvent(request, authToken);
    
    // Create parent task
    parentTask = await createTestTask(request, authToken, testEvent._id, {
      name: 'Parent Task for Subtask Tests',
      taskType: 'Venue',
      details: 'This is a parent task for subtask testing'
    });
    
    // Create subtasks
    for (let i = 0; i < 3; i++) {
      const subtask = await createTestTask(request, authToken, testEvent._id, {
        name: `Subtask ${i + 1} for ${parentTask.name}`,
        taskType: 'Other',
        details: `This is subtask ${i + 1} for testing`,
        parentTask: parentTask._id
      });
      subtasks.push(subtask);
    }
    
    // Update parent task with subtasks
    await request.put(`/api/tasks/${parentTask._id}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      data: {
        ...parentTask,
        subtasks: subtasks.map(task => task._id)
      }
    });
  });

  test.beforeEach(async () => {
    // Navigate to task list for the test event
    await page.goto('/events');
    await selectEvent(page, testEvent.title);
    await navigateToTaskList(page);
    
    // Initialize page object
    taskListPage = new TaskListPage(page);
  });

  test.afterAll(async ({ request }) => {
    // Clean up test data
    const taskIds = [parentTask._id, ...subtasks.map(task => task._id)];
    await cleanupTestData(request, authToken, [testEvent._id], taskIds);
  });

  test('should display parent task with subtask indicator', async () => {
    // Check that the parent task is displayed
    await expect(page.locator(`table tbody tr:has-text("${parentTask.name}")`)).toBeVisible();
    
    // Check that the subtask indicator is displayed
    await expect(page.locator(`table tbody tr:has-text("${parentTask.name}") td:nth-child(2) span:has-text("subtask")`)).toBeVisible();
  });

  test('should display subtasks when parent task is expanded', async () => {
    // Expand the parent task
    await taskListPage.expandTask(parentTask.name);
    
    // Check that the subtasks section is displayed
    const detailsText = await taskListPage.getTaskDetails(parentTask.name);
    expect(detailsText).toContain('Subtasks');
    
    // Check that all subtasks are displayed
    for (const subtask of subtasks) {
      expect(detailsText).toContain(subtask.name);
    }
  });

  test('should be able to edit a subtask from the parent task details', async () => {
    // Expand the parent task
    await taskListPage.expandTask(parentTask.name);
    
    // Click the edit button for the first subtask
    await page.click(`div:has-text("${subtasks[0].name}") button:has(svg[data-testid="EditIcon"])`);
    
    // Wait for the task dialog to appear
    await page.waitForSelector('div[role="dialog"]');
    
    // Update the subtask name
    const updatedSubtaskName = `${subtasks[0].name} (Edited)`;
    await page.fill('input[name="name"]', updatedSubtaskName);
    
    // Save the changes
    await page.click('div[role="dialog"] button:has-text("Save")');
    
    // Wait for the snackbar to appear
    await page.waitForSelector('div[role="alert"]');
    
    // Expand the parent task again to see the updated subtask
    await taskListPage.expandTask(parentTask.name);
    
    // Check that the subtask name was updated
    const detailsText = await taskListPage.getTaskDetails(parentTask.name);
    expect(detailsText).toContain(updatedSubtaskName);
    
    // Update the subtask in our list
    subtasks[0].name = updatedSubtaskName;
  });

  test('should be able to add a new subtask to a parent task', async () => {
    // Expand the parent task
    await taskListPage.expandTask(parentTask.name);
    
    // Click the "Add Subtask" button
    await page.click(`div:has-text("${parentTask.name}") button:has-text("Add Subtask")`);
    
    // Wait for the task dialog to appear
    await page.waitForSelector('div[role="dialog"]');
    
    // Fill in the subtask details
    const newSubtaskName = generateTaskName();
    await page.fill('input[name="name"]', newSubtaskName);
    await page.fill('textarea[name="details"]', 'This is a new subtask created by Playwright');
    
    // Make sure the parent task is selected
    await expect(page.locator('input[name="parentTask"]')).toHaveValue(parentTask._id);
    
    // Save the subtask
    await page.click('div[role="dialog"] button:has-text("Save")');
    
    // Wait for the snackbar to appear
    await page.waitForSelector('div[role="alert"]');
    
    // Expand the parent task again to see the new subtask
    await taskListPage.expandTask(parentTask.name);
    
    // Check that the new subtask is displayed
    const detailsText = await taskListPage.getTaskDetails(parentTask.name);
    expect(detailsText).toContain(newSubtaskName);
    
    // Add the new subtask to our list for cleanup
    // We need to get the ID from the API since we don't have it from the UI
    const response = await page.request.get(`/api/tasks?eventId=${testEvent._id}`);
    const tasks = await response.json();
    const newSubtask = tasks.find(task => task.name === newSubtaskName);
    if (newSubtask) {
      subtasks.push(newSubtask);
    }
  });

  test('should be able to remove a subtask from a parent task', async () => {
    // Expand the parent task
    await taskListPage.expandTask(parentTask.name);
    
    // Click the delete button for the last subtask
    const lastSubtask = subtasks[subtasks.length - 1];
    await page.click(`div:has-text("${lastSubtask.name}") button:has(svg[data-testid="DeleteIcon"])`);
    
    // Wait for the delete confirmation dialog
    await page.waitForSelector('div[role="dialog"]:has-text("Delete Task")');
    
    // Confirm the deletion
    await page.click('div[role="dialog"] button:has-text("Delete")');
    
    // Wait for the snackbar to appear
    await page.waitForSelector('div[role="alert"]');
    
    // Expand the parent task again to see that the subtask is gone
    await taskListPage.expandTask(parentTask.name);
    
    // Check that the subtask is no longer displayed
    const detailsText = await taskListPage.getTaskDetails(parentTask.name);
    expect(detailsText).not.toContain(lastSubtask.name);
    
    // Remove the subtask from our list
    subtasks.pop();
  });
});
