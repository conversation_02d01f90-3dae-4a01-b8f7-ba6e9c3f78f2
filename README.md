# Event Planner and Management System

A comprehensive web application for planning and managing events such as weddings, birthdays, corporate events, and more.

[**Development Guide**](./DEVELOPMENT.md) - Detailed instructions for developers

## Features

### Event Management
- Create and manage different types of events (Wedding, Birthday, Corporate, Anniversary, etc.)
- Set event details including title, date, venue, budget, and description
- View event details and progress

### Task Management
- Create custom tasks with detailed information:
  - Task name and type
  - Start time and location
  - Detailed description
  - Cost tracking (amount, currency, payment status)
  - Assignee management
  - Soft and hard deadlines with date and time
  - File attachments
  - Dependencies between tasks
- Select from predefined task templates based on event type
- View tasks in list format or as a dependency graph
- Track task status and completion

### Calendar Views
- Monthly calendar showing all scheduled tasks
- Daily view with detailed task information
- Ability to reschedule tasks and manage deadlines
- Assignee-specific views to track individual responsibilities

### Template Management
- Create and manage event templates with predefined tasks
- Apply templates when creating new events

## How to Use

1. **Create an Event**
   - Enter event details including type, date, venue, and budget
   - Optionally select a template to pre-populate tasks

2. **Manage Tasks**
   - Select from default tasks for your event type or create custom tasks
   - For each task, specify:
     - Task name and type
     - Start time and location
     - Detailed description
     - Cost information
     - Assignee
     - Soft and hard deadlines (with date and time)
     - File attachments (optional)
     - Dependencies on other tasks
   - View tasks that are missing dates or assignees

3. **Calendar Management**
   - Use the monthly calendar to get an overview of all tasks
   - Use the daily view to see detailed task information for specific days
   - Reschedule tasks as needed, with warnings if changes affect hard deadlines
   - View tasks by assignee to track individual responsibilities

## Technical Implementation

- Frontend: React with Material-UI
- Backend: Node.js with Express
- Database: MongoDB
- Authentication: JWT-based user authentication

## Quick Start

1. Clone the repository
2. Install dependencies:
   ```
   npm run install-all
   ```

3. Start the application with sample data:
   ```
   npm run dev:seed
   ```

4. Access the application at http://localhost:3000

## For Developers

If you're a developer looking to contribute or set up the project for development, please refer to our comprehensive [**Development Guide**](./DEVELOPMENT.md) which includes:

- Detailed setup instructions
- Development workflow
- Database management
- Available scripts and commands
- Troubleshooting tips

## Application Structure

- `/client` - React frontend
- `/server` - Express backend
- `/scripts` - Utility scripts

